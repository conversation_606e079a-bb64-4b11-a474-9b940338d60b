version: "3.2"

services:

  # psql:
  #   container_name: db
  #   image: "postgres:14.1-alpine"
  #   environment:
  #     - POSTGRES_DB=postgres
  #     - POSTGRES_USER=root
  #     - POSTGRES_PASSWORD=root

  backend:
    restart: always
    container_name: api
    command : bash -c "python manage.py migrate &&
                      python manage.py runserver 0.0.0.0:8000"
    env_file:
      - ./backend/.env
    build:
      context: ./backend/
      dockerfile: Dockerfile
    ports:
      - 8000:8000
    # depends_on:
    #   - psql
      
    # networks:
    #   - db-net

  frontend:
    restart: always
    command : npm start
    container_name: front
    build:
      context: ./frontend/
      dockerfile: Dockerfile
    ports:
      - 3000:3000
    # stdin_open: true
    # depends_on:
    #   - backend
    # networks:
    #   - db-net

# networks:
#   db-net:
#     driver: bridge