import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Message from "../components/Message";
import Loader from "../components/Loader";
import "./ProductScreen.css";
import { listProductBySlugDetails } from "../actions/productActions";
import getFormatedPrice from "../utils/getFormatedPrice";
import { addToCart } from "../actions/cartActions";
import { Link, useParams } from "react-router-dom";
import { listOrganizerBySlugDetails } from "../actions/organizerActions";
import uuid from "react-uuid";
import { baseURL, baseURLFiles } from "../axios";

import axios from "../axios";

const ProductScreen = ({ history }) => {
  const [qty, setQty] = useState(1);

  const [listTicketName, setListTicketName] = useState([""]);

  const [eventDate, setEventDate] = useState("");
  // const [isDisabaled, setIsDisabaled] = useState(false);
  const { organization, slug } = useParams();

  const [activeImage, setActiveImage] = useState(0);
  const [selectedVariation, setSelectedVariation] = useState("");

  const [customization, setCustomization] = useState("");

  const [isAvailable, setIsAvailable] = useState(false);
  const [isSoldOut, setIsSoldOut] = useState(false);

  const { organizer } = useSelector((state) => state.organizerDetails);

  const dispatch = useDispatch();

  const productDetails = useSelector((state) => state.productDetails);
  const { loading, error, product } = productDetails;
  const [total, setTotal] = useState(0);

  useEffect(() => {
    if (product) {
      setTotal(
        product?.variation?.name !== "" &&
          product?.variation?.attributes[0].price
      );

      if (
        product.organizer?.is_daily === false ||
        (product.organizer?.is_daily === true &&
          product.has_date === true &&
          product.event_date !== "" &&
          new Date(product.event_date).setHours(0, 0, 0, 0) >=
            new Date().setHours(0, 0, 0, 0))
      ) {
        setIsAvailable(true);
      }
      if (
        product.organizer?.is_daily === true &&
        product.has_date === true &&
        product.event_date !== "" &&
        new Date(product.event_date).setHours(0, 0, 0, 0) <
          new Date().setHours(0, 0, 0, 0)
      ) {
        setIsSoldOut(true);
      }
    }
  }, [product]);

  useEffect(() => {
    dispatch(listOrganizerBySlugDetails(organization));
  }, [dispatch, organization]);

  useEffect(() => {
    if (!product.slug || product.slug !== slug) {
      dispatch(listProductBySlugDetails(slug));
    }
  }, [dispatch, slug]);

  const addToCartHandler = () => {
    dispatch(
      addToCart(
        product._id,
        qty,
        selectedVariation,
        customization,
        product.customizationTitle ?? "",
        product.organizer.is_daily,
        product.has_date,
        product.organizer.is_daily && product.has_date === false
          ? eventDate
          : product.organizer.is_daily && product.has_date
          ? product.event_date
          : "",
        product.qte_tickets,
        product.organizer?.isticket_customization,
        product.organizer?.isticket_customization ? listTicketName : []
      )
    );
    history.push(`/cart/`);
  };

  const handleVariationChnage = (e) => {
    const selectedId = e.target.value;
    setSelectedVariation(selectedId);
    const selectedAttribute = product.variation.attributes.find(
      (att) => att.id === parseInt(selectedId)
    );
    setTotal(parseFloat(selectedAttribute.price));
  };

  // useEffect(() => {
  //   if (error){
  //     history.push('/')
  //   }
  // }, [error])

  // useEffect(() => {
  //   const t1 =
  //     !product.isImmediately &&
  //     product.hasNoEndDate &&
  //     new Date(product.eventDate) <= new Date();
  //   console.log(new Date(product.eventDate), new Date());
  //   if (product.countInStock <= 0 || t1) setIsDisabaled(true);
  // }, [product]);

  return loading ? (
    <Loader />
  ) : error ? (
    <div className="pt-24">{error}</div>
  ) : organizer.isEnded ? (
    <div className="pt-24 pb-20 xl:px-0 px-4 max-w-screen-xl mx-auto flex flex-col justify-center">
      <img
        src="/mo-results-found.png"
        className=" w-60 mt-10 mx-auto mb-10"
        alt="No found results"
      />
      <p className="text-center">
        Cet événement a expiré ou n'a pas été trouvé
      </p>
    </div>
  ) : (
    <div className="pt-24 pb-20 xl:px-0 px-4 max-w-screen-xl mx-auto">
      <div
        className="bg-cover relative min-h-[20vh] md:min-h-[30vh] bg-center w-full  rounded-xl"
        style={{ backgroundImage: `url(${organizer.cover})` }}
      >
        <div className=" absolute -bottom-10 text-center left-1/2 -translate-x-1/2 bg-black rounded-full">
          <img
            src={
              baseURL === "/api/"
                ? organizer.avatar
                : baseURLFiles + organizer.avatar
            }
            className="object-cover p-0.5 w-20 h-20 md:w-24 md:h-24 rounded-full"
            alt={organizer.name}
          />
        </div>
      </div>

      <div className="flex flex-col pt-10 justify-center items-center">
        <h1 className="text-xl py-3 font-bold">{organizer.name}</h1>
      </div>

      <div>
        <div className="flex flex-wrap text-sm md:text-base items-center gap-x-1 ">
          <span className="opacity-70">
            <Link to="/">Home</Link>
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-4 h-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M8.25 4.5l7.5 7.5-7.5 7.5"
            />
          </svg>
          <span className="opacity-70 whitespace-nowrap">
            <Link to={`/${organizer.slug}`}>{organizer.name}</Link>
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-4 h-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M8.25 4.5l7.5 7.5-7.5 7.5"
            />
          </svg>

          <span className="whitespace-nowrap">{product.name}</span>
        </div>
      </div>

      {loading ? (
        <Loader />
      ) : error ? (
        <Message variant="danger">{error}</Message>
      ) : (
        <>
          {/* <Meta title={product.name} /> */}
          <div className="md:flex md:items-start rounded-md pt-6 gap-3  ">
            <div className="md:w-7/12 ">
              <div className=" flex flex-col-reverse items-start justify-center relative ">
                <div className="flex-grow w-full border rounded-xl relative">
                  <div className=" md:max-h-[520px] mx-auto w-full">
                    <img
                      className="w-full md:max-h-[520px] max-h-max  rounded-md h-full object-contain"
                      src={`${
                        product?.images?.length > 0 &&
                        (baseURL === "/api/"
                          ? product?.images[activeImage]?.image
                          : baseURLFiles + product?.images[activeImage]?.image)
                      }`}
                      alt={
                        product?.images?.length > 0
                          ? product?.images[activeImage]?.name
                          : "image"
                      }
                    />
                  </div>

                  {/* next */}

                  <button
                    className="absolute top-1/2 bg-white shadow-md -right-3 md:right-0 p-2 disabled:bg-opacity-20  rounded-full "
                    disabled={activeImage >= product?.images?.length - 1}
                    onClick={() =>
                      activeImage < product?.images?.length - 1
                        ? setActiveImage(activeImage + 1)
                        : setActiveImage(product?.images?.length - 1)
                    }
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                  {/* prev */}

                  <button
                    className="absolute top-1/2 bg-white shadow-md -left-3 md:left-0 p-2 rounded-full disabled:bg-opacity-20 "
                    disabled={activeImage <= 0}
                    onClick={() =>
                      activeImage > 0
                        ? setActiveImage(activeImage - 1)
                        : setActiveImage(0)
                    }
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <div className="md:w-5/12 mt-4 md:mt-0 md:mx-2 bg-secondary bg-opacity-30  border shadow-md  py-8 px-4 rounded-xl">
              <h1 className="text-2xl uppercase ">{product.name}</h1>
              <div className="text-sm opacity-70">
                <i>
                  Organisateur :
                  <a href={`/${product?.organizer?.slug}`}>
                    {product?.organizer?.name}
                  </a>
                </i>
              </div>

              <h2 className="text-xl py-4">
                {product.variation?.name !== ""
                  ? getFormatedPrice(parseFloat(total))
                  : getFormatedPrice(product.price)}{" "}
              </h2>
              <form
                method="POST"
                onSubmit={(event) => {
                  event.preventDefault(); // Prevent form submission

                  if (
                    product.countInStock <= 0 ||
                    product.organizer?.isEnded ||
                    !isAvailable ||
                    isSoldOut
                  ) {
                    console.log("no");
                  } else {
                    addToCartHandler();
                  }
                }}
              >
                {product.hasVariation &&
                  product.variation &&
                  product.variation.name && (
                    <div>
                      <label>
                        {product.variation && product.variation.name}
                      </label>

                      <div className="border border-black rounded-full px-3">
                        <select
                          onChange={handleVariationChnage}
                          className="w-full px-3 py-2  border-none outline-none bg-transparent"
                          value={selectedVariation}
                          required
                        >
                          <option value="">Sélectionner une option</option>
                          {product?.variation?.attributes?.map((att) => (
                            <option key={uuid()} value={att.id}>
                              {att.name} - {att.price}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  )}
                <div className="pt-6">
                  {product.countInStock !== 0 && (
                    <>
                      <label htmlFor="" className="opacity-70">
                        Quantité
                      </label>
                      <div className="border border-black rounded-full px-3">
                        <select
                          value={qty}
                          onChange={(e) => {
                            setQty(e.target.value);
                            if (!isNaN(parseInt(e.target.value))) {
                              setListTicketName((prevList) => {
                                const updatedList = [...prevList];

                                if (
                                  parseInt(e.target.value) > updatedList.length
                                ) {
                                  // زيد العناصر الفارغة
                                  const diff =
                                    parseInt(e.target.value) -
                                    updatedList.length;
                                  return [
                                    ...updatedList,
                                    ...Array(diff).fill(""),
                                  ];
                                } else if (
                                  parseInt(e.target.value) < updatedList.length
                                ) {
                                  // نقص من اللائحة
                                  return updatedList.slice(
                                    0,
                                    parseInt(e.target.value)
                                  );
                                }

                                return updatedList; // نفس الطول، رجعها كما هي
                              });
                            }
                          }}
                          className=" w-full px-3 py-2  border-none outline-none bg-transparent"
                          required
                        >
                          {[
                            ...new Array(
                              product.countInStock > product.qte_tickets
                                ? product.qte_tickets
                                : product.countInStock
                            ),
                          ].map((op, id) => (
                            <option key={uuid()} value={id + 1}>
                              {id + 1}
                            </option>
                          ))}
                        </select>
                      </div>
                    </>
                  )}
                </div>
                {product.isCustomized && (
                  <div className="pt-4">
                    <label htmlFor="comment" className="opacity-70 italic ">
                      {product.customizationTitle}
                    </label>
                    <div
                      className="border border-black rounded-md mt-1 px-3"
                      id="comment"
                    >
                      <textarea
                        name="comment"
                        id="comment"
                        value={customization}
                        onChange={(e) => setCustomization(e.target.value)}
                        className="w-full  bg-transparent outline-none border-none px-3 py-2 "
                        rows="2"
                        required
                        placeholder="Votre réponse "
                      />
                    </div>
                  </div>
                )}
                {product.organizer?.is_daily && !product.has_date ? (
                  <>
                    <label htmlFor="" className="opacity-70 mt-4">
                      Date
                    </label>
                    <div className="border border-black rounded-full px-3">
                      {/* <DatePicker
                        required
                        selected={eventDate}
                        onChange={(date) => setEventDate(date)}
                        minDate={getTodayDate()}
                        className=" w-full p-3  border-none outline-none bg-transparent "
                        locale="fr" // Set the locale to French
                        dateFormat="dd/MM/yyyy" // Date format commonly used in France
                        placeholderText="jj/mm/aaaa" // Placeholder in French format
                      /> */}
                      <input
                        min={new Date().toISOString().split("T")[0]}
                        max={
                          product.organizer.max_date &&
                          product.organizer.max_date !== ""
                            ? product.organizer.max_date
                            : undefined
                        }
                        required
                        value={eventDate}
                        onChange={async (v) => {
                          setEventDate(v.target.value);
                          if (v.target.value !== "") {
                            try {
                              const response = await axios.post(
                                `products/check-product-date/${product._id}/`,
                                { date: v.target.value, quantity: qty }
                              );
                              setIsAvailable(true);
                              setIsSoldOut(false);
                            } catch (error) {
                              setIsAvailable(false);
                              setIsSoldOut(true);
                            }
                          } else {
                            setIsAvailable(false);
                            setIsSoldOut(false);
                          }
                        }}
                        type="date"
                        lang="fr"
                        className=" w-full px-3 py-2 border-none outline-none bg-transparent "
                      />
                    </div>
                  </>
                ) : (
                  <></>
                )}
                {product.organizer?.isticket_customization ? (
                  <>
                    <div className="pt-4">
                      <label htmlFor="comment" className="opacity-70 italic ">
                        Ticket Customization??
                      </label>
                      {listTicketName?.map((item, index) => (
                        <div className="pt-1 px-2" key={index}>
                          <div
                            className="border border-black rounded-full px-3 my-1"
                            id={"nameticket-" + index}
                          >
                            <input
                              key={index}
                              className="w-full bg-transparent outline-none border-none px-3 py-2"
                              value={item}
                              required
                              name={"ticket-name-" + index}
                              id={"ticket-name-" + index}
                              placeholder={"Ticket Name " + (index + 1)}
                              onChange={(e) => {
                                const newList = [...listTicketName];
                                newList[index] = e.target.value;
                                setListTicketName(newList);
                              }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </>
                ) : (
                  <></>
                )}
                {/* {product.isImmediately ? "Immediately" : " Programe"} <br />
                {!product.isImmediately && product.hasNoEndDate
                  ? "Has no end date"
                  : "has end date :" + new Date(product.endDate) > new Date()
                  ? "yes"
                  : "No"}
                <br />
                {!product.isImmediately &&
                new Date(product.eventDate) <= new Date()
                  ? "Available"
                  : "NOT YET"} */}
                {product.isDeleted ? (
                  <div className="font-semibold">
                    Ce produit n'est plus disponible
                  </div>
                ) : (
                  <div className="pt-6 ">
                    {product.countInStock <= 0 ||
                    product.organizer?.isEnded ||
                    !isAvailable ||
                    isSoldOut ? (
                      <button
                        type="button"
                        className="w-full overflow-hidden box-border bg-primary disabled:text-gray-600  p-3 text-white fade-in hover:scale-[1.01] disabled:scale-[1]  rounded-full disabled:bg-gray-300 "
                        disabled={
                          product.countInStock <= 0 ||
                          product.organizer?.isEnded ||
                          !isAvailable ||
                          isSoldOut
                        }
                      >
                        {/* {product.isImmediately ? "Immediatly - " : ""} */}

                        {product.countInStock <= 0 ||
                        product.organizer?.isEnded ||
                        isSoldOut
                          ? "Sold out"
                          : "AJOUTER AU PANIER"}
                      </button>
                    ) : (
                      <button
                        type="submit"
                        className="w-full overflow-hidden box-border bg-primary disabled:text-gray-600  p-3 text-white fade-in hover:scale-[1.01] disabled:scale-[1]  rounded-full disabled:bg-gray-300 "
                        disabled={
                          product.countInStock <= 0 ||
                          product.organizer?.isEnded ||
                          !isAvailable ||
                          isSoldOut
                        }
                      >
                        {/* {product.isImmediately ? "Immediatly - " : ""} */}

                        {product.countInStock <= 0 ||
                        product.organizer?.isEnded ||
                        isSoldOut
                          ? "Sold out"
                          : "AJOUTER AU PANIER"}
                      </button>
                    )}
                  </div>
                )}
              </form>
              <div className="pt-6">
                <div className="pb-2 text-sm italic opacity-70">Partager </div>

                <div className="flex items-center gap-3">
                  {/* <a className="link-share f" >
                            <svg xmlns="http://www.w3.org/2000/svg" className='hover:fill-primary' viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M13 19.938A8.001 8.001 0 0 0 12 4a8 8 0 0 0-1 15.938V14H9v-2h2v-1.654c0-1.337.14-1.822.4-2.311A2.726 2.726 0 0 1 12.536 6.9c.382-.205.857-.328 1.687-.381.329-.021.755.005 1.278.08v1.9H15c-.917 0-1.296.043-1.522.164a.727.727 0 0 0-.314.314c-.12.226-.164.45-.164 1.368V12h2.5l-.5 2h-2v5.938zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z"/></svg>
                          </a> */}
                  <a
                    className=""
                    href={`https://www.facebook.com/sharer/sharer.php?u=${`https://aylink.ma/p/${product?.organizer?.slug}/${product.slug}}/`}`}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 50 50"
                      className="w-6 h-6 hover:fill-secondary"
                    >
                      <path d="M 25 3 C 12.861562 3 3 12.861562 3 25 C 3 36.019135 11.127533 45.138355 21.712891 46.728516 L 22.861328 46.902344 L 22.861328 29.566406 L 17.664062 29.566406 L 17.664062 26.046875 L 22.861328 26.046875 L 22.861328 21.373047 C 22.861328 18.494965 23.551973 16.599417 24.695312 15.410156 C 25.838652 14.220896 27.528004 13.621094 29.878906 13.621094 C 31.758714 13.621094 32.490022 13.734993 33.185547 13.820312 L 33.185547 16.701172 L 30.738281 16.701172 C 29.349697 16.701172 28.210449 17.475903 27.619141 18.507812 C 27.027832 19.539724 26.84375 20.771816 26.84375 22.027344 L 26.84375 26.044922 L 32.966797 26.044922 L 32.421875 29.564453 L 26.84375 29.564453 L 26.84375 46.929688 L 27.978516 46.775391 C 38.71434 45.319366 47 36.126845 47 25 C 47 12.861562 37.138438 3 25 3 z M 25 5 C 36.057562 5 45 13.942438 45 25 C 45 34.729791 38.035799 42.731796 28.84375 44.533203 L 28.84375 31.564453 L 34.136719 31.564453 L 35.298828 24.044922 L 28.84375 24.044922 L 28.84375 22.027344 C 28.84375 20.989871 29.033574 20.060293 29.353516 19.501953 C 29.673457 18.943614 29.981865 18.701172 30.738281 18.701172 L 35.185547 18.701172 L 35.185547 12.009766 L 34.318359 11.892578 C 33.718567 11.811418 32.349197 11.621094 29.878906 11.621094 C 27.175808 11.621094 24.855567 12.357448 23.253906 14.023438 C 21.652246 15.689426 20.861328 18.170128 20.861328 21.373047 L 20.861328 24.046875 L 15.664062 24.046875 L 15.664062 31.566406 L 20.861328 31.566406 L 20.861328 44.470703 C 11.816995 42.554813 5 34.624447 5 25 C 5 13.942438 13.942438 5 25 5 z" />
                    </svg>
                  </a>

                  <a href="https://api.whatsapp.com/send?text=Binvenue chez aylink .">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 50 50"
                      className="w-6 h-6 hover:fill-secondary"
                    >
                      <path d="M 25 2 C 12.309534 2 2 12.309534 2 25 C 2 29.079097 3.1186875 32.88588 4.984375 36.208984 L 2.0371094 46.730469 A 1.0001 1.0001 0 0 0 3.2402344 47.970703 L 14.210938 45.251953 C 17.434629 46.972929 21.092591 48 25 48 C 37.690466 48 48 37.690466 48 25 C 48 12.309534 37.690466 2 25 2 z M 25 4 C 36.609534 4 46 13.390466 46 25 C 46 36.609534 36.609534 46 25 46 C 21.278025 46 17.792121 45.029635 14.761719 43.333984 A 1.0001 1.0001 0 0 0 14.033203 43.236328 L 4.4257812 45.617188 L 7.0019531 36.425781 A 1.0001 1.0001 0 0 0 6.9023438 35.646484 C 5.0606869 32.523592 4 28.890107 4 25 C 4 13.390466 13.390466 4 25 4 z M 16.642578 13 C 16.001539 13 15.086045 13.23849 14.333984 14.048828 C 13.882268 14.535548 12 16.369511 12 19.59375 C 12 22.955271 14.331391 25.855848 14.613281 26.228516 L 14.615234 26.228516 L 14.615234 26.230469 C 14.588494 26.195329 14.973031 26.752191 15.486328 27.419922 C 15.999626 28.087653 16.717405 28.96464 17.619141 29.914062 C 19.422612 31.812909 21.958282 34.007419 25.105469 35.349609 C 26.554789 35.966779 27.698179 36.339417 28.564453 36.611328 C 30.169845 37.115426 31.632073 37.038799 32.730469 36.876953 C 33.55263 36.755876 34.456878 36.361114 35.351562 35.794922 C 36.246248 35.22873 37.12309 34.524722 37.509766 33.455078 C 37.786772 32.688244 37.927591 31.979598 37.978516 31.396484 C 38.003976 31.104927 38.007211 30.847602 37.988281 30.609375 C 37.969311 30.371148 37.989581 30.188664 37.767578 29.824219 C 37.302009 29.059804 36.774753 29.039853 36.224609 28.767578 C 35.918939 28.616297 35.048661 28.191329 34.175781 27.775391 C 33.303883 27.35992 32.54892 26.991953 32.083984 26.826172 C 31.790239 26.720488 31.431556 26.568352 30.914062 26.626953 C 30.396569 26.685553 29.88546 27.058933 29.587891 27.5 C 29.305837 27.918069 28.170387 29.258349 27.824219 29.652344 C 27.819619 29.649544 27.849659 29.663383 27.712891 29.595703 C 27.284761 29.383815 26.761157 29.203652 25.986328 28.794922 C 25.2115 28.386192 24.242255 27.782635 23.181641 26.847656 L 23.181641 26.845703 C 21.603029 25.455949 20.497272 23.711106 20.148438 23.125 C 20.171937 23.09704 20.145643 23.130901 20.195312 23.082031 L 20.197266 23.080078 C 20.553781 22.728924 20.869739 22.309521 21.136719 22.001953 C 21.515257 21.565866 21.68231 21.181437 21.863281 20.822266 C 22.223954 20.10644 22.02313 19.318742 21.814453 18.904297 L 21.814453 18.902344 C 21.828863 18.931014 21.701572 18.650157 21.564453 18.326172 C 21.426943 18.001263 21.251663 17.580039 21.064453 17.130859 C 20.690033 16.232501 20.272027 15.224912 20.023438 14.634766 L 20.023438 14.632812 C 19.730591 13.937684 19.334395 13.436908 18.816406 13.195312 C 18.298417 12.953717 17.840778 13.022402 17.822266 13.021484 L 17.820312 13.021484 C 17.450668 13.004432 17.045038 13 16.642578 13 z M 16.642578 15 C 17.028118 15 17.408214 15.004701 17.726562 15.019531 C 18.054056 15.035851 18.033687 15.037192 17.970703 15.007812 C 17.906713 14.977972 17.993533 14.968282 18.179688 15.410156 C 18.423098 15.98801 18.84317 16.999249 19.21875 17.900391 C 19.40654 18.350961 19.582292 18.773816 19.722656 19.105469 C 19.863021 19.437122 19.939077 19.622295 20.027344 19.798828 L 20.027344 19.800781 L 20.029297 19.802734 C 20.115837 19.973483 20.108185 19.864164 20.078125 19.923828 C 19.867096 20.342656 19.838461 20.445493 19.625 20.691406 C 19.29998 21.065838 18.968453 21.483404 18.792969 21.65625 C 18.639439 21.80707 18.36242 22.042032 18.189453 22.501953 C 18.016221 22.962578 18.097073 23.59457 18.375 24.066406 C 18.745032 24.6946 19.964406 26.679307 21.859375 28.347656 C 23.05276 29.399678 24.164563 30.095933 25.052734 30.564453 C 25.940906 31.032973 26.664301 31.306607 26.826172 31.386719 C 27.210549 31.576953 27.630655 31.72467 28.119141 31.666016 C 28.607627 31.607366 29.02878 31.310979 29.296875 31.007812 L 29.298828 31.005859 C 29.655629 30.601347 30.715848 29.390728 31.224609 28.644531 C 31.246169 28.652131 31.239109 28.646231 31.408203 28.707031 L 31.408203 28.708984 L 31.410156 28.708984 C 31.487356 28.736474 32.454286 29.169267 33.316406 29.580078 C 34.178526 29.990889 35.053561 30.417875 35.337891 30.558594 C 35.748225 30.761674 35.942113 30.893881 35.992188 30.894531 C 35.995572 30.982516 35.998992 31.07786 35.986328 31.222656 C 35.951258 31.624292 35.8439 32.180225 35.628906 32.775391 C 35.523582 33.066746 34.975018 33.667661 34.283203 34.105469 C 33.591388 34.543277 32.749338 34.852514 32.4375 34.898438 C 31.499896 35.036591 30.386672 35.087027 29.164062 34.703125 C 28.316336 34.437036 27.259305 34.092596 25.890625 33.509766 C 23.114812 32.325956 20.755591 30.311513 19.070312 28.537109 C 18.227674 27.649908 17.552562 26.824019 17.072266 26.199219 C 16.592866 25.575584 16.383528 25.251054 16.208984 25.021484 L 16.207031 25.019531 C 15.897202 24.609805 14 21.970851 14 19.59375 C 14 17.077989 15.168497 16.091436 15.800781 15.410156 C 16.132721 15.052495 16.495617 15 16.642578 15 z" />
                    </svg>
                  </a>
                  {/* <a className="link-share w" href="https://api.whatsapp.com/send?text=Binvenue chez aylink .">
                            <svg xmlns="http://www.w3.org/2000/svg" className='w-6 h-6' fill="currentColor"  viewBox="0 0 16 16" width="18" height="18"> <path d="M13.601 2.326A7.854 7.854 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.933 7.933 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.898 7.898 0 0 0 13.6 2.326zM7.994 14.521a6.573 6.573 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.557 6.557 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592zm3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.729.729 0 0 0-.529.247c-.182.198-.691.677-.691 1.654 0 .977.71 1.916.81 2.049.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 ***********-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232z"/> </svg>
                              
                          </a> */}
                </div>
              </div>
            </div>
          </div>

          <div className="md:flex pt-6">
            <div className="md:w-2/3">
              <div className="flex items-center ">
                <h1 className=" py-3 italic flex-none pr-4">Description </h1>
                <div className="bg-gray-300 h-[1px] rounded-sm flex-grow "></div>
              </div>

              <div className="p-2 ">
                <p className="pt-3 whitespace-pre-line">
                  {product.description}
                </p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ProductScreen;
