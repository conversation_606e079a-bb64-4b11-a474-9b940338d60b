import React, { useEffect , useState} from 'react'
import { Link, useParams } from 'react-router-dom'
import axios from 'axios'
import Welcome from '../components/Welcome'


function CategoryScreen() {

    const {category} = useParams()

    const [subCategories, setSubCategories] = useState([])

    // useEffect(() => {
    //     const catId = category.split('-')[category.split('-').length -1]

    //     axios.get(`/api/categories/${catId}/sub/`).then(res=> {
    //         setSubCategories(res.data.subcategories)
    //       }).catch(err=>{
    //         setSubCategories([])
    //       })
         

    // }, [])
    
    return (
        <div className='bg-cover   min-h-screen w-full' style={{background:'url(/bg_itw.jpg)',backgroundPosition:"center 20%"}}>
            <div className='px-8 py-4 max-w-screen-md mx-auto pt-32 '>
                
                <Welcome />

                <div className='flex justify-center items-center gap-3 flex-wrap '>
                    {/* {subCategories.map(sub => {
                        return (
                            
                            <Link key={sub._id} to={`/tickets/${sub.category}/${sub.slug}/`}>
                                <div className=" w-72 mx-auto fade-in lg:group-hover:scale-105 group-hover:shadow-me duration-300 rounded-3xl square aspect-w-1 aspect-h-1 overflow-hidden  shadow-me" >
                                    <img src={`${sub.image}`} alt={"image"} className=" duration-300 w-full h-full object-center object-cover " />
                                </div>
                                <p className="opacity-70  mt-3 uppercase font-mono tracking-widest text-md text-center">{sub.name} </p>
                                <h3 className="font-600  pb-2 mt-1 text-xl flex items-center justify-center tracking-wider text-center uppercase">
                                </h3>
                            </Link>
                        )
                    })} */}
                </div>
                
            </div> 
        </div>
    )
}

export default CategoryScreen