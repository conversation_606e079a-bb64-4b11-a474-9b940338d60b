import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

const PaymentScreen = ({ history }) => {
  const cart = useSelector((state) => state.cart)
  const { shippingAddress } = cart


  const [paymentMethod, setPaymentMethod] = useState('PayPal')

  const dispatch = useDispatch()

  const submitHandler = (e) => {
    e.preventDefault()

  }

  return (
    <div className="pt-32 container max-w-screen-lg mx-auto">

        <h1 className="text-xl font-medium">Payment Method</h1>
        <form onSubmit={submitHandler}>
          <div className="py-2">
            <label >Select Method</label>
            <div className="flex items-center mt-3">
              <label className="pr-4" htmlFor="">PayPal or Credit Card</label>
              <input
                type='radio'
                id='PayPal'
                name='paymentMethod'
                value='PayPal'
                checked
                onChange={(e) => setPaymentMethod(e.target.value)}
              />
             
            </div>
            <div className="flex items-center mt-3">
              <label className="pr-4" htmlFor="">CMI</label>
              <input
                type='radio'
                id='PayPal'
                name='paymentMethod'
                value='CMI'
                checked
                onChange={(e) => setPaymentMethod(e.target.value)}
              />
             
            </div>
          </div>

          <button type='submit' className="p-2 bg-black text-white ">
            Continue
          </button>
        </form>
    </div>
  )
}

export default PaymentScreen
