import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import Message from '../components/Message'
import Loader from '../components/Loader'
import {
  getOrderDetails
} from '../actions/orderActions'



const TicketScreen = ({ match, history }) => {

  const orderId = match.params.id

  const dispatch = useDispatch()

  const orderDetails = useSelector((state) => state.orderDetails)
  const { order, loading, error } = orderDetails
 

  if (!loading) {
    //   Calculate prices
    const addDecimals = (num) => {
      return (Math.round(num * 100) / 100).toFixed(2)
    }
    order.itemsPrice = addDecimals(
      order.orderItems.reduce((acc, item) => acc + (item.price ), 0)
    )
  }

  useEffect(() => {

    if (!order ||order._id !== orderId) {
      dispatch(getOrderDetails(orderId))
    } 
  }, [dispatch, orderId, order])

 

  return loading ? (
    <Loader />
  ) : error ? (
    <Message variant='danger'>{error}</Message>
  ) : (
    
    <div>
      Ticket Screen 
    </div>
   
  )
}

export default TicketScreen
