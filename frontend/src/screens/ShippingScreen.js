import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import CheckoutSteps from "../components/CheckoutSteps";
import { saveShippingAddress } from "../actions/cartActions";
import Meta from "../components/Meta";
import { createOrder } from "../actions/orderActions";
import { useEffect } from "react";
import Message from "../components/Message";
import Loader from "../components/Loader";

const ShippingScreen = ({ history }) => {
  const cart = useSelector((state) => state.cart);
  const { shippingAddress } = cart;

  const addDecimals = (num) => {
    return (Math.round(num * 100) / 100).toFixed(2);
  };

  cart.itemsPrice = addDecimals(
    cart.cartItems.reduce(
      (acc, item) => acc + parseFloat(item.price) * item.qty,
      0
    )
  );

  cart.taxPrice = addDecimals(Number((0.1 * cart.itemsPrice).toFixed(2)));

  cart.totalPrice = (Number(cart.itemsPrice) + +Number(cart.taxPrice)).toFixed(
    2
  );

  const orderCreate = useSelector((state) => state.orderCreate);
  const { loading, order, success, error } = orderCreate;

  const [firstName, setFirstName] = useState(shippingAddress.firstName);
  const [lastName, setLastName] = useState(shippingAddress.lastName);
  const [phone, setPhone] = useState(shippingAddress.phone);
  const [email, setEmail] = useState(shippingAddress.email);

  const [isTermsChecked, setIsTermsChecked] = useState(false);

  const dispatch = useDispatch();

  const submitHandler = (e) => {
    e.preventDefault();

    const shippingAddress = {
      firstName,
      lastName,
      isTermsChecked,
      phone,
      email,
    };

    dispatch(saveShippingAddress(shippingAddress));

    dispatch(
      createOrder({
        orderItems: cart.cartItems,
        shippingAddress: shippingAddress,
        paymentMethod: "CMI",
        itemsPrice: cart.itemsPrice,
        taxPrice: cart.taxPrice,
        totalPrice: cart.totalPrice,
      })
    );

    // history.push('/order')
  };

  useEffect(() => {
    if (success) {
      history.push(`/order/${order._id}`);
    }
  }, [success, history]);

  // const handleChangeFirstName = (e) => {
  //     setFirstName(e.target.value.replace(/[^a-zA-Z0-9 ]/g, ""))

  // }

  return (
    <div className="pt-32 lg:px-0 px-4 container max-w-screen-md mx-auto">
      <Meta title={"Ticket Holder’s Information"} />
      <CheckoutSteps step1 step2 />
      <div className="p-4 shadow-sm border rounded-md">
        <h1 className="text-2xl font-medium">Ticket Holder’s Information </h1>

        {loading && <Loader />}
        {error && <Message variant="danger">{error}</Message>}

        <form onSubmit={submitHandler}>
          <div className="my-3">
            <label className="opacity-70 ">First Name</label>
            <div className="border rounded-md mt-1">
              <input
                className="p-2 w-full bg-transparent outline-none  "
                type="text"
                placeholder="Enter firstname"
                value={firstName}
                required
                onChange={(e) =>
                  setFirstName(e.target.value.replace(/[^a-zA-Z0-9 ]/g, ""))
                }
              />
            </div>
          </div>
          <div className="my-3">
            <label className="opacity-70 ">Last Name</label>
            <div className="border rounded-md mt-1">
              <input
                className="p-2 w-full  outline-none bg-transparent "
                type="text"
                placeholder="Enter Lastname"
                value={lastName}
                required
                onChange={(e) =>
                  setLastName(e.target.value.replace(/[^a-zA-Z0-9 ]/g, ""))
                }
              />
            </div>
          </div>

          <div className="my-2">
            <label className="opacity-70 pb-1">Phone</label>
            <div className="border rounded-md mt-1">
              <input
                className="p-2 w-full  outline-none bg-transparent "
                type="tel"
                pattern="[0-9]*\.?[0-9]*"
                placeholder="Enter Phone Number"
                value={phone}
                required
                onChange={(e) =>
                  setPhone(e.target.value.replace(/[^0-9]/g, ""))
                }
              />
            </div>
          </div>

          <div className="my-2">
            <label className="opacity-70 pb-2">Email</label>
            <div className="border rounded-md mt-1">
              <input
                className="p-2 w-full  outline-none border "
                type="email"
                placeholder="Enter Email Address"
                value={email}
                required
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
          </div>

          <div className="my-2 ">
            <input
              id="isTermsChecked"
              className="p-2  outline-none border "
              type="checkbox"
              name="isTermsChecked"
              value={isTermsChecked}
              required
              onChange={(e) => {
                setIsTermsChecked(e.target.checked);
              }}
            />
            <label className="pl-2" for="isTermsChecked">
              I have read and agreed to the{" "}
              <a
                href="/terms-and-conditions"
                className="text-blue-600"
                target={"_blank"}
              >
                terms and conditions
              </a>
            </label>
          </div>

          <button
            type="submit"
            className="p-2 my-2 bg-primary border-primary text-white"
          >
            Continue
          </button>
        </form>
      </div>
    </div>
  );
};

export default ShippingScreen;
