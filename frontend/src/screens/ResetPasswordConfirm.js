import axiosInstance from "../axios";
import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

function ResetPasswordConfirm() {
  const [otp, setOtp] = useState("");
  const [password, setPassword] = useState("");
  const location = useLocation();
  const [email, setEmail] = useState("");
  const [hash, setHash] = useState("");
  // const email = location.search
  //   ? location.search.substring(1).split("=")[1]
  //   : "";

  const [message, setMessage] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Get the query parameters from the current URL
    const queryParams = new URLSearchParams(window.location.search);

    // Extract the `email` and `hash` values
    const emailParam = queryParams.get("email");
    const hashParam = queryParams.get("hash");

    setEmail(emailParam || ""); // Set email state
    setHash(hashParam || ""); // Set hash state
  }, []);

  const handleResetPassword = (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage(null);
    setError(null);

    axiosInstance
      .post(`/users/password-reset-confirm/`, {
        email,
        otp,
        new_password: password,
        hash,
      })
      .then((res) => {
        setLoading(false);
        setMessage(res.data.detail);
      })
      .catch((error) => {
        setLoading(false);
        setError(
          error.response && error.response.data.detail
            ? error.response.data.detail
            : error.detail
        );
      });
  };
  return (
    <div className="p-8 pt-32 flex">
      <div className="p-4  rounded-md">
        <h1 className="text-xl font-semibold pb-4">
          Réinitialisation du mot de passe !
        </h1>
        {loading && "loading..."}
        {error && <div className="text-red-600 "> {error}</div>}
        <div>
          {message && (
            <div className="py-2">
              <div className="text-green-600 ">{message}</div>
              <div className="">
                <a href="/" className="text-blue-600 border-b">
                  Login
                </a>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-start">
          <form onSubmit={handleResetPassword}>
            {/* <div>
            <label htmlFor="email">Adresse email</label>
            <div className="border  rounded-md">
              <input
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                type="email"
                className="p-2 outline-none bg-transparent"
                placeholder="<EMAIL> "
              />
            </div>
          </div> */}
            <div className="pt-3">
              <label htmlFor="otp">Code OTP de 6 chiffres </label>
              <div className="border  rounded-md">
                <input
                  type="text"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  className="p-2 w-60 md:w-80  outline-none bg-transparent"
                  placeholder="0030002"
                />
              </div>
            </div>
            <div className="pt-3">
              <label htmlFor="otp">Nouveau mot de passe </label>
              <div className="border  rounded-md">
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="p-2 w-60 md:w-80  outline-none bg-transparent"
                  placeholder="********"
                />
              </div>
            </div>
            {!message && (
              <div className="pt-10">
                <button
                  className="bg-primary disabled:bg-gray-400 flex items-center p-2 rounded-md text-white"
                  disabled={loading}
                >
                  Réinitialiser
                  {loading && (
                    <div role="status">
                      <svg
                        aria-hidden="true"
                        class="w-4 h-4 ml-2 text-gray-200 animate-spin fill-blue-400"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                          fill="currentColor"
                        />
                        <path
                          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                          fill="currentFill"
                        />
                      </svg>
                    </div>
                  )}
                </button>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}

export default ResetPasswordConfirm;
