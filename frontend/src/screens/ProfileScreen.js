import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Message from "../components/Message";
import Loader from "../components/Loader";
import {
  getUserProfileDetails,
  logout,
  updateUserProfile,
} from "../actions/userActions";
import { listMyOrders } from "../actions/orderActions";

const OrderItem = ({ order }) => {
  return (
    <div className="flex justify-between mb-2 w-full py-2 px-4 border rounded-sm">
      <div className="">
        <div className="pl-2 float-right">
          <div className="pb-2">
            <h2 className="text">
              {order.shippingAddress && order.shippingAddress.lastName}{" "}
              {order.shippingAddress && order.shippingAddress.firstName}
            </h2>
            <div className=" opacity-80 text-sm">
              <a
                href={`/order/${order._id}`}
                className="border-b border-black  mr-2"
              >
                #{order._id.slice(0, 8)}
              </a>{" "}
              <span>
                {order.orderItems.reduce(function (a, b) {
                  return a + parseFloat(b.price);
                }, 0)}{" "}
                MAD
              </span>
            </div>
          </div>

          <div className="flex justify-between flex-grow">
            <div className="opacity-70 pr-4">Order at : {order.created_at}</div>
            <div className="opacity-70">
              {order.isPaid ? (
                <div>| Paid at {order.isPaid && order.paidAt}</div>
              ) : (
                <div className="text-secondary">
                  <a href={`/order/${order._id}`}>Pay Now </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const UpdateProfilePopUp = ({ isShow, setIsShow, user }) => {
  const [userName, setUserName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [message, setMessage] = useState(null);

  const dispatch = useDispatch();

  useEffect(() => {
    if (!user) {
    } else {
      setEmail(user.email);
      setUserName(user.user_name);
    }
  }, [user]);

  const submitHandler = (e) => {
    e.preventDefault();
    if (password !== "" && password !== confirmPassword) {
      setMessage("Passwords do not match");
    } else {
      dispatch(
        updateUserProfile({
          id: user._id,
          user_name: userName,
          email,
          password,
        })
      );
      dispatch(logout());
    }
  };

  return (
    <div className={isShow ? "block" : "hidden"}>
      <div
        onClick={() => setIsShow(false)}
        className="fixed bg-black w-screen top-0 bg-opacity-70 left-0 bottom-0 right-0 h-screen z-10 "
      ></div>

      <div className="md:w-1/2 w-full fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 ">
        <div className="bg-white border  mx-10 p-4  shadow-sm rounded-md">
          <form onSubmit={submitHandler} method="post">
            <h2 className="text-xl font-semibold">Update Profile Info</h2>
            {message && <Message variant={"danger"}>{message}</Message>}
            <div className="pt-2">
              <label htmlFor="firstname">Username</label>
              <div className="border w-full rounded-md">
                <input
                  disabled
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  placeholder="username"
                  type="text"
                  className="w-full bg-transparent p-2 outline-none "
                />
              </div>
            </div>
            <div className="pt-2">
              <label htmlFor="firstname">Password</label>
              <div className="border w-full rounded-md">
                <input
                  disabled
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="password"
                  type="password"
                  className="w-full bg-transparent p-2 outline-none "
                />
              </div>
            </div>
            <div className="pt-2">
              <label htmlFor="firstname">Confirm Password</label>
              <div className="border w-full rounded-md">
                <input
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="confirm password"
                  type="password"
                  className="w-full bg-transparent p-2 outline-none "
                />
              </div>
            </div>

            <div className="pt-2 flex justify-between">
              <button
                onClick={() => setIsShow(false)}
                className="px-4 py-2 rounded-md border border-primary "
                type="reset"
              >
                Cancel
              </button>
              <button
                onClick={submitHandler}
                className="px-4 py-2 rounded-md bg-primary text-white"
                type="submit"
              >
                Submit
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

const ProfileScreen = ({ location, history }) => {
  const [isPopUpUpdateProfileShow, setIsPopUpUpdateProfileShow] =
    useState(false);

  const userDetails = useSelector((state) => state.userDetails);
  const { loading, error, user } = userDetails;
  const userUpdateProfile = useSelector((state) => state.userUpdateProfile);
  const { success } = userUpdateProfile;

  const dispatch = useDispatch();
  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  const orderListMy = useSelector((state) => state.orderListMy);
  const { loading: loadingOrders, error: errorOrders, orders } = orderListMy;

  useEffect(() => {
    if (!userInfo) {
      history.push("/login");
    } else {
      dispatch(listMyOrders());
    }
  }, [dispatch, history, userInfo, success]);

  useEffect(() => {
    dispatch(getUserProfileDetails());
  }, [dispatch]);

  return (
    <div className="pt-32 pb-20 max-w-screen-xl mx-auto px-10">
      <div className="flex md:flex-row flex-col md:items-start gap-3">
        <div className="p-4  bg-white border rounded-md">
          {loading ? (
            <Loader />
          ) : error ? (
            <Message variant="danger">{error}</Message>
          ) : loading ? (
            "loading..."
          ) : error ? (
            error
          ) : (
            <div className="">
              <div className=" w-full">
                {/* <div className='w-20 h-20 border rounded-full mb-3'>
                    <img src={user.image} alt="" srcset="" />
                  </div> */}
                <h2 className=" font-semibold capitalize pb-4">
                  {user.user_name}
                </h2>
                <p className="whitespace-nowrap">Email : {user.email}</p>
              </div>

              <div className="pt-4">
                <button
                  onClick={() =>
                    setIsPopUpUpdateProfileShow(!isPopUpUpdateProfileShow)
                  }
                  className="border-b opacity-75 border-black"
                >
                  Update Info
                </button>
              </div>

              <div className="pt-4">
                <button
                  onClick={() => dispatch(logout())}
                  className="border-b opacity-75 border-black"
                >
                  Logout{" "}
                </button>
              </div>
            </div>
          )}
        </div>
        <div className="flex-grow bg-white p-4 border rounded-md">
          <h2 className="pb-4 text-xl ">Mes Commandes</h2>

          {loadingOrders ? (
            <Loader />
          ) : errorOrders ? (
            <Message variant="danger">{errorOrders}</Message>
          ) : (
            <div>
              {orders.map((order) => (
                <OrderItem order={order} key={order._id} />
              ))}
            </div>
          )}
        </div>
      </div>

      <UpdateProfilePopUp
        user={user}
        setIsShow={setIsPopUpUpdateProfileShow}
        isShow={isPopUpUpdateProfileShow}
      />
    </div>
  );
};

export default ProfileScreen;
