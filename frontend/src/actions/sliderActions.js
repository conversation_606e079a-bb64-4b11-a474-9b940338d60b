import axios from 'axios'

export const listSliders = () => async (dispatch) => {
    try {
      dispatch({ type: "SLIDER_LIST_REQUEST" })
  
      const { data } = await axios.get(
        `/api/slider`
      )
  
      dispatch({
        type: "SLIDER_LIST_SUCCESS",
        payload: data,
      })
    } catch (error) {
      dispatch({
        type: "SLIDER_LIST_FAIL",
        payload:
          error.response && error.response.data.message
            ? error.response.data.message
            : error.message,
      })
    }
  }

const createSlider = ()  => async (dispatch) =>{
    
    
}