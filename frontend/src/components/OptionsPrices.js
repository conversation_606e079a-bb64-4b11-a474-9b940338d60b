import React from "react";
import { useState } from "react";

function OptionsPrices({ options }) {
  const [nameOption, setNameOption] = useState("");
  const [optionName, setOptionName] = useState("");
  const [isPriceVary, setisPriceVary] = useState(false);

  const [showPopUp, setShowPopUp] = useState(false);

  const [optionValues, setOptionValues] = useState([]);
  const [optionValue, setOptionValue] = useState("");

  const addOptionName = (e) => {
    setOptionName(nameOption);
    setNameOption("");
  };

  const addOptionValue = (e) => {
    setOptionValues([...optionValues, { value: optionValue, price: 0 }]);
    setOptionValue("");
  };

  const saveOptions = () => {
    const variation = {
      name: optionName,
      attributes: optionValues,
    };
    localStorage.setItem("variation", JSON.stringify(variation));
  };

  const cancelSaveOptions = () => {
    localStorage.removeItem("variation");
    setShowPopUp(false);
  };

  return (
    <div className="p-2 mt-3 border rounded-md">
      <div className=" my-2 flex">
        <div
          className="border p-3 rounded-full"
          onClick={() => setShowPopUp(true)}
        >
          Add Options
        </div>
      </div>
      <div
        onClick={() => setShowPopUp(false)}
        className={`fixed ${
          showPopUp ? "visible" : "hidden"
        }  z-40 h-screen w-screen top-0 left-0 right-0 bottom-0 bg-black bg-opacity-60`}
      ></div>
      <div
        className={`fixed z-50  ${
          showPopUp ? "visible" : "hidden"
        } w-3/4 md:w-1/2 top-1/2 transform -translate-y-1/2 left-1/2 -translate-x-1/2  p-4 bg-white border shadow-md rounded-md`}
      >
        <div className="py-3">
          <h1 className="text-xl font-semibold">Add options </h1>
        </div>

        <div className="flex justify-start gap-x-3">
          <div>
            <div>Name of the Option</div>
            {optionName == "" ? (
              <div className="flex items-center rounded-md border">
                <input
                  type="text"
                  placeholder="color"
                  value={nameOption}
                  onChange={(e) => setNameOption(e.target.value)}
                  className="p-2 w-full outline-none bg-transparent "
                />
                <div onClick={addOptionName} className="px-2">
                  add
                </div>
              </div>
            ) : (
              <div className="py-3 flex items-start justify-between">
                <div>{optionName}</div>
                <div className="border-b flex">Delete</div>
              </div>
            )}

            {optionName && (
              <div className="py-3">
                <input
                  type="checkbox"
                  value={isPriceVary}
                  onChange={(e) => setisPriceVary(!isPriceVary)}
                />{" "}
                Prices vary for each {optionName}
              </div>
            )}
          </div>

          <div className={` ${optionName != "" ? "block" : "hidden"} `}>
            <div>Set Values For :{optionName}</div>

            <div className="flex items-center rounded-md border">
              <input
                type="text"
                placeholder="value"
                value={optionValue}
                onChange={(e) => setOptionValue(e.target.value)}
                className="p-2 w-full outline-none bg-transparent "
              />
              <div onClick={addOptionValue} className="px-2">
                add
              </div>
            </div>

            <div className="pt-3">
              {optionValues.map((item) => (
                <div className="p-2 mt-1 border w-full">{item.value}</div>
              ))}
            </div>
          </div>
        </div>

        <div className="pt-4 pb-2 flex justify-between items-center">
          <div
            onClick={cancelSaveOptions}
            className="px-4 cursor-pointer py-2 rounded-full border border-black"
          >
            Cancel
          </div>
          <div
            onClick={saveOptions}
            className="px-4 cursor-pointer py-2 rounded-full bg-black text-white"
          >
            Save
          </div>
        </div>
      </div>

      {localStorage.getItem("options") && (
        <div>
          <h1>{JSON.parse(localStorage.getItem("options")).name}</h1>
          {JSON.parse(localStorage.getItem("options")).is_price_vary ? (
            <table>
              {JSON.parse(localStorage.getItem("options")).values.map(
                (item) => (
                  <tbody>
                    <tr className="p-2 ">
                      <td>{item.value}</td>
                      <td>
                        <div>
                          <input
                            type="text"
                            className="p-2 outline-none bg-transparent"
                            value={item.price}
                          />
                        </div>
                      </td>
                      <td>Update</td>
                    </tr>
                  </tbody>
                )
              )}
            </table>
          ) : (
            <div>
              {JSON.parse(localStorage.getItem("options"))?.values?.map(
                (item) => (
                  <div>{item.value}</div>
                )
              )}
            </div>
          )}
        </div>
      )}
      <div></div>
    </div>
  );
}

export default OptionsPrices;
