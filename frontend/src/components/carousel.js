import React, { useEffect, useRef, useState } from 'react'
import { Link } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import Loader from './Loader'
import Message from './Message'
import { listTopProducts } from '../actions/productActions'


const ProductCarousel = () => {
  const dispatch = useDispatch()
  const [currentImage, setCurrentImage] = useState(0)

  const { loading, error, products } = useSelector((state) => state.productTopRated)
 

  useEffect(() => {
    dispatch(listTopProducts())
  }, [dispatch])
  console.log(currentImage)
  return loading ? (
    <Loader />
  ) : error ? (
    <Message variant='danger'>{error}</Message>
  ) : (

    <div className='px-2'>
      
      <div className="w-full h-screen relative"  >
          {/* <button className="absolute top-1/2 left-2 bg-gray-500 h-10" onClick={() => setCurrentImage(currentImage >1 ? currentImage-1 : products.length -1 )}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button> */}
          <div className="">
            <div className="absolute text-center top-1/4 left-1/2 transform -translate-x-1/2 ">
              <span className=" text-gray-400"> Decory.ma</span>
              <h1 className="uppercase text-4xl font-bold">
                Bring life to your home
              </h1>
             
              <button className="mt-4 px-4  py-2  bg-gray-50 ">
                Collections 
              </button>
          
            </div>

            <div className="absolute text-center bottom-4 left-1/2 transform -translate-x-1/2 ">
              <div className="flex">
                <div className="px-2">
                  <div className="bg-gray-200 hover:bg-white rounded-full h-3 w-3"></div>
                </div>
                <div className="px-2">
                  <div className="bg-gray-200 rounded-full h-3 w-3"></div>
                  
                </div>
                <div className="px-2">
                  <div className="bg-gray-200 rounded-full h-3 w-3"></div>
                  
                </div>
              </div>
            </div>
            {/* <img src={Image} className="w-full " /> */}
            {/* {products.map((product,id) => (
                <div  className={currentImage === id ? "block":"hidden"} key={product._id}>
                  <Link  to={`/product/${product._id}`}>
                    <img className="h-full w-full" src={product.image} alt={product.name}  />
              
                    <div className="absolute bottom-5 left-3" >
                      <h2 className="text-xl">
                        {product.name} (${product.price})
                      </h2>
                    </div>
                  </Link>
                </div>
              ))} */}
          </div>
            
          {/* <button className="absolute top-1/2 right-2 bg-gray-500 h-10" onClick={() => setCurrentImage(currentImage <  products.length -1 ? currentImage+1 : 0)}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button> */}
          
        </div>
      
   
    </div>
  )
}

export default ProductCarousel
