import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { createOrder } from "../actions/orderActions";

const GuestCheckoutModal = ({ isOpen, setIsOpen, cart, history, onLoginChoice }) => {
  const [checkoutType, setCheckoutType] = useState(""); // "login" or "guest"
  const [guestInfo, setGuestInfo] = useState({
    guest_name: "",
    guest_email: "",
    guest_phone: "",
  });
  const [errors, setErrors] = useState({});

  const dispatch = useDispatch();
  const orderCreate = useSelector((state) => state.orderCreate);
  const { loading, error, success, order } = orderCreate;

  useEffect(() => {
    if (success && order) {
      setIsOpen(false);
      history.push(`/order/${order._id}`);
    }
  }, [success, order, history, setIsOpen]);

  // Reset checkout type when modal is closed
  useEffect(() => {
    if (!isOpen) {
      setCheckoutType("");
      setGuestInfo({
        guest_name: "",
        guest_email: "",
        guest_phone: "",
      });
      setErrors({});
    }
  }, [isOpen]);

  const addDecimals = (num) => {
    return (Math.round(num * 100) / 100).toFixed(2);
  };

  const itemsPrice = addDecimals(
    cart.cartItems.reduce(
      (acc, item) => acc + parseFloat(item.price) * item.qty,
      0
    )
  );

  const taxPrice = addDecimals(Number((0 * itemsPrice).toFixed(2)));
  const totalPrice = (Number(itemsPrice) + Number(taxPrice)).toFixed(2);

  const validateGuestInfo = () => {
    const newErrors = {};
    
    if (!guestInfo.guest_name.trim()) {
      newErrors.guest_name = "Le nom est requis";
    }
    
    if (!guestInfo.guest_email.trim()) {
      newErrors.guest_email = "L'email est requis";
    } else if (!/\S+@\S+\.\S+/.test(guestInfo.guest_email)) {
      newErrors.guest_email = "Format d'email invalide";
    }
    
    if (!guestInfo.guest_phone.trim()) {
      newErrors.guest_phone = "Le téléphone est requis";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleGuestCheckout = () => {
    if (!validateGuestInfo()) {
      return;
    }

    // No need to check as_guest here since CartScreen already ensures
    // all items allow guest checkout before showing this modal

    const orderData = {
      orderItems: cart.cartItems,
      paymentMethod: "CMI",
      itemsPrice: itemsPrice,
      taxPrice: taxPrice,
      totalPrice: totalPrice,
      is_guest: true,
      guest_name: guestInfo.guest_name,
      guest_email: guestInfo.guest_email,
      guest_phone: guestInfo.guest_phone,
    };

    dispatch(createOrder(orderData));
  };

  const handleLoginChoice = () => {
    setIsOpen(false);
    // Small delay to ensure modal is closed before opening login modal
    setTimeout(() => {
      if (onLoginChoice) {
        onLoginChoice();
      }
    }, 100);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setGuestInfo(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Finaliser votre commande</h2>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {!checkoutType && (
            <div className="space-y-4">
              <p className="text-gray-600 mb-6">
                Comment souhaitez-vous continuer ?
              </p>
              
              <button
                onClick={handleLoginChoice}
                className="w-full p-4 border-2 border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-colors"
              >
                <div className="text-left">
                  <div className="font-semibold">Se connecter</div>
                  <div className="text-sm opacity-75">
                    Accédez à votre compte pour un checkout rapide
                  </div>
                </div>
              </button>

              <button
                onClick={() => setCheckoutType("guest")}
                className="w-full p-4 border-2 border-gray-300 text-gray-700 rounded-lg hover:border-primary hover:text-primary transition-colors"
              >
                <div className="text-left">
                  <div className="font-semibold">Continuer en tant qu'invité</div>
                  <div className="text-sm opacity-75">
                    Checkout rapide sans créer de compte
                  </div>
                </div>
              </button>
            </div>
          )}

          {checkoutType === "guest" && (
            <div className="space-y-4">
              <div className="flex items-center mb-4">
                <button
                  onClick={() => setCheckoutType("")}
                  className="mr-3 text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <h3 className="text-lg font-semibold">Informations invité</h3>
              </div>

              {(error || errors.general) && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                  {error || errors.general}
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom complet *
                </label>
                <input
                  type="text"
                  name="guest_name"
                  value={guestInfo.guest_name}
                  onChange={handleInputChange}
                  className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.guest_name ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Votre nom complet"
                />
                {errors.guest_name && (
                  <p className="text-red-500 text-sm mt-1">{errors.guest_name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  name="guest_email"
                  value={guestInfo.guest_email}
                  onChange={handleInputChange}
                  className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.guest_email ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors.guest_email && (
                  <p className="text-red-500 text-sm mt-1">{errors.guest_email}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Téléphone *
                </label>
                <input
                  type="tel"
                  name="guest_phone"
                  value={guestInfo.guest_phone}
                  onChange={handleInputChange}
                  className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.guest_phone ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="+212 6XX XXX XXX"
                />
                {errors.guest_phone && (
                  <p className="text-red-500 text-sm mt-1">{errors.guest_phone}</p>
                )}
              </div>

              <div className="border-t pt-4 mt-6">
                <div className="flex justify-between items-center mb-4">
                  <span className="text-lg font-semibold">Total:</span>
                  <span className="text-lg font-bold text-primary">{totalPrice} MAD</span>
                </div>
                
                <button
                  onClick={handleGuestCheckout}
                  disabled={loading}
                  className="w-full bg-primary text-white py-3 px-6 rounded-lg hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? "Traitement..." : "Finaliser la commande"}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default GuestCheckoutModal;
