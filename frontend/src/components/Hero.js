import React, { useState } from 'react'
import { DateRangePicker } from 'react-dates'
import "react-dates/initialize";
// import "react-dates/lib/css/_datepicker.css";
import "../assets/css/datepicker.css";

function Hero() {
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [focusedInput, setFocusedInput] = useState(null);
  // 2022-03-27
  const handleDatesChange = ({ startDate, endDate }) => {
    console.log(startDate.format("Y-MM-D"))
    setStartDate(startDate);
    setEndDate(endDate);
  };

  const searchAvailablePacks = () => {
    window.document.location='/products?startDate='+startDate.format("Y-MM-D")+'&endDate='+endDate.format("Y-MM-D")
  }
  
  return (
    <div className=' text-white  '  >
        <div style={{height:"620px",backgroundImage:'url(./cover2.jpeg)'}} className='flex px-4 items-center justify-center bg-cover bg-center bg-no-repeat '>
            <div>
                <h1 className='text-7xl pb-16 text-center font-mono font-extrabold'>
                    Enjoy Your Travel 
                </h1>

                <div className='py-8 rounded-xl flex items-center bg-secondary bg-opacity-70 justify-center'>
                  <div className='flex  rounded-md'>
                    <DateRangePicker
                        startDate={startDate}
                        startDateId="tata-start-date"
                        endDate={endDate}
                        endDateId="tata-end-date"
                        onDatesChange={handleDatesChange}
                        focusedInput={focusedInput}
                        onFocusChange={focusedInput => setFocusedInput(focusedInput)}
                    />
                    <button onClick={searchAvailablePacks} className='bg-primary p-2 w-32 text-white'>SEARCH</button>
                  </div>
                </div>

                {/* <div className='pt-20 flex justify-center'>
                  <Link to={"/products"}>
                      <div className='px-4 mx-4 text-4xl rounded-md text-white bg-black bg-opacity-60 py-2'>
                          GET YOUR TICKET NOW
                      </div>
                  </Link>
                </div> */}
            </div>
        </div>
    </div>

  )
}

export default Hero