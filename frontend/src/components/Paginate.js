import React from "react";
import { Link } from "react-router-dom";

const Paginate = ({ pages, page, organizer }) => {
  return (
    pages > 1 && (
      <div className="flex justify-end pt-8">
        {[...Array(pages).keys()].map((x) => (
          <Link key={x + 1} to={`/${organizer}/${x + 1}`}>
            {/* active={x + 1 === page} */}
            <div
              className={` border p-1 w-8 mr-2 hover:bg-opacity-90 flex items-center justify-center  rounded-full ${
                x + 1 === page ? "bg-primary text-white" : ""
              }`}
            >
              {x + 1}
            </div>
          </Link>
        ))}
      </div>
    )
  );
};

export default Paginate;
