import React from "react";

function Filters({ history }) {
  return (
    <div className="p-4 pt-0 ">
      <div className="flex items-center pb-1">
        <div className="pr-2">Filters : </div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-5 h-5"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
          />
        </svg>
      </div>
      <hr />
      <div>
        <div className="text-sm py-2">1. First one</div>
        <div className="text-sm py-2">2. First one</div>
      </div>

      <button className="hover:bg-gray-100 border px-3 py-1 rounded-full">
        Add Filter{" "}
      </button>
    </div>
  );
}

export default Filters;
