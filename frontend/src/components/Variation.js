import axios from "axios";
import React, { useState } from "react";

function Variation({ product }) {
  const [name, setName] = useState("");

  const [optionValue, setOptionValue] = useState("");
  const [priceValue, setPriceValue] = useState();
  const [isOpen, setIsOpen] = useState(false);

  const [attributes, setAttributes] = useState([]);

  const addAttribute = (e) => {
    setAttributes([
      ...attributes,
      { name: optionValue, price: parseFloat(priceValue) },
    ]);
    setPriceValue(0);
    setOptionValue("");
  };

  const handleAddVariation = async () => {
    const data = await axios.post(
      `/api/products/${product._id}/variations/add/`,
      { name: name, attributes }
    );
    setIsOpen(false);
  };

  const handleDeleteVariation = (varId) => async (e) => {
    const isOk = window.confirm("Are you sure you want to delete variation ! ");

    if (isOk) {
      const data = await axios.delete(
        `/api/products/${product._id}/variations/${varId}/delete/`
      );
    }
  };

  return (
    <div>
      {product.variation && product.variation.name ? (
        <div>
          <label>{product.variation && product.variation.name}</label>{" "}
          <button
            onClick={handleDeleteVariation(product.variation._id)}
            className="border-b opacity-70 hover:opacity-100"
          >
            Delete
          </button>
          <div>
            {product?.variation?.attributes?.map((item) => (
              <li>
                {" "}
                {item.name} - {item.price}{" "}
              </li>
            ))}
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-start">
          <div
            onClick={() => setIsOpen(true)}
            className="p-2 bg-gray-50 cursor-pointer hover:bg-gray-100 rounded-md"
          >
            Add variation
          </div>
          <div
            onClick={() => setIsOpen(false)}
            className={
              isOpen
                ? "fixed w-screen h-screen top-0 left-0 bottom-0 right-0 bg-black bg-opacity-70 z-40"
                : ""
            }
          ></div>
          <div
            className={
              isOpen
                ? "p-4 rounded-md z-50 bg-white border shadow-sm fixed top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2"
                : "hidden"
            }
          >
            <p>Variation Name</p>
            <div className="border rounded-md mt-3">
              <input
                type="text"
                className="p-2 outline-none bg-transparent"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>
            <p>Variation attributes</p>

            <div className="pt-4">
              <div className="flex items-center">
                <input
                  value={optionValue}
                  onChange={(e) => setOptionValue(e.target.value)}
                  type="text"
                  className="p-2 border outline-none bg-transparent"
                  placeholder="name"
                />
                <input
                  value={priceValue}
                  onChange={(e) => setPriceValue(e.target.value)}
                  type="number"
                  className="p-2 border outline-none bg-transparent"
                  placeholder="price"
                />
                <div className="p-2" onClick={addAttribute}>
                  Add
                </div>
              </div>

              <div className="pt-3">
                {attributes.map((item) => (
                  <div key={item.value} className="p-2 mt-1 border w-full">
                    {item.name} - {item.price}
                  </div>
                ))}
              </div>
            </div>
            <div className="pt-2">
              <div
                className="bg-primary cursor-pointer text-white p-2 rounded-md"
                onClick={handleAddVariation}
              >
                Save
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Variation;
