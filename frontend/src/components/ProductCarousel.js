import React, { useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import Loader from "./Loader";
import Message from "./Message";
import { listSliders } from "../actions/sliderActions";

const ProductCarousel = () => {
  const dispatch = useDispatch();
  const [currentImage, setCurrentImage] = useState(1);

  const { loading, error, sliders } = useSelector((state) => state.sliderList);

  useEffect(() => {
    dispatch(listSliders());
  }, [dispatch]);
  console.log(currentImage);
  return loading ? (
    <Loader />
  ) : error ? (
    <Message variant="danger">{error}</Message>
  ) : (
    <div className="">
      {sliders.map((slider, id) => (
        <div
          key={id}
          className={
            currentImage === id ? "w-full h-screen relative" : "hidden"
          }
          style={{
            backgroundImage: "url(" + slider.image + ")",
            // backgroundColor:"red",
            backgroundPosition: "center",
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
          }}
        >
          {/* <button className="absolute top-1/2 left-2 bg-gray-500 h-10" onClick={() => setCurrentImage(currentImage >1 ? currentImage-1 : products.length -1 )}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button> */}
          <div className="">
            <div className="absolute text-center top-1/4 left-1/2 transform -translate-x-1/2 ">
              <span className=" text-gray-400"> Morrocan Art</span>
              <h1 className="uppercase text-4xl font-bold">{slider.title}</h1>
              <h3>{slider.subtitle}</h3>
              <div className="mt-12">
                <Link to="/explore" className="px-4  py-2  bg-gray-50 ">
                  Explore Collections
                </Link>
              </div>
            </div>

            <div className="absolute text-center bottom-4 left-1/2 transform -translate-x-1/2 ">
              <div className="flex">
                <div className="px-2" onClick={() => setCurrentImage(0)}>
                  <div className="bg-black hover:bg-white rounded-full h-3 w-3"></div>
                </div>
                <div className="px-2" onClick={() => setCurrentImage(1)}>
                  <div className="bg-gray-400 hover:bg-white rounded-full h-3 w-3"></div>
                </div>
                <div className="px-2">
                  <div className="bg-gray-400 hover:bg-white rounded-full h-3 w-3"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductCarousel;
