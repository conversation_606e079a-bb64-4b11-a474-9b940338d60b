import { createStore, combineReducers, applyMiddleware } from "redux";
import thunk from "redux-thunk";
import {
  productListReducer,
  productDetailsReducer,
  productDeleteReducer,
  productCreateReducer,
  productUpdateReducer,
  productReviewCreateReducer,
  productTopRatedReducer,
} from "./reducers/productReducers";
import { cartReducer } from "./reducers/cartReducers";
import {
  userLoginReducer,
  userRegisterReducer,
  userDetailsReducer,
  userUpdateProfileReducer,
  userListReducer,
  userDeleteReducer,
  userUpdateReducer,
  userPasswordUpdateReducer,
} from "./reducers/userReducers";
import {
  orderCreateReducer,
  orderDetailsReducer,
  orderPayReducer,
  orderDeliverReducer,
  orderListMyReducer,
  orderListReducer,
  orderConfirmReducer,
} from "./reducers/orderReducers";
import { sliderListReducer } from "./reducers/sliderReducer";
import {
  expiredOrganizerListReducer,
  organizerDetailReducer,
  organizerListReducer,
} from "./reducers/organizerReducers";
import { sectionListReducer } from "./reducers/sectionReducers";
import { couponListReducer } from "./reducers/couponReducers";

const reducer = combineReducers({
  productList: productListReducer,
  productDetails: productDetailsReducer,
  sliderList: sliderListReducer,
  productDelete: productDeleteReducer,
  productCreate: productCreateReducer,
  productUpdate: productUpdateReducer,
  productReviewCreate: productReviewCreateReducer,
  productTopRated: productTopRatedReducer,

  sectionList: sectionListReducer,

  // organizer
  organizerList: organizerListReducer,
  expiredOrganizerList: expiredOrganizerListReducer,
  organizerDetails: organizerDetailReducer,

  // coupon
  couponList: couponListReducer,

  cart: cartReducer,
  userLogin: userLoginReducer,
  userRegister: userRegisterReducer,
  userDetails: userDetailsReducer,
  userUpdateProfile: userUpdateProfileReducer,
  userList: userListReducer,
  userDelete: userDeleteReducer,
  userUpdate: userUpdateReducer,
  userPasswordUpdate: userPasswordUpdateReducer,

  orderCreate: orderCreateReducer,
  orderDetails: orderDetailsReducer,
  orderPay: orderPayReducer,
  orderDeliver: orderDeliverReducer,
  orderListMy: orderListMyReducer,
  orderList: orderListReducer,
  orderConfirm: orderConfirmReducer,
});

const cartItemsFromStorage = localStorage.getItem("ay_cartItems")
  ? JSON.parse(localStorage.getItem("ay_cartItems"))
  : [];

//  test if token has been expired !

// const now = Math.ceil(Date.now() / 1000);

// const exp = JSON.parse(localStorage.getItem('user')) ?  JSON.parse(localStorage.getItem('user')).exp :0

// if (exp > now ){
//   console.log("Not Expired")
// }

const userInfoFromStorage = localStorage.getItem("authTokens")
  ? JSON.parse(localStorage.getItem("authTokens"))
  : null;

const shippingAddressFromStorage = localStorage.getItem("shippingAddress")
  ? JSON.parse(localStorage.getItem("shippingAddress"))
  : {};

const initialState = {
  cart: {
    cartItems: cartItemsFromStorage,
    shippingAddress: shippingAddressFromStorage,
  },
  userLogin: { userInfo: userInfoFromStorage },
};

const middleware = [thunk];

const store = createStore(
  reducer,
  initialState,
  applyMiddleware(...middleware)
);

export default store;
