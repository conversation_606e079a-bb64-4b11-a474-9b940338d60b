*.pyc
db.sqlite3
.DS_Store
.env
/staticfiles/*
/mediafiles/*
__pycache__/
/.vscode/
staticfiles/

scripts/
media/
resources/

backend/base/migrations/
backend/base/__pycache__/
backend/settings.py

# coverage result
.coverage
/coverage/

# pycharm
.idea/

# data
*.dump

# npm
node_modules
frontend/node_modules/
aylink-back-front/node_modules/
admin/node_modules/
back-office/node_modules/

npm-debug.log

# Webpack
/frontend/bundles/*
/frontend/webpack_bundles/*
/webpack-stats.json

/admin/bundles/*
/admin/webpack_bundles/*

/aylink-back-front/bundles/*
/aylink-back-front/webpack_bundles/*

# Sass
.sass-cache
*.map

# General
backend/.env
backend/env/
backend/venv/
backend/db3.sqlite3
/env/
/output/
/cache/
boilerplate.zip
.venv/

# Spritesmith
spritesmith-generated/
spritesmith.scss

# templated email
tmp_email/

.direnv
.envrc
.tool-versions

aylinkapp/
scanappaylink/