import { createStore, combineReducers, applyMiddleware } from "redux";
import thunk from "redux-thunk";
import { composeWithDevTools } from "redux-devtools-extension";
import { orderListReducer } from "./reducers/orderReducers";
import { userLoginReducer } from "./reducers/userReducers";
import { organizerListReducer } from "./reducers/organizerReducers";

const reducer = combineReducers({
  orderList: orderListReducer,

  userLogin: userLoginReducer,

  organizerList: organizerListReducer,
});

//  test if token has been expired !

// const now = Math.ceil(Date.now() / 1000);

// const exp = JSON.parse(localStorage.getItem('user')) ?  JSON.parse(localStorage.getItem('user')).exp :0

// if (exp > now ){
//   console.log("Not Expired")
// }

const userInfoFromStorage = localStorage.getItem("authTokens")
  ? JSON.parse(localStorage.getItem("authTokens"))
  : null;

const initialState = {
  userLogin: { userInfo: userInfoFromStorage },
};

const middleware = [thunk];

const store = createStore(
  reducer,
  initialState,
  applyMiddleware(...middleware)
);

export default store;
