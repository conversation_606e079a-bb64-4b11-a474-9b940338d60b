import React, { useEffect } from "react";
import { Route, BrowserRouter as Router, useLocation } from "react-router-dom";
import DashboardScreen from "./screens/DashboardScreen";
import LoginScreen from "./screens/LoginScreen";
import OrderListScreen from "./screens/OrderListScreen";
import FinanceScreen from "./screens/FinanceScreen";
import OrderSuperAdminScreen from "./screens/OrderSuperAdminScreen";

function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth",
    });
  }, [pathname]);

  return null;
}

const App = () => {
  return (
    <div className="">
      <Router>
        <ScrollToTop />

        <main className="min-h-screen">
          <Route path="/" component={DashboardScreen} exact />
          <Route path="/login" component={LoginScreen} exact />
          <Route path="/orders" component={OrderListScreen} exact />
          <Route path="/orders/:id" component={OrderSuperAdminScreen} exact />
          <Route path="/finances" component={FinanceScreen} exact />
        </main>

        {/* <Footer /> */}
      </Router>
    </div>
  );
};

export default App;
