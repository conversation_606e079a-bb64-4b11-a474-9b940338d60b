import React, { useEffect } from "react";
import SuperAdminLayout from "../layout/SuperAdminLayout";
import { useParams } from "react-router-dom/cjs/react-router-dom.min";
import { useDispatch, useSelector } from "react-redux";
import { getOrderDetails } from "../actions/orderActions";

function OrderSuperAdminScreen() {
  const { id } = useParams();
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getOrderDetails(id));
  }, [dispatch]);

  return (
    <SuperAdminLayout>
      <h1>Order Detail : {id}</h1>
      <div></div>
    </SuperAdminLayout>
  );
}

export default OrderSuperAdminScreen;
