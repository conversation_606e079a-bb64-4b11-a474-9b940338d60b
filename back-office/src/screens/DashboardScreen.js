import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import axiosInstance from "../axios";
import { logout } from "../actions/userActions";
import SuperAdminLayout from "../layout/SuperAdminLayout";
import { listOrganizers } from "../actions/organizerActions";
import { useLocation } from "react-router-dom";

function DashboardScreen({ history }) {
  //   console.log(orders);
  const location = useLocation();
  const organizerId =
    new URLSearchParams(location.search).get("organizerId") || "all";

  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;
  const dispatch = useDispatch();
  const organizerList = useSelector((state) => state.organizerList);
  const { organizers } = organizerList;

  const [stats, setStats] = useState({
    tickets_sold: 0,
    revenu: 0,
    total_places: 0,
    net_profit: 0,
  });

  useEffect(() => {
    if (userInfo) {
      const config = {
        headers: {
          Authorization: `Bearer ${userInfo.access}`,
        },
      };
      axiosInstance
        .get(`/stats-admin/?organizerId=${organizerId}`, config)
        .then((res) => {
          setStats(res.data);
        })
        .catch((error) => {
          const message =
            error.response && error.response.data.detail
              ? error.response.data.detail
              : error.detail;

          if (message == "User not found") {
            dispatch(logout());
          }
        });
    }
  }, []);
  useEffect(() => {
    if (userInfo) {
      dispatch(listOrganizers());
    } else {
      history.push("/login");
    }
  }, [dispatch, history, userInfo]);

  return (
    <SuperAdminLayout>
      <div>
        <select
          onChange={(e) =>
            (document.location.href = `/?organizerId=${e.target.value}`)
          }
          className="p-2 border "
          name="organizer"
          id="organizer"
        >
          <option value={"all"}> Choose Organizer</option>
          {organizers.map((item) => (
            <option
              selected={organizerId == item._id ? "selected" : ""}
              value={item._id}
            >
              {item.name}
            </option>
          ))}
        </select>
      </div>

      <div className="grid grid-cols-4 gap-3 p-2">
        <div className="p-2 border rounded-md">
          Tickets Sold
          <div>{stats.tickets_sold}</div>
        </div>
        <div className="p-2 border rounded-md">
          Revenu
          <div>{stats.revenu}</div>
        </div>
        <div className="p-2 border rounded-md">
          Total Places
          <div>{stats.total_places}</div>
        </div>
        <div className="p-2 border rounded-md">
          Net Profit
          <div>{stats.net_profit}</div>
        </div>
      </div>
    </SuperAdminLayout>
  );
}

export default DashboardScreen;
