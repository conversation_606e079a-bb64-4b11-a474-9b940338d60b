import React, { useEffect, useRef, useState } from "react";
import { listOrders } from "../actions/orderActions";
import { useDispatch, useSelector } from "react-redux";
import { listOrganizers } from "../actions/organizerActions";
import { useHistory, useLocation } from "react-router-dom";
import Paginate from "../components/Paginate";
import SuperAdminLayout from "../layout/SuperAdminLayout";
import useOnClickOutside from "../hooks/useOnClickOutside";

const OrderItem = ({ order }) => {
  const ref = useRef(null);
  const history = useHistory();
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);

  useOnClickOutside(ref, () => setIsOpen(false));

  // const deleteHandler = (id) => (ev) => {
  //   dispatch(deleteOrder(id));
  // };

  return (
    <tr class="bg-white border-b transition duration-300 ease-in-out hover:bg-gray-100">
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
        <a className="text-blue-600" href={`/orders/${order._id}`}>
          {order._id.substring(0, 8)}...
        </a>
      </td>
      <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
        {order.createdAt}
      </td>
      <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
        {order.isOffline
          ? `${order.infoClient.firstName} ${order.infoClient.lastName} `
          : `${order.user.first_name} ${order.user.last_name} `}
      </td>
      <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
        {order.isOffline ? (
          <span className="text-red-600">Offline</span>
        ) : (
          <span className="text-green-600">Online</span>
        )}
      </td>

      <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
        {order.orderItems.reduce(function (a, b) {
          return a + parseFloat(b.price) * b.qty;
        }, 0)}{" "}
        MAD{" "}
        <span className="text-xs ">
          {order.hasDiscount && `(- ${order.discount} %)`}
        </span>
      </td>
      <td class="text-sm relative text-gray-900 font-light px-6 py-4 whitespace-nowrap">
        °°°
      </td>
    </tr>
  );
};

function OrderListScreen({ history }) {
  const dispatch = useDispatch();
  const location = useLocation();
  const organizerId =
    new URLSearchParams(location.search).get("organizerId") || "all";
  const monthId = new URLSearchParams(location.search).get("monthId") || -1;
  const pageNumber = new URLSearchParams(location.search).get("page") || 1;
  const orderList = useSelector((state) => state.orderList);
  const { loading, error, orders, page, pages, count } = orderList;

  const organizerList = useSelector((state) => state.organizerList);
  const { organizers } = organizerList;

  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  useEffect(() => {
    if (userInfo) {
      dispatch(listOrders("", "", pageNumber, organizerId, monthId));
    } else {
      history.push("/login");
    }
  }, [dispatch, history, userInfo, pageNumber, organizerId, monthId]);

  //   list of organizers

  useEffect(() => {
    if (userInfo) {
      dispatch(listOrganizers());
    } else {
      history.push("/login");
    }
  }, [dispatch, history, userInfo]);

  let months = [
    { id: 1, name: "January" },
    { id: 2, name: "February" },
    { id: 3, name: "March" },
    { id: 4, name: "April" },
    { id: 5, name: "May" },
    { id: 6, name: "June" },
    { id: 7, name: "July" },
    { id: 8, name: "August" },
    { id: 9, name: "September" },
    { id: 10, name: "October" },
    { id: 11, name: "November" },
    { id: 12, name: "December" },
  ];

  return (
    <SuperAdminLayout>
      <select
        onChange={(e) =>
          (document.location.href = `/orders/?organizerId=${e.target.value}`)
        }
        className="p-2 border "
        name="organizer"
        id="organizer"
      >
        <option value={"all"}> Choose Organizer</option>
        {organizers.map((item) => (
          <option
            selected={organizerId == item._id ? "selected" : ""}
            value={item._id}
          >
            {item.name}
          </option>
        ))}
      </select>

      <select
        className="p-2 border "
        onChange={(e) =>
          (document.location.href = `/orders/?organizerId=${organizerId}&monthId=${e.target.value}`)
        }
        name="month"
        id="month"
      >
        <option value="-1">Select Month</option>
        {months.map((item) => (
          <option
            key={item.id}
            selected={monthId == item.id ? "selected" : ""}
            value={item.id}
          >
            {item.name}
          </option>
        ))}
      </select>

      <div class="flex flex-col bg-white rounded-md mt-4">
        <div class="overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="py-2 inline-block min-w-full sm:px-6 lg:px-8">
            <div class="overflow-hidden">
              <table class="min-w-full">
                <thead class="bg-white border-b">
                  <tr>
                    <th
                      scope="col"
                      class="text-sm font-medium text-gray-900 px-6 py-4 text-left"
                    >
                      #
                    </th>
                    <th
                      scope="col"
                      class="text-sm font-medium text-gray-900 px-6 py-4 text-left"
                    >
                      Date
                    </th>
                    <th
                      scope="col"
                      class="text-sm font-medium text-gray-900 px-6 py-4 text-left"
                    >
                      Client
                    </th>
                    <th
                      scope="col"
                      class="text-sm font-medium text-gray-900 px-6 py-4 text-left"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      class="text-sm whitespace-nowrap font-medium text-gray-900 px-6 py-4 text-left"
                    >
                      Total price
                    </th>
                    <th
                      scope="col"
                      class="text-sm font-medium text-gray-900 px-6 py-4 text-left"
                    ></th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    "..loading"
                  ) : error ? (
                    "Error : " + error
                  ) : (
                    <>
                      {orders.map((order, id) => (
                        <OrderItem order={order} key={id} />
                      ))}
                    </>
                  )}

                  <tr className="h-16"></tr>
                </tbody>
              </table>
              <Paginate
                organizerId={organizerId}
                section={"orders"}
                pages={pages}
                page={page}
              />
            </div>
          </div>
        </div>
      </div>
      {/* <Paginate
        page={page}
        pages={pages}
        organizerId={organizerId}
        section={"orders"}
      /> */}
    </SuperAdminLayout>
  );
}

export default OrderListScreen;
