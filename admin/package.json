{"name": "admin", "version": "0.1.0", "private": true, "proxy": "http://127.0.0.1:8000", "dependencies": {"@craco/craco": "^7.0.0-alpha.3", "@react-pdf/renderer": "^3.0.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "assert": "^2.0.0", "axios": "^0.20.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "chart.js": "^4.2.1", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.5.0", "js-sha512": "^0.8.0", "jwt-decode": "^3.1.2", "moment": "^2.29.4", "process": "^0.11.10", "query-string": "^7.1.1", "react": "^17.0.2", "react-calendar": "^4.0.0", "react-chartjs-2": "^5.2.0", "react-datepicker": "^4.10.0", "react-dom": "^17.0.2", "react-facebook-pixel": "^1.0.4", "react-helmet": "^6.1.0", "react-hook-form": "^7.44.3", "react-i18next": "^14.1.0", "react-paypal-button-v2": "^2.6.2", "react-redux": "^7.2.1", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.0", "recharts": "^2.5.0", "redux": "^4.0.5", "redux-devtools-extension": "^2.13.8", "redux-thunk": "^2.3.0", "stream-browserify": "^3.0.0", "util": "^0.12.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.4", "postcss": "^8.4.12", "tailwindcss": "^3.0.23"}}