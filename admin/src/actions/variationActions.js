import axios from "../axios";
import {
  VARIATION_CREATE_FAIL,
  VARIATION_CREATE_REQUEST,
  VARIATION_CREATE_SUCCESS,
  VARIATION_DELETE_FAIL,
  VARIATION_DELETE_REQUEST,
  VARIATION_DELETE_SUCCESS,
} from "../constants/variationConstants";

import { logout } from "./userActions";

export const createVariation =
  (idProduct, varia_) => async (dispatch, getState) => {
    try {
      dispatch({
        type: VARIATION_CREATE_REQUEST,
      });

      const {
        userLogin: { userInfo },
      } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userInfo.access}`,
        },
      };
      const data = await axios.post(
        `/products/${idProduct}/variations/add/`,
        varia_,
        config
      );

      dispatch({
        type: VARIATION_CREATE_SUCCESS,
        payload: data,
      });
    } catch (error) {
      const message =
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail;
      if (message === "Not authorized, token failed") {
        dispatch(logout());
      }
      dispatch({
        type: VARIATION_CREATE_FAIL,
        payload: message,
      });
    }
  };

export const deleteVariation =
  (productId, varId) => async (dispatch, getState) => {
    try {
      dispatch({
        type: VARIATION_DELETE_REQUEST,
      });

      const {
        userLogin: { userInfo },
      } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userInfo.access}`,
        },
      };

      const { data } = await axios.delete(
        `/products/${productId}/variations/${varId}/delete/`,
        config
      );

      dispatch({
        type: VARIATION_DELETE_SUCCESS,
        payload: data,
      });
    } catch (error) {
      const message =
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail;
      if (message === "Not authorized, token failed") {
        dispatch(logout());
      }
      dispatch({
        type: VARIATION_DELETE_FAIL,
        payload: message,
      });
    }
  };
