import React, { useEffect, useRef, useState } from "react";

const SearchBox = ({ history }) => {
  const [keyword, setKeyword] = useState("");
  const inputRef = useRef();

  const submitHandler = (e) => {
    e.preventDefault();
    if (keyword.trim()) {
      history.push(`/search/${keyword}`);
    } else {
      history.push("/");
    }
  };

  useEffect(() => {}, []);

  return (
    <form onSubmit={submitHandler}>
      <div
        className="flex justify-between border rounded-3xl px-2 bg-gray-100"
        onClick={() => inputRef.current.focus()}
      >
        <input
          type="text"
          name="q"
          ref={inputRef}
          onChange={(e) => setKeyword(e.target.value)}
          className="outline-none bg-transparent pl-2 "
          placeholder="search here"
        />
        <div className="flex items-center">
          <button type="submit" className=" py-2 px-3 rounded-r-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </button>
        </div>
      </div>
    </form>
  );
};

export default SearchBox;
