import React, { useRef, useState } from "react";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { deleteImage } from "../actions/productActions";
import Confirm from "./Confirm";
import { baseURL, baseURLFiles } from "../axios";

const Photo = ({ file }) => {
  return (
    <div className="">
      <img
        src={URL.createObjectURL(file)}
        className="w-32 h-32 object-cover rounded-md"
        alt=""
      />
    </div>
  );
};

function Photos({ product, productId, setImages, setLenImages }) {
  const [productImages, setProductImages] = useState([]);
  const [imageIdToDelete, setImageIdToDelete] = useState(-1);
  const [openConfirm, setOpenConfirm] = useState(false);

  const dispatch = useDispatch();

  const handleDeleteImage = (id) => {
    dispatch(deleteImage(productId, id));
  };

  const handleProductFiles = (event) => {
    const data = [];
    for (let i = 0; i < event.target.files.length; i++) {
      data.push(event.target.files[i]);
    }
    setProductImages((old) => [...old, ...data]);
    setImages((old) => [...old, ...data]);
    setLenImages(
      parseInt(product?.images?.length) + parseInt([...data].length)
    );
  };

  const imageRef = useRef();

  useEffect(() => {
    setLenImages(product?.images?.length);
  }, []);

  return (
    <div>
      <div className="p-4">
        <div className="flex flex-wrap  gap-3 ">
          {product?.images?.map((item) => {
            return (
              <div key={item._id} className=" relative w-32 h-32 ">
                <div
                  onClick={() => {
                    setImageIdToDelete(item._id);
                    setOpenConfirm(true);
                  }}
                  className="absolute cursor-pointer z-20 -top-3 -right-2 flex items-center justify-center w-6 h-6 bg-gray-50 rounded-full "
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth={2}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
                <img
                  className="w-full h-full object-cover rounded-md "
                  src={
                    baseURL === "/api/"
                      ? item?.image
                      : baseURLFiles + item?.image
                  }
                />
              </div>
            );
          })}
          <div>
            <input
              type="file"
              ref={imageRef}
              className="hidden"
              onChange={handleProductFiles}
              multiple
            />
          </div>

          {productImages.map((item, id) => (
            <Photo file={item} key={id} />
          ))}

          <div
            onClick={() => imageRef.current.click()}
            className="w-32 h-32 flex border-dotted border-2  rounded-md items-center justify-center cursor-pointer bg-gray-50"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-6 h-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
              />
            </svg>
          </div>
        </div>
      </div>

      <Confirm
        onClose={() => setOpenConfirm(false)}
        open={openConfirm}
        onConfirm={() => handleDeleteImage(imageIdToDelete)}
      >
        <h1 className="text-xl pb-3">Are you sure !</h1>
        <p>Please save any changes before deleting any photo ! </p>
      </Confirm>
    </div>
  );
}

export default Photos;
