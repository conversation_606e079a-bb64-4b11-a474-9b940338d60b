import axios from "axios";
import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { createVariation, deleteVariation } from "../actions/variationActions";

function Variation({ product }) {
  const [name, setName] = useState("");
  const dispatch = useDispatch();
  const [optionValue, setOptionValue] = useState("");
  const [priceValue, setPriceValue] = useState();
  const [isOpen, setIsOpen] = useState(false);

  const [attributes, setAttributes] = useState([]);

  const addAttribute = (e) => {
    setAttributes([
      ...attributes,
      { name: optionValue, price: parseFloat(priceValue) },
    ]);
    setPriceValue(0);
    setOptionValue("");
  };

  const handleAddVariation = async () => {
    dispatch(createVariation(product._id, { name: name, attributes }));
    document.location.href = `/product/${product._id}/edit/`;
    setIsOpen(false);
  };

  const handleDeleteVariation = (varId) => async (e) => {
    const isOk = window.confirm("Are you sure to delete variation ! ");

    if (isOk) {
      dispatch(deleteVariation(product._id, varId));
    }
  };

  return (
    <div>
      {product.variation && product.variation.name ? (
        <div>
          <label>{product.variation && product.variation.name}</label>{" "}
          <button
            onClick={handleDeleteVariation(product.variation._id)}
            className="border-b opacity-70 hover:opacity-100"
          >
            Delete
          </button>
          <div>
            {product?.variation?.attributes?.map((item) => (
              <li>
                {" "}
                {item.name} - {item.price}{" "}
              </li>
            ))}
          </div>
        </div>
      ) : (
        <div className="">
          {/* <div onClick={()=> setIsOpen(true)} className='p-2 bg-gray-50 cursor-pointer hover:bg-gray-100 rounded-md'>
                Add variation 
            </div> */}
          {/* <div onClick={()=> setIsOpen(false)} className={isOpen ? 'fixed w-screen h-screen top-0 left-0 bottom-0 right-0 bg-black bg-opacity-70 z-40':''}></div> */}
          {/* <div className={isOpen ?'p-4 rounded-md z-50 bg-white border shadow-sm fixed top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2':'hidden'}> */}
          <div>
            <div className="w-1/2">
              <div>Variation Name</div>
              <div className="border rounded-md mt-3">
                <input
                  type="text"
                  className="p-2 w-full outline-none bg-transparent"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                />
              </div>
            </div>
            <div className="pt-4">Variation attributes</div>

            <div className="pt-2">
              <div className="flex gap-3">
                <div className="border w-5/12 rounded-md">
                  <input
                    value={optionValue}
                    onChange={(e) => setOptionValue(e.target.value)}
                    type="text"
                    className="p-2  outline-none bg-transparent w-full"
                    placeholder="name"
                  />
                </div>

                <div className="border w-5/12 rounded-md">
                  <input
                    value={priceValue}
                    onChange={(e) => setPriceValue(e.target.value)}
                    type="number"
                    className="p-2  outline-none bg-transparent w-full"
                    placeholder="price"
                  />
                </div>
                <div className="p-2 w-2/12" onClick={addAttribute}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-6 h-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
              </div>

              <div className="pt-3">
                {attributes.map((item) => (
                  <div key={item.value} className="p-2 mt-1  w-full">
                    {/* {item.price} */}

                    <div className="flex gap-3">
                      <div className="border w-5/12 rounded-md">
                        <input
                          value={item.name}
                          disabled
                          type="text"
                          className="p-2  outline-none bg- w-full"
                          placeholder="name"
                        />
                      </div>

                      <div className="border w-5/12 rounded-md">
                        <input
                          value={item.price}
                          disabled
                          onChange={(e) => setPriceValue(e.target.value)}
                          type="number"
                          className="p-2  outline-none bg-transparent w-full"
                          placeholder="price"
                        />
                      </div>
                      {/* <div className='p-2 w-2/12' onClick={addAttribute}>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>

                                    </div> */}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="pt-2 flex justify-end pr-32">
              <div
                className="bg-primary px-4 cursor-pointer text-white p-2 rounded-md"
                onClick={handleAddVariation}
              >
                Save
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Variation;
