import React, { useEffect, useState } from "react";
import AdminLayout from "../../layout/AdminLayout";
import { useRef } from "react";
import { listSection } from "../../actions/sectionActions";
import { useDispatch, useSelector } from "react-redux";
import DatePicker from "react-datepicker";

import "react-datepicker/dist/react-datepicker.css";
import { createProduct } from "../../actions/productActions";
import Loader from "../../components/Loader";
import Message from "../../components/Message";
import { Prompt, useLocation } from "react-router-dom";

const Variation = ({
  variation,
  isVariationOpen,
  setVariation,
  setIsVariationOpen,
}) => {
  const [name, setName] = useState("");

  const [optionValue, setOptionValue] = useState("");
  const [priceValue, setPriceValue] = useState();
  const [isOpen, setIsOpen] = useState(false);

  const [attributes, setAttributes] = useState([]);

  const addAttribute = (e) => {
    setAttributes([
      ...attributes,
      { name: optionValue, price: parseFloat(priceValue) },
    ]);
    setPriceValue(0);
    setOptionValue("");
  };

  const handleAddVariation = async () => {
    // const data = await axios.post(
    //   `/api/products/${product._id}/variations/add/`,
    //   { name: name, attributes }
    // );
    setVariation({ name: name, attributes });

    setIsOpen(false);
  };

  const handleDeleteVariation = () => async (e) => {
    const isOk = window.confirm("Are you sure to delete variation ! ");

    if (isOk) {
      // const data = await axios.delete(
      //   `/api/products/${product._id}/variations/${varId}/delete/`
      // );
      setVariation({});
    }
  };

  return (
    <div>
      {variation && variation.name ? (
        <div>
          <label>{variation && variation.name}</label>{" "}
          <button
            onClick={handleDeleteVariation()}
            className="border-b opacity-70 hover:opacity-100"
          >
            Delete
          </button>
          <div>
            {variation?.attributes?.map((item) => (
              <li>
                {" "}
                {item.name} - {item.price}{" "}
              </li>
            ))}
          </div>
        </div>
      ) : (
        isVariationOpen && (
          <div className="fixed top-1/2 z-50 -translate-x-1/2 -translate-y-1/2 left-1/2 border shadow-sm bg-white p-3 rounded-md  ">
            <div>
              <div className="w-1/2">
                <div>Variation Name</div>
                <div className="border rounded-md mt-3">
                  <input
                    type="text"
                    className="p-2 w-full outline-none bg-transparent"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                  />
                </div>
              </div>
              <div className="pt-4">Variation attributes</div>

              <div className="pt-2">
                <div className="flex gap-3">
                  <div className="border w-5/12 rounded-md">
                    <input
                      value={optionValue}
                      onChange={(e) => setOptionValue(e.target.value)}
                      type="text"
                      className="p-2  outline-none bg-transparent w-full"
                      placeholder="name"
                    />
                  </div>

                  <div className="border w-5/12 rounded-md">
                    <input
                      value={priceValue}
                      onChange={(e) => setPriceValue(e.target.value)}
                      type="number"
                      className="p-2  outline-none bg-transparent w-full"
                      placeholder="price"
                    />
                  </div>
                  <div className="p-2 w-2/12" onClick={addAttribute}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-6 h-6"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                </div>

                <div className="pt-3">
                  {attributes.map((item) => (
                    <div key={item.value} className="p-2 mt-1  w-full">
                      {/* {item.price} */}

                      <div className="flex gap-3">
                        <div className="border w-5/12 rounded-md">
                          <input
                            value={item.name}
                            disabled
                            type="text"
                            className="p-2  outline-none bg- w-full"
                            placeholder="name"
                          />
                        </div>

                        <div className="border w-5/12 rounded-md">
                          <input
                            value={item.price}
                            disabled
                            onChange={(e) => setPriceValue(e.target.value)}
                            type="number"
                            className="p-2  outline-none bg-transparent w-full"
                            placeholder="price"
                          />
                        </div>
                        {/* <div className='p-2 w-2/12' onClick={addAttribute}>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>

                                    </div> */}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="pt-2 flex justify-end gap-4 pr-32">
                <div
                  className="bg-primary px-4 cursor-pointer text-white p-2 rounded-md"
                  onClick={handleAddVariation}
                >
                  Sauvegarder
                </div>
              </div>
            </div>
          </div>
        )
      )}
    </div>
  );
};

const LayoutProductSection = (props) => {
  return (
    <div className=" border rounded-md my-2">
      <div className="p-2 rounded-t-md bg-gray-100 ">{props.title}</div>
      <div className="p-4 bg-white rounded-b-md">{props.children}</div>
    </div>
  );
};

const ProductNewScreen2 = () => {
  const imageRef = useRef();
  const location = useLocation();
  const [isVariationOpen, setIsVariationOpen] = useState(false);
  const [productImages, setProductImages] = useState([]);
  const dispatch = useDispatch();
  const [product, setProduct] = useState({
    name: "",
    description: "",
    price: 0.0,
    section: 0,
    countInStock: 0,
    fromDate: new Date(),
    toDate: new Date(),
  });
  const [variation, setVariation] = useState({});
  const handleProductFiles = (event) => {
    const data = [];
    for (let i = 0; i < event.target.files.length; i++) {
      data.push(event.target.files[i]);
    }
    setProductImages((old) => [...old, ...data]);
  };

  const handleDeleteImage = (idx) => {
    const temp = [...productImages];
    temp.splice(idx, 1);
    setProductImages(temp);
  };

  const sectionList = useSelector((state) => state.sectionList);
  const {
    loadin: sectionsLoading,
    error: sectionsError,
    sections,
  } = sectionList;

  const productCreate = useSelector((state) => state.productCreate);
  const {
    loading: loadingCreate,
    error: errorCreate,
    success: successCreate,
    product: createdProduct,
  } = productCreate;

  useEffect(() => {
    dispatch(listSection());
  }, [dispatch]);

  const submitHandler = (e) => {
    e.preventDefault();
    setShouldBlockNavigation(false);
    window.onbeforeunload = undefined;
    console.log(variation);
    dispatch(createProduct(product, productImages, variation));
  };
  const [shouldBlockNavigation, setShouldBlockNavigation] = useState(true);
  useEffect(() => {
    if (location.pathname.includes("product/new") && shouldBlockNavigation) {
      window.onbeforeunload = () => true;
    } else {
      window.onbeforeunload = undefined;
    }
  }, []);

  useEffect(() => {
    if (successCreate) {
      document.location.href = "/productlist";
    }
  }, [successCreate]);

  return (
    <>
      <AdminLayout>
        <Prompt
          when={shouldBlockNavigation}
          message="You have unsaved changes, are you sure you want to leave?"
        />
        <div id="main" className="flex  pb-6 justify-between">
          <h1 className="text-xl font-600 ">Nouveau Ticket</h1>
        </div>

        {loadingCreate && <Loader />}
        {errorCreate && <Message variant="danger">{errorCreate}</Message>}

        <form>
          <LayoutProductSection title="Media">
            <div className="pb-2 text-sm text-gray-600">
              <p>
                * Ajoutez jusqu'à 3 photos pour que les acheteurs puissent voir
                tous les détails
              </p>
              <p>* 1x1 ratio</p>
            </div>
            <div className="flex flex-wrap  gap-3 ">
              <div>
                <input
                  type="file"
                  ref={imageRef}
                  className="hidden"
                  onChange={handleProductFiles}
                  multiple
                />
              </div>

              {productImages.map((file, id) => (
                // <Photo file={item} key={id} />
                <div className="relative" key={id}>
                  <div
                    onClick={() => handleDeleteImage(id)}
                    className="absolute cursor-pointer top-2 right-2 text-white hover:text-black"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-6 h-6"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </div>
                  <img
                    src={URL.createObjectURL(file)}
                    className="w-32 h-32 object-cover rounded-md"
                    alt=""
                  />
                </div>
              ))}

              <div
                onClick={() => imageRef.current.click()}
                className="w-32 h-32 flex border-dotted border-2  rounded-md items-center justify-center cursor-pointer bg-gray-50"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-6 h-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
                  />
                </svg>
              </div>
            </div>
          </LayoutProductSection>

          <LayoutProductSection title="Information du ticket">
            <div className="pb-2 text-sm text-gray-600">
              <p>
                * choisissez un titre clair et court pour décrire plus
                précisément votre article
              </p>
              <p>
                * Vous pouvez ajouter les sections a partir du pages (Produits &
                Sections)
              </p>
            </div>
            <div className="pt-2">
              <div className="py-2">
                <label className="">
                  Title <span className="required"></span>
                </label>
                <div className="border rounded-md mt-1">
                  <input
                    className="p-2 w-full outline-none focus:border-blue-300 bg-transparent"
                    type="text"
                    placeholder="Enter name"
                    value={product.name}
                    onChange={(e) =>
                      setProduct({ ...product, name: e.target.value })
                    }
                  />
                </div>
              </div>
              <div className=" my-2 ">
                <label>Section</label>
                {sectionsLoading ? (
                  "loading .."
                ) : sectionsError ? (
                  sectionsError
                ) : (
                  <div className="border rounded-md">
                    <select
                      value={product.section}
                      className="p-2 outline-none w-full bg-transparent"
                      name="section"
                      onChange={(e) =>
                        setProduct({ ...product, section: e.target.value })
                      }
                    >
                      <option value="">Select Section </option>
                      {sections.map((item) => (
                        <option value={item._id}>{item.name}</option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
              <div className=" my-2">
                <label className="">
                  Description <span className="required"></span>
                </label>
                <div className="border rounded-md mt-2">
                  <textarea
                    className="p-2 w-full outline-none bg-transparent"
                    type="text"
                    rows={10}
                    placeholder="Enter description"
                    value={product.description}
                    onChange={(e) =>
                      setProduct({ ...product, description: e.target.value })
                    }
                  />
                </div>
              </div>
            </div>
          </LayoutProductSection>

          <LayoutProductSection title="Stock et prix">
            <div className="my-2">
              <label className=" opacity-70">
                Price <span className="required"></span>
              </label>
              {Object.keys(variation).length === 0 ? (
                <div className="mt-2 border rounded-md">
                  <input
                    className="p-2 w-full  bg-transparent"
                    type="text"
                    placeholder="Enter price"
                    value={product.price}
                    onChange={(e) =>
                      setProduct({ ...product, price: e.target.value })
                    }
                  />
                </div>
              ) : (
                "Eidt Price in Variation "
              )}
            </div>
            <div className=" my-2 ">
              <label className="opacity-70 ">
                Quantity <span className="required "></span>
              </label>
              <div className="mt-2 border rounded-md">
                <input
                  type="text"
                  className="p-2 w-full outline-none bg-transparent"
                  placeholder="Enter countInStock"
                  value={product.countInStock}
                  onChange={(e) =>
                    setProduct({ ...product, countInStock: e.target.value })
                  }
                />
              </div>
            </div>
          </LayoutProductSection>

          <LayoutProductSection title="Date">
            <p className="opacity-70 text-sm">
              Choose Date when your item will be able to purchase !
            </p>
            <div className="p-3 grid grid-cols-2 gap-2">
              <div className=" my-2 ">
                <label className=" ">From Date</label>
                <div className="border rounded-md">
                  <DatePicker
                    className="w-full border-none bg-transparent p-2 outline-none"
                    selected={product.fromDate}
                    onChange={(date) =>
                      setProduct({ ...product, fromDate: date })
                    }
                  />
                </div>
              </div>

              <div className=" my-2 ">
                <label className=" ">To Date</label>
                <div className="border relative z-20 rounded-md">
                  <DatePicker
                    className="w-full border-none bg-transparent p-2 outline-none"
                    selected={product.toDate}
                    onChange={(date) =>
                      setProduct({ ...product, toDate: date })
                    }
                  />
                  <div className="absolute top-2 right-2 z-10">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-6 h-6"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </LayoutProductSection>

          <LayoutProductSection title="Variations">
            <div className="flex">
              <div
                onClick={() => setIsVariationOpen(true)}
                className="border flex  p-2 cursor-pointer rounded-md"
              >
                Add Variation
              </div>
            </div>
            <Variation
              // product={product}
              setVariation={setVariation}
              variation={variation}
              setIsVariationOpen={setIsVariationOpen}
              isVariationOpen={isVariationOpen}
            />
          </LayoutProductSection>

          <div className="flex justify-end gap-x-3 pt-4">
            <div
              onClick={() => (document.location.href = "/productlist")}
              className="border cursor-pointer py-2 px-8 text-primary rounded-md border-primary"
            >
              Retour
            </div>
            <button
              onClick={submitHandler}
              className="rounded-md py-2 px-8 text-white  bg-primary"
            >
              Sauvgarder
            </button>
          </div>
        </form>
      </AdminLayout>
    </>
  );
};

export default ProductNewScreen2;
