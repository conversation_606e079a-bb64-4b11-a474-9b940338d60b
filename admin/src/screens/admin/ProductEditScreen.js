import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Message from "../../components/Message";
import Loader from "../../components/Loader";
import {
  disableProduct,
  listProductDetails,
  updateProduct,
} from "../../actions/productActions";
import {
  PRODUCT_UPDATE_FAIL,
  PRODUCT_UPDATE_RESET,
} from "../../constants/productConstants";
import Photos from "../../components/Photos";
import AdminLayout from "../../layout/AdminLayout";
import { listSection } from "../../actions/sectionActions";
import Variation from "../../components/Variation";

const ProductEditScreen = ({ match, history }) => {
  const productId = match.params.id;

  const [images, setImages] = useState([]);
  const [lenImages, setLenImages] = useState(0);

  const [name, setName] = useState("");

  const [price, setPrice] = useState(0);

  const [fromDate, setFromDate] = useState(new Date());
  const [toDate, setToDate] = useState(new Date());

  const [section, setSection] = useState("");

  const [countInStock, setCountInStock] = useState(0);
  const [description, setDescription] = useState("");
  const [hasComment, setHasComment] = useState(false);

  const dispatch = useDispatch();

  const productDetails = useSelector((state) => state.productDetails);
  const { loading, error, product } = productDetails;

  const sectionList = useSelector((state) => state.sectionList);
  const {
    loadin: sectionsLoading,
    error: sectionsError,
    sections,
  } = sectionList;

  const productUpdate = useSelector((state) => state.productUpdate);
  const {
    loading: loadingUpdate,
    error: errorUpdate,
    success: successUpdate,
  } = productUpdate;

  useEffect(() => {
    if (successUpdate) {
      dispatch({ type: PRODUCT_UPDATE_RESET });
      history.push("/productlist");
    } else if (!error) {
      if (!product.name || product._id !== Number(productId)) {
        dispatch(listProductDetails(productId));
        dispatch(listSection());
      } else {
        setName(product.name);
        setPrice(product.price);
        setSection(product?.section?._id);
        console.log(product?.section?._id);

        setCountInStock(product.countInStock);
        setDescription(product.description);
        setHasComment(product.hasComment);
        setLenImages(product.images.length);
      }
    }
  }, [dispatch, history, productId, product, error, successUpdate]);

  const handleDisableProduct = (isActive) => (ev) => {
    dispatch(disableProduct(product._id, isActive));
  };

  const submitHandler = (e) => {
    e.preventDefault();
    if (lenImages === 0) {
      // errorUpdate = "Please enter at least one photo ! ";
      dispatch({
        type: PRODUCT_UPDATE_FAIL,
        payload: "Veuillez saisir au moins une photo !",
      });
    } else
      dispatch(
        updateProduct(
          {
            _id: productId,
            name,
            price,
            section,
            description,
            countInStock,
            hasComment,
            organizerDateFrom: fromDate,
            organizerDateTo: toDate,
          },
          images
        )
      );
  };

  useEffect(() => {
    const main = document.querySelector("#main");
    if (errorUpdate)
      main.scrollIntoView(
        {
          behavior: "smooth",
        },
        100
      );
  }, [errorUpdate]);

  return (
    <>
      <AdminLayout>
        <div id="main" className="flex  pb-6 justify-between">
          <h1 className="text-xl font-600 ">Edit Item</h1>
          <div>
            <div class="flex  items-center justify-center w-full ">
              <label
                onClick={handleDisableProduct(!product?.isActive)}
                for="toggleB"
                class="flex items-center cursor-pointer"
              >
                <div class="relative">
                  <input
                    type="checkbox"
                    checked={product?.isActive}
                    id="toggleB"
                    class="sr-only"
                  />
                  <div class="block bg-gray-600 w-14 h-8 rounded-full"></div>
                  <div class="dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition"></div>
                </div>
              </label>
            </div>
          </div>
        </div>

        {loadingUpdate && <Loader />}
        {errorUpdate && <Message variant="danger">{errorUpdate}</Message>}
        {loading ? (
          <Loader />
        ) : error ? (
          <Message variant="danger">{error}</Message>
        ) : (
          <form className="relative">
            <div className=" border rounded-md ">
              <div className="p-2 rounded-t-md bg-gray-100">Media</div>
              {lenImages}
              <Photos
                setLenImages={setLenImages}
                images={images}
                setImages={setImages}
                product={product}
                productId={productId}
              />
            </div>

            {/* info ticket */}
            <div className="border rounded-md mt-3 ">
              <div className="p-2 rounded-t-md bg-gray-100">
                Information du ticket
              </div>
              <div className="p-3">
                <div className="py-2">
                  <label className="">Name</label>
                  <div className="border rounded-md mt-1">
                    <input
                      className="p-2 w-full outline-none bg-transparent"
                      type="text"
                      placeholder="Enter name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div className=" my-2 ">
                    <label className=" ">Quantity</label>
                    <div className="border rounded-md">
                      <input
                        type="text"
                        className="p-2 w-full outline-none bg-transparent"
                        placeholder="Enter countInStock"
                        value={countInStock}
                        onChange={(e) => setCountInStock(e.target.value)}
                      />
                    </div>
                  </div>

                  {/* sections  */}

                  <div className=" my-2 ">
                    <label>Section</label>
                    {sectionsLoading ? (
                      "loading .."
                    ) : sectionsError ? (
                      sectionsError
                    ) : (
                      <div className="border rounded-md">
                        <select
                          value={section}
                          className="p-2 outline-none w-full bg-transparent"
                          name="section"
                          onChange={(e) => setSection(e.target.value)}
                        >
                          <option value="">Select Section </option>
                          {sections.map((item) => (
                            <option value={item._id}>
                              {item._id}-{item.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                  </div>
                </div>
                <div className=" my-2">
                  <label className="">Description</label>
                  <div className="border rounded-md mt-2">
                    <textarea
                      className="p-2 w-full outline-none bg-transparent"
                      type="text"
                      rows={10}
                      placeholder="Enter description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="border rounded-md mt-3">
              <div className="p-2 rounded-t-md  bg-gray-100">Pricing</div>
              <div className="p-3">
                <label className="">Price</label>
                <div className="border mt-3 rounded-md">
                  <input
                    className="p-2 w-full  bg-transparent"
                    type="text"
                    placeholder="Enter price"
                    value={price}
                    onChange={(e) => setPrice(e.target.value)}
                  />
                </div>
              </div>
            </div>

            <div className="border rounded-md mt-3">
              <div className="p-2 rounded-t-md  bg-gray-100">Date</div>
              <div className="p-3 grid grid-cols-2 gap-2">
                <div className=" my-2 ">
                  <label className=" ">From Date</label>
                  <div className="border rounded-md">
                    <input
                      type="datetime-local"
                      className="p-2 w-full outline-none bg-transparent"
                      value={fromDate}
                      onChange={(e) => setFromDate(e.target.value)}
                    />
                  </div>
                </div>

                <div className=" my-2 ">
                  <label className=" ">To Date</label>
                  <div className="border rounded-md">
                    <input
                      type="datetime-local"
                      className="p-2 w-full outline-none bg-transparent"
                      value={toDate}
                      onChange={(e) => setToDate(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="border rounded-md mt-3">
              <div className="p-2 rounded-t-md  bg-gray-100">Variation</div>
              <div className="p-2 mt-3">
                <Variation product={product} />
                <div className=" my-2">
                  Comment :{" "}
                  <input
                    type="checkbox"
                    checked={hasComment}
                    onClick={() => setHasComment(!hasComment)}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-x-3 pt-4">
              <button className="border py-2 px-8 text-primary rounded-md border-primary">
                Retour
              </button>
              <button
                onClick={submitHandler}
                className="rounded-md py-2 px-8 text-white  bg-primary"
              >
                Sauvgarder
              </button>
            </div>

            {/* <div className='fixed bottom-0 w-4/5 right-0  bg-white'>
                 <div className='flex p-2 justify-between items-center'>
                    <h1 className='text-2xl'>
                      {name}
                    </h1>
                    <button onClick={submitHandler} className="rounded-md py-2 px-8 text-white  bg-primary">
                      Update 
                    </button>
                 </div>
                </div> */}
          </form>
        )}

        {/* addons */}
        {/* <div className='py-2'>
                  <h1 className='text-xl font-600  '>
                    <span className='border-b'>Addons</span>
                  </h1>
                
                  <div className='flex pt-3 flex-wrap w-full overflow-x-scroll gap-3'>
                
                    {product?.addons?.map( item =>{
                        return ( <div className="mr-3 relative ">
                                  <div onClick={handleDeleteAddon(item._id)} className='absolute w-6 h-6 bg-gray-100 hover:bg-gray-200 flex items-center justify-center rounded-full -top-2 -right-2' >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                  </div>
                                   <div className='px-6 py-2 border rounded-md'>
                                     {item.name} | {item.value} DH
                                   </div>
                                  </div>
                                  )
                              
                            }
                      )}
                    
                  </div>

                  <div className=' py-4'>
  
                    <div className='pt-2 flex'>
                      <div className='border rounded-md mr-2'>
                        <input placeholder='addon' className='p-2 outline-none bg-transparent' type="text" value={option.name} onChange={(e)=> setOption({...option,name:e.target.value})} />
                      </div>
                      <div className='border rounded-md '>
                        <input placeholder='price' className='p-2 outline-none bg-transparent' type="text" value={option.value} onChange={(e)=> setOption({...option,value:e.target.value})} />
                      </div>
                      <button className="bg-primary  mx-2 rounded-md text-white w-10 flex items-center justify-center" disabled={option.value ==="" && option.name ===""}  onClick={handleAddNewOption}>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
                        </svg>
                      </button>
                    </div>

                  </div>

                </div> */}
      </AdminLayout>
    </>
  );
};

export default ProductEditScreen;
