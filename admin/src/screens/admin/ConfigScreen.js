import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  createCategory,
  deleteCategory,
  listCategory,
  updateCategory,
} from "../../actions/sectionActions";
import AdminLayout from "../../layout/AdminLayout";
import { baseURL, baseURLFiles } from "../../axios";

const CategoryItem = ({ category }) => {
  const dispatch = useDispatch();

  const [name, setName] = useState(category.name);
  const [imageCategory, setImageCategory] = useState("");

  const [openEditCategory, setOpenEditCategory] = useState(false);

  const handleDeleteCategory = (id) => (ev) => {
    ev.preventDefault();
    dispatch(deleteCategory(id));
  };

  const handleEditCategory = (id) => (ev) => {
    ev.preventDefault();
    imageCategory
      ? dispatch(updateCategory(id, { name, image: imageCategory }))
      : dispatch(updateCategory(id, { name, image: "" }));
  };

  return (
    <>
      <div className="p-2 relative w-1/4 shadow-md rounded-md  ">
        <div>
          <img
            src={`${
              baseURL === "/api/"
                ? category.image
                : baseURLFiles + category.image
            }`}
          />
          <div className="pt-2">{category.name}</div>
        </div>

        <div className="flex">
          <button
            className="text-xs border-b hover:text-blue-600 hover:border-blue-600"
            onClick={handleDeleteCategory(category._id)}
          >
            delete
          </button>
          <button
            className="text-xs border-b hover:text-blue-600 hover:border-blue-600 ml-4"
            onClick={() => setOpenEditCategory(true)}
          >
            edit
          </button>
        </div>
      </div>

      <div
        className={`p-4 ${
          openEditCategory ? "" : "hidden"
        } border absolute left-1/2 -translate-x-1/2 z-[50] bg-white shadow-md top-1/2 transform -translate-y-1/2`}
      >
        <button
          onClick={() => setOpenEditCategory(false)}
          className=" absolute top-0 bg-gray-50 right-0 p-2"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
        <div className="py-4 max-w-md ">
          Update <b>{category.name}</b> : <br />
          <div className="pt-2">
            <form onSubmit={handleEditCategory(category._id)}>
              <div className="border my-2 ">
                <input
                  className="p-2 outline-none bg-transparent w-full"
                  type="file"
                  placeholder="image"
                  onChange={(e) => setImageCategory(e.target.files[0])}
                />
              </div>
              <div className="border my-2 ">
                <input
                  className="p-2 outline-none bg-transparent w-full"
                  type="text"
                  placeholder="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                />
              </div>

              <button className="whitespace-nowrap p-2 bg-black text-white">
                Update Category
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* <ul>
                {category?.subcategories?.map(subcat => <li className='flex justify-between items-center'>
                    <div>
                        {subcat.name}
                    </div>
                    <button className='text-blue-600 border-b text-sm border-blue-600' onClick={handleDeleteSubCategory(subcat._id)}>
                        Delete
                    </button>
                </li>)}
            </ul> */}
    </>
  );
};

const ConfigScreen = ({ history }) => {
  const [openAddCategory, setOpenAddCategory] = useState(false);

  const categoryList = useSelector((state) => state.categoryList);
  const dispatch = useDispatch();

  const { loading, error, categories } = categoryList;

  const [name, setName] = useState("");
  const [imageCategory, setImageCategory] = useState("");

  useEffect(() => {
    dispatch(listCategory());
  }, [dispatch]);

  const handleAddCategory = (event) => {
    event.preventDefault();
    dispatch(createCategory({ name, image: imageCategory }));
  };

  return (
    <>
      <AdminLayout>
        <h1 className="text-xl pb-1 ">Config </h1>
        <hr />
        <div className="pt-3">
          <h2 className="font-semibold pb-2">Categories</h2>
          <div className="pb-4">
            <div className="flex gap-3">
              {categories?.map((cat) => (
                <CategoryItem category={cat} key={cat._id} />
              ))}
              <div
                onClick={() => setOpenAddCategory(true)}
                className={`w-1/4 flex border p-2 items-center justify-center`}
              >
                Add new Cat
              </div>
            </div>
          </div>

          <div
            className={`p-4 ${
              openAddCategory ? "" : "hidden"
            } absolute left-1/2 -translate-x-1/2 z-50 bg-white shadow-md top-1/2 transform -translate-y-1/2`}
          >
            <div className="py-4 max-w-md ">
              New Category : <br />
              <div className="pt-2">
                <form onSubmit={handleAddCategory}>
                  <div className="border my-2 ">
                    <input
                      className="p-2 outline-none bg-transparent w-full"
                      type="file"
                      placeholder="image"
                      onChange={(e) => setImageCategory(e.target.files[0])}
                    />
                  </div>
                  <div className="border my-2 ">
                    <input
                      className="p-2 outline-none bg-transparent w-full"
                      type="text"
                      placeholder="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                    />
                  </div>

                  <button className="whitespace-nowrap p-2 bg-black text-white">
                    Add Category
                  </button>
                </form>
              </div>
            </div>
          </div>

          {/* <AddNewType /> */}
        </div>
      </AdminLayout>
    </>
  );
};

export default ConfigScreen;
