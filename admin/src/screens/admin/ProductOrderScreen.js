import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  Prompt,
  useHistory,
  useLocation,
  useParams,
} from "react-router-dom/cjs/react-router-dom";
import { listProductDetails } from "../../actions/productActions";
import axiosInstance, { baseURL, baseURLFiles } from "../../axios";
import { logout } from "../../actions/userActions";
import AdminLayout from "../../layout/AdminLayout";
import getFormatedPrice from "../../utils/getFormatedPrice";
import { deleteOrder, listOrdersByProduct } from "../../actions/orderActions";
import Paginate from "../../components/Paginate";
import Loader from "../../components/Loader";
import Message from "../../components/Message";
import useOnClickOutside from "../../hooks/useOnClickOutside";

const OrderItem = ({ order, organizer }) => {
  const ref = useRef(null);
  const history = useHistory();
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);

  useOnClickOutside(ref, () => setIsOpen(false));

  const deleteHandler = (id) => (ev) => {
    dispatch(deleteOrder(id));
  };

  return (
    <tr class="bg-white border-b transition duration-300 ease-in-out hover:bg-gray-100">
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
        <a
          className="text-blue-600"
          href={`/orderlist/${order._id}?organizer=${organizer}`}
        >
          {order._id.substring(0, 8)}...
        </a>
      </td>
      <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
        {order.createdAt}
      </td>
      <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
        {order.isOffline
          ? `${order.infoClient.firstName} ${order.infoClient.lastName} `
          : `${order.user.first_name} ${order.user.last_name} `}
      </td>
      <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
        {order.isOffline ? (
          <span className="text-red-600">Offline</span>
        ) : (
          <span className="text-green-600">Online</span>
        )}
      </td>

      <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
        {order.orderItems.reduce(function (a, b) {
          return a + parseFloat(b.price) * b.qty;
        }, 0)}{" "}
        MAD{" "}
        <span className="text-xs ">
          {order.hasDiscount && `(- ${order.discount} %)`}
        </span>
      </td>
      <td class="text-sm relative text-gray-900 font-light px-6 py-4 whitespace-nowrap">
        °°°
      </td>
    </tr>
  );
};

function ProductOrderScreen({ match, history }) {
  const { id } = useParams();
  const { t } = useTranslation();

  const location = useLocation();
  const dispatch = useDispatch();
  const searchParams = new URLSearchParams(location.search);
  const organizerId = searchParams.get("organizer", "");
  const page = searchParams.get("page") || "1";

  const [listOrganizers, setListOrganizers] = useState([]);
  const [selectOrganizer, setSelectOrganizer] = useState(organizerId);

  const productDetails = useSelector((state) => state.productDetails);
  const { loading, error, product } = productDetails;

  const ordersByProduct = useSelector((state) => state.orderListByProduct);
  const { loadingListOrdersProduct, errorListOrdersProduct, orders, pages } =
    ordersByProduct;

  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  useEffect(async () => {
    if (!userInfo) {
      history.push("/login");
    } else {
      const config = {
        headers: {
          Authorization: `Bearer ${userInfo.access}`,
        },
      };

      await axiosInstance
        .get("/organizers/organizers-user/", config)
        .then((res) => {
          // console.log(res.data)
          setListOrganizers(res.data.organizers ?? []);
          if (listOrganizers.length !== 0 && organizerId === "") {
            setSelectOrganizer(listOrganizers[0]._id ?? "");
          }
        })
        .catch((error) => {
          const message =
            error.response && error.response.data.detail
              ? error.response.data.detail
              : error.detail;

          if (message === "User not found") {
            dispatch(logout());
          }
        });
      await dispatch(listProductDetails(id));
      await dispatch(listOrdersByProduct(id, selectOrganizer, page));
    }
  }, [userInfo, history, selectOrganizer, organizerId, id, page]);

  useEffect(() => {
    if (listOrganizers.length !== 0 && selectOrganizer === "") {
      setSelectOrganizer(listOrganizers[0]._id ?? "");
    }
  }, [listOrganizers, selectOrganizer]);

  const [shouldBlockNavigation, setShouldBlockNavigation] = useState(true);

  return (
    <div>
      <AdminLayout>
        <div id="main" className="flex  pb-6 justify-between">
          <h1 className="text-xl font-600 ">{t("Ticket History")}</h1>
        </div>
        <div className="my-8 mx-5">
          {loading ? (
            <div role="status">
              <svg
                aria-hidden="true"
                class="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                viewBox="0 0 100 101"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor"
                />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="currentFill"
                />
              </svg>
              <span class="sr-only">Loading...</span>
            </div>
          ) : error ? (
            <div className="flex w-full border-l-6 border-[#F87171] bg-[#F87171] bg-opacity-[15%] px-7 py-3 shadow-md dark:bg-[#1B1B24] dark:bg-opacity-30 md:p-5 my-2">
              <div className="mr-5 flex h-9 w-full max-w-[36px] items-center justify-center rounded-lg bg-[#F87171]">
                <svg
                  width="13"
                  height="13"
                  viewBox="0 0 13 13"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.4917 7.65579L11.106 12.2645C11.2545 12.4128 11.4715 12.5 11.6738 12.5C11.8762 12.5 12.0931 12.4128 12.2416 12.2645C12.5621 11.9445 12.5623 11.4317 12.2423 11.1114C12.2422 11.1113 12.2422 11.1113 12.2422 11.1113C12.242 11.1111 12.2418 11.1109 12.2416 11.1107L7.64539 6.50351L12.2589 1.91221L12.2595 1.91158C12.5802 1.59132 12.5802 1.07805 12.2595 0.757793C11.9393 0.437994 11.4268 0.437869 11.1064 0.757418C11.1063 0.757543 11.1062 0.757668 11.106 0.757793L6.49234 5.34931L1.89459 0.740581L1.89396 0.739942C1.57364 0.420019 1.0608 0.420019 0.740487 0.739944C0.42005 1.05999 0.419837 1.57279 0.73985 1.89309L6.4917 7.65579ZM6.4917 7.65579L1.89459 12.2639L1.89395 12.2645C1.74546 12.4128 1.52854 12.5 1.32616 12.5C1.12377 12.5 0.906853 12.4128 0.758361 12.2645L1.1117 11.9108L0.758358 12.2645C0.437984 11.9445 0.437708 11.4319 0.757539 11.1116C0.757812 11.1113 0.758086 11.111 0.75836 11.1107L5.33864 6.50287L0.740487 1.89373L6.4917 7.65579Z"
                    fill="#ffffff"
                    stroke="#ffffff"
                  ></path>
                </svg>
              </div>
              <div className="w-full">
                <ul>
                  <li className="leading-relaxed text-[#CD5D5D]">
                    {t(error) ?? t("This Ticket was not found")}
                  </li>
                </ul>
              </div>
            </div>
          ) : product ? (
            <>
              {/* info product */}
              <div className="flex flex-row  items-center bg-white py-5 rounded-md shadow">
                <div className="mx-3 my-3">
                  {product.images?.length > 0 ? (
                    <img
                      className="w-32 h-32 border-dotted border-2  rounded-md"
                      src={
                        baseURL === "/api/"
                          ? product.images[0].image
                          : baseURLFiles + product.images[0].image
                      }
                      alt={product.name ?? "product image"}
                    />
                  ) : (
                    <div className="w-32 h-32 flex border-dotted border-2  rounded-md items-center justify-center cursor-pointer bg-gray-50">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-6 h-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
                        />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="text-lg ">{product.name}</div>
                  <div className="font-medium">
                    {getFormatedPrice(product.price)}
                  </div>
                  <div className="text-sm">
                    {" "}
                    {product?.countInStock > 0 ? (
                      `${t("Quantity")} : ${product?.countInStock}`
                    ) : (
                      <span className="bg-red-600 text-white rounded-md px-1">
                        {t("Sold Out")}
                      </span>
                    )}
                  </div>

                  <div className="text-sm py-1 opacity-70">
                    {product.isActive ? (
                      <span className="px-1 rounded-md bg-green-600 text-white">
                        {t("Active")}
                      </span>
                    ) : (
                      <span className="px-1 rounded-md text-white bg-red-600">
                        {t("Deactivated")}
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-red">
                    {product?.hasActiveDiscount ? "ON SALE" : ""}
                  </div>
                </div>
              </div>
              {/* orders */}
              <div class="flex flex-col bg-white rounded-md mt-4">
                <div class="overflow-x-auto sm:-mx-6 lg:-mx-8">
                  <div class="py-2 inline-block min-w-full sm:px-6 lg:px-8">
                    <div class="overflow-hidden">
                      <table class="min-w-full">
                        <thead class="bg-white border-b">
                          <tr>
                            <th
                              scope="col"
                              class="text-sm font-medium text-gray-900 px-6 py-4 text-left"
                            >
                              #
                            </th>
                            <th
                              scope="col"
                              class="text-sm font-medium text-gray-900 px-6 py-4 text-left"
                            >
                              {t("Date")}
                            </th>
                            <th
                              scope="col"
                              class="text-sm font-medium text-gray-900 px-6 py-4 text-left"
                            >
                              {t("Client")}
                            </th>
                            <th
                              scope="col"
                              class="text-sm font-medium text-gray-900 px-6 py-4 text-left"
                            >
                              {t("Status")}
                            </th>
                            <th
                              scope="col"
                              class="text-sm whitespace-nowrap font-medium text-gray-900 px-6 py-4 text-left"
                            >
                              {t("Total price")}
                            </th>
                            <th
                              scope="col"
                              class="text-sm font-medium text-gray-900 px-6 py-4 text-left"
                            ></th>
                          </tr>
                        </thead>
                        <tbody>
                          {loadingListOrdersProduct ? (
                            <Loader />
                          ) : errorListOrdersProduct ? (
                            <Message variant="danger">
                              {errorListOrdersProduct}
                            </Message>
                          ) : (
                            <>
                              {orders?.map((order, id) => (
                                <OrderItem
                                  order={order}
                                  key={id}
                                  organizer={selectOrganizer}
                                />
                              ))}
                            </>
                          )}

                          <tr className="h-16"></tr>
                        </tbody>
                      </table>
                      <Paginate
                        section={`product/${id}/orders`}
                        pages={pages}
                        page={page}
                        organizer={selectOrganizer}
                        search={""}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </>
          ) : null}
        </div>
      </AdminLayout>
    </div>
  );
}

export default ProductOrderScreen;
