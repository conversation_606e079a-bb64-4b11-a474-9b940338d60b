{"name": "admin-reparup-reactjs", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "date-fns": "^4.1.0", "firebase": "^10.2.0", "framer-motion": "^11.0.13", "leaflet": "^1.9.4", "moment": "^2.29.4", "react": "^18.2.0", "react-circle": "^1.1.1", "react-datepicker": "^7.5.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-google-autocomplete": "^2.7.3", "react-html-table-to-excel": "^2.0.0", "react-leaflet": "^4.2.1", "react-places-autocomplete": "^7.3.0", "react-redux": "^8.1.1", "react-router-dom": "^6.13.0", "react-scripts": "^5.0.1", "react-select": "^5.8.1", "react-tag-input": "^6.9.0", "react-tagsinput": "^3.20.3", "react-toastify": "^9.1.3", "react-tooltip": "^5.26.3", "redux": "^4.2.1", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^2.4.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start --port 3001", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.3.2"}, "proxy": "https://maps.googleapis.com"}