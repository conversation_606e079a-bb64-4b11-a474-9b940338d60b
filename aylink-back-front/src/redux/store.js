import { createStore, combineReducers, applyMiddleware } from "redux";
import thunk from "redux-thunk";

import {
  createNewUserReducer,
  deleteUserReducer,
  getDetailUserReducer,
  getProfileUserReducer,
  updateLastLoginUserReducer,
  updatePasswordUserReducer,
  updateProfileUserReducer,
  updateUserReducer,
  userLoginReducer,
  usersListEmployeReducer,
  usersListReducer,
} from "./reducers/userReducers";
import {
  createNewOrganizerReducer,
  deleteOrganizerReducer,
  detailOrganizerReducer,
  getListOrganizerReducer,
  getListUserOrganizerReducer,
  updateOrganizerReducer,
  usersListOrganizerReducer,
} from "./reducers/organizerReducres";
import {
  deleteOrderReducer,
  getDetailOrderReducer,
  getListOrderReducer,
} from "./reducers/orderReducers";
import {
  deleteTicketReducer,
  getListTicketReducer,
} from "./reducers/ticketReducers";
import {
  createNewCategoryReducer,
  deleteCategoryReducer,
  detailCategoryReducer,
  getListCategoriesReducer,
  updateCategoryReducer,
} from "./reducers/categpryReducers";
import {
  getDashboardInfoReducer,
  getDashboardOrderStatsReducer,
} from "./reducers/dashboardReducers";

const reducer = combineReducers({
  userLogin: userLoginReducer,

  //
  usersList: usersListReducer,
  createNewUser: createNewUserReducer,
  getProfileUser: getProfileUserReducer,
  updateProfileUser: updateProfileUserReducer,
  deleteUser: deleteUserReducer,
  updatePasswordUser: updatePasswordUserReducer,
  updateLastLoginUser: updateLastLoginUserReducer,
  getDetailUser: getDetailUserReducer,
  updateUser: updateUserReducer,
  // employes
  usersListEmploye: usersListEmployeReducer,

  // organizers
  getListOrganizerReducer: getListOrganizerReducer,
  getListUserOrganizer: getListUserOrganizerReducer,
  createNewOrganizer: createNewOrganizerReducer,
  detailOrganizer: detailOrganizerReducer,
  updateOrganizer: updateOrganizerReducer,
  deleteOrganizer: deleteOrganizerReducer,

  // order
  getListOrder: getListOrderReducer,
  getDetailOrder: getDetailOrderReducer,
  deleteOrder: deleteOrderReducer,

  // tickets
  getListTicket: getListTicketReducer,
  deleteTicket: deleteTicketReducer,

  // categories
  getListCategories: getListCategoriesReducer,
  createNewCategory: createNewCategoryReducer,
  detailCategory: detailCategoryReducer,
  updateCategory: updateCategoryReducer,
  deleteCategory: deleteCategoryReducer,
  // dash
  getDashboardInfo: getDashboardInfoReducer,
  getDashboardOrderStats: getDashboardOrderStatsReducer,
});

const userInfoFromStorage = localStorage.getItem("userInfoAylinkBackfront")
  ? JSON.parse(localStorage.getItem("userInfoAylinkBackfront"))
  : null;

const initialState = {
  userLogin: { userInfo: userInfoFromStorage },
};

const middleware = [thunk];

const store = createStore(
  reducer,
  initialState,
  applyMiddleware(...middleware)
);

export default store;
