import { toast } from "react-toastify";
import {
  CATEGORY_ADD_SUCCESS,
  CATEGORY_ADD_REQUEST,
  CATEGORY_ADD_FAIL,
  //
  CATEGORY_LIST_SUCCESS,
  CATEGORY_LIST_REQUEST,
  CATEGORY_LIST_FAIL,
  //
  CATEGORY_DETAIL_SUCCESS,
  CATEGORY_DETAIL_REQUEST,
  CATEGORY_DETAIL_FAIL,
  //
  CATEGORY_DELETE_SUCCESS,
  CATEGORY_DELETE_REQUEST,
  CATEGORY_DELETE_FAIL,
  //
  CATEGORY_UPDATE_SUCCESS,
  CATEGORY_UPDATE_REQUEST,
  CATEGORY_UPDATE_FAIL,
  //

  //
} from "../constants/categoryConstants";

export const deleteCategoryReducer = (state = {}, action) => {
  switch (action.type) {
    case CATEGORY_DELETE_REQUEST:
      return { loadingCategoryDelete: true };
    case CATEGORY_DELETE_SUCCESS:
      toast.success("This Category has been successfully deleted.");
      return {
        loadingCategoryDelete: false,
        successCategoryDelete: true,
      };
    case CATEGORY_DELETE_FAIL:
      return {
        loadingCategoryDelete: false,
        errorCategoryDelete: action.payload,
        successCategoryDelete: false,
      };
    default:
      return state;
  }
};

export const updateCategoryReducer = (state = {}, action) => {
  switch (action.type) {
    case CATEGORY_UPDATE_REQUEST:
      return { loadingCategoryUpdate: true };
    case CATEGORY_UPDATE_SUCCESS:
      toast.success("This Category has been successfully updated");
      return {
        loadingCategoryUpdate: false,
        successCategoryUpdate: true,
      };
    case CATEGORY_UPDATE_FAIL:
      return {
        loadingCategoryUpdate: false,
        errorCategoryUpdate: action.payload,
        successCategoryUpdate: false,
      };
    default:
      return state;
  }
};

export const detailCategoryReducer = (state = { categoryInfo: {} }, action) => {
  switch (action.type) {
    case CATEGORY_DETAIL_REQUEST:
      return { loadingCategoryInfo: true };
    case CATEGORY_DETAIL_SUCCESS:
      return {
        loadingCategoryInfo: false,
        successCategoryInfo: true,
        categoryInfo: action.payload.category,
      };
    case CATEGORY_DETAIL_FAIL:
      return {
        loadingCategoryInfo: false,
        successCategoryInfo: false,
        errorCategoryInfo: action.payload,
      };
    default:
      return state;
  }
};

export const createNewCategoryReducer = (state = {}, action) => {
  switch (action.type) {
    case CATEGORY_ADD_REQUEST:
      return { loadingCategoryAdd: true };
    case CATEGORY_ADD_SUCCESS:
      toast.success("This category has been added successfully");
      return {
        loadingCategoryAdd: false,
        successCategoryAdd: true,
      };
    case CATEGORY_ADD_FAIL:
      toast.error(action.payload);
      return {
        loadingCategoryAdd: false,
        successCategoryAdd: false,
        errorCategoryAdd: action.payload,
      };
    default:
      return state;
  }
};

export const getListCategoriesReducer = (
  state = { categories: [] },
  action
) => {
  switch (action.type) {
    case CATEGORY_LIST_REQUEST:
      return { loadingListCategories: true, categories: [] };
    case CATEGORY_LIST_SUCCESS:
      return {
        loadingListCategories: false,
        categories: action.payload.categories,
      };
    case CATEGORY_LIST_FAIL:
      return {
        loadingListCategories: false,
        errorListCategories: action.payload,
      };
    default:
      return state;
  }
};
