import { toast } from "react-toastify";
import {
  TICKET_ADD_SUCCESS,
  TICKET_ADD_REQUEST,
  TICKET_ADD_FAIL,
  //
  TICKET_LIST_SUCCESS,
  TICKET_LIST_REQUEST,
  TICKET_LIST_FAIL,
  //
  TICKET_DETAIL_SUCCESS,
  TICKET_DETAIL_REQUEST,
  TICKET_DETAIL_FAIL,
  //
  TICKET_DELETE_SUCCESS,
  TICKET_DELETE_REQUEST,
  TICKET_DELETE_FAIL,
  //
  TICKET_UPDATE_SUCCESS,
  TICKET_UPDATE_REQUEST,
  TICKET_UPDATE_FAIL,
  //

  //
} from "../constants/ticketConstants";

export const deleteTicketReducer = (state = {}, action) => {
  switch (action.type) {
    case TICKET_DELETE_REQUEST:
      return { loadingTicketDelete: true };
    case TICKET_DELETE_SUCCESS:
      toast.success("This Ticket has been successfully deleted.");
      return {
        loadingTicketDelete: false,
        successTicketDelete: true,
      };
    case TICKET_DELETE_FAIL:
      return {
        loadingTicketDelete: false,
        errorTicketDelete: action.payload,
        successTicketDelete: false,
      };
    default:
      return state;
  }
};

export const getListTicketReducer = (state = { tickets: [] }, action) => {
  switch (action.type) {
    case TICKET_LIST_REQUEST:
      return { loadingListTickets: true, tickets: [] };
    case TICKET_LIST_SUCCESS:
      return {
        loadingListTickets: false,
        tickets: action.payload.tickets,
        pages: action.payload.pages,
        page: action.payload.page,
      };
    case TICKET_LIST_FAIL:
      return {
        loadingListTickets: false,
        errorListTickets: action.payload,
      };
    default:
      return state;
  }
};
