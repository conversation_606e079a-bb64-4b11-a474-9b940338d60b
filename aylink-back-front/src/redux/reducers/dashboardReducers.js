import { toast } from "react-toastify";
import {
  DASHBOARD_LIST_SUCCESS,
  DASHBOARD_LIST_REQUEST,
  DASHBOARD_LIST_FAIL,
  //
  DASHBOARD_ORDER_LIST_SUCCESS,
  DASHBOARD_ORDER_LIST_REQUEST,
  DASHBOARD_ORDER_LIST_FAIL,
  //

  //
} from "../constants/dashboardConstants";

export const getDashboardOrderStatsReducer = (
  state = { orders: [] },
  action
) => {
  switch (action.type) {
    case DASHBOARD_ORDER_LIST_REQUEST:
      return { loadingDashboardOrderStats: true, orders: [] };
    case DASHBOARD_ORDER_LIST_SUCCESS:
      return {
        loadingDashboardOrderStats: false,
        orders: action.payload.orders,
        pages: action.payload.pages,
        page: action.payload.page,
      };
    case DASHBOARD_ORDER_LIST_FAIL:
      return {
        loadingDashboardOrderStats: false,
        errorDashboardOrderStats: action.payload,
        successDashboardOrderStats: false,
      };
    default:
      return state;
  }
};

export const getDashboardInfoReducer = (state = { dashInfo: {} }, action) => {
  switch (action.type) {
    case DASHBOARD_LIST_REQUEST:
      return { loadingDashboardInfo: true };
    case DASHBOARD_LIST_SUCCESS:
      return {
        loadingDashboardInfo: false,
        dashInfo: action.payload,
        successDashboardInfo: true,
      };
    case DASHBOARD_LIST_FAIL:
      return {
        loadingDashboardInfo: false,
        errorDashboardInfo: action.payload,
        successDashboardInfo: false,
      };
    default:
      return state;
  }
};
