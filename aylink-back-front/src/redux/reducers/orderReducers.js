import { toast } from "react-toastify";
import {
  ORDER_ADD_SUCCESS,
  ORDER_ADD_REQUEST,
  ORDER_ADD_FAIL,
  //
  ORDER_LIST_SUCCESS,
  ORDER_LIST_REQUEST,
  ORDER_LIST_FAIL,
  //
  ORDER_DETAIL_SUCCESS,
  ORDER_DETAIL_REQUEST,
  ORDER_DETAIL_FAIL,
  //
  ORDER_DELETE_SUCCESS,
  ORDER_DELETE_REQUEST,
  ORDER_DELETE_FAIL,
  //
  ORDER_UPDATE_SUCCESS,
  ORDER_UPDATE_REQUEST,
  ORDER_UPDATE_FAIL,
  //

  //
} from "../constants/orderConstants";

export const deleteOrderReducer = (state = {}, action) => {
  switch (action.type) {
    case ORDER_DELETE_REQUEST:
      return { loadingOrderDelete: true };
    case ORDER_DELETE_SUCCESS:
      toast.success("This Order has been successfully deleted.");
      return {
        loadingOrderDelete: false,
        successOrderDelete: true,
      };
    case ORDER_DELETE_FAIL:
      return {
        loadingOrderDelete: false,
        errorOrderDelete: action.payload,
        successOrderDelete: false,
      };
    default:
      return state;
  }
};

export const getDetailOrderReducer = (state = { orderDetail: {} }, action) => {
  switch (action.type) {
    case ORDER_DETAIL_REQUEST:
      return { loadingOrderDetail: true };
    case ORDER_DETAIL_SUCCESS:
      return {
        loadingOrderDetail: false,
        orderDetail: action.payload,
        successOrderDetail: true,
      };
    case ORDER_DETAIL_FAIL:
      return {
        loadingOrderDetail: false,
        errorOrderDetail: action.payload,
        successOrderDetail: false,
      };
    default:
      return state;
  }
};

export const getListOrderReducer = (state = { orders: [] }, action) => {
  switch (action.type) {
    case ORDER_LIST_REQUEST:
      return { loadingListOrders: true, orders: [] };
    case ORDER_LIST_SUCCESS:
      return {
        loadingListOrders: false,
        orders: action.payload.orders,
        pages: action.payload.pages,
        page: action.payload.page,
      };
    case ORDER_LIST_FAIL:
      return {
        loadingListOrders: false,
        errorListOrders: action.payload,
      };
    default:
      return state;
  }
};
