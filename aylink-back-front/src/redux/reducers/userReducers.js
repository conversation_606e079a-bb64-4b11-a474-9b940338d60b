import { toast } from "react-toastify";
import {
  USER_LOGIN_REQUEST,
  USER_LOGIN_SUCCESS,
  USER_LOGIN_FAIL,
  USER_LOGOUT,
  //
  USER_ADD_SUCCESS,
  USER_ADD_REQUEST,
  USER_ADD_FAIL,
  //
  USER_LIST_SUCCESS,
  USER_LIST_REQUEST,
  USER_LIST_FAIL,
  //
  USER_PROFILE_SUCCESS,
  USER_PROFILE_REQUEST,
  USER_PROFILE_FAIL,
  //
  USER_PROFILE_UPDATE_SUCCESS,
  USER_PROFILE_UPDATE_REQUEST,
  USER_PROFILE_UPDATE_FAIL,
  //
  USER_PASSWORD_UPDATE_SUCCESS,
  USER_PASSWORD_UPDATE_REQUEST,
  USER_PASSWORD_UPDATE_FAIL,
  //
  USER_DELETE_SUCCESS,
  USER_DELETE_REQUEST,
  USER_DELETE_FAIL,
  //
  USER_DETAIL_SUCCESS,
  USER_DETAIL_REQUEST,
  USER_DETAIL_FAIL,
  //
  USER_UPDATE_SUCCESS,
  USER_UPDATE_REQUEST,
  USER_UPDATE_FAIL,
  //
  USER_UPDATE_LOGIN_SUCCESS,
  USER_UPDATE_LOGIN_REQUEST,
  USER_UPDATE_LOGIN_FAIL,
  //
  USER_HISTORY_LOGED_SUCCESS,
  USER_HISTORY_LOGED_REQUEST,
  USER_HISTORY_LOGED_FAIL,
  //
  USER_HISTORY_SUCCESS,
  USER_HISTORY_REQUEST,
  USER_HISTORY_FAIL,
  //
  USER_EMPLOYE_LIST_REQUEST,
  USER_EMPLOYE_LIST_SUCCESS,
  USER_EMPLOYE_LIST_FAIL,
  //
} from "../constants/userConstants";

export const updateUserReducer = (state = {}, action) => {
  switch (action.type) {
    case USER_UPDATE_REQUEST:
      return { loadingUserUpdate: true };
    case USER_UPDATE_SUCCESS:
      toast.success("This User has been updated successfully.");
      return {
        loadingUserUpdate: false,
        successUserUpdate: true,
      };
    case USER_UPDATE_FAIL:
      toast.error(action.payload);
      return {
        loadingUserUpdate: false,
        successUserUpdate: false,
        errorUserUpdate: action.payload,
      };
    default:
      return state;
  }
};

export const getDetailUserReducer = (state = { userDetail: {} }, action) => {
  switch (action.type) {
    case USER_DETAIL_REQUEST:
      return { loadingUserDetail: true };
    case USER_DETAIL_SUCCESS:
      return {
        loadingUserDetail: false,
        userDetail: action.payload,
        successUserDetail: true,
      };
    case USER_DETAIL_FAIL:
      return {
        loadingUserDetail: false,
        errorUserDetail: action.payload,
        successUserDetail: false,
      };
    default:
      return state;
  }
};

// get list employes
export const usersListEmployeReducer = (
  state = { listEmployes: [] },
  action
) => {
  switch (action.type) {
    case USER_EMPLOYE_LIST_REQUEST:
      return { loadingListEmployes: true, listEmployes: [] };
    case USER_EMPLOYE_LIST_SUCCESS:
      return {
        loadingListEmployes: false,
        listEmployes: action.payload.users,
      };
    case USER_EMPLOYE_LIST_FAIL:
      return {
        loadingListEmployes: false,
        errorListEmployes: action.payload,
      };
    default:
      return state;
  }
};

// last

export const historyListCoordinatorReducer = (
  state = { historyCoordinator: [] },
  action
) => {
  switch (action.type) {
    case USER_HISTORY_REQUEST:
      return { loadingHistoryCoordinator: true, historyCoordinator: [] };
    case USER_HISTORY_SUCCESS:
      return {
        loadingHistoryCoordinator: false,
        historyCoordinator: action.payload.historys,
        pages: action.payload.pages,
        page: action.payload.page,
      };
    case USER_HISTORY_FAIL:
      return {
        loadingHistoryCoordinator: false,
        errorHistoryCoordinator: action.payload,
      };
    default:
      return state;
  }
};

export const historyListLoggedReducer = (
  state = { historyLogged: [] },
  action
) => {
  switch (action.type) {
    case USER_HISTORY_LOGED_REQUEST:
      return { loadingHistoryLogged: true, historyLogged: [] };
    case USER_HISTORY_LOGED_SUCCESS:
      return {
        loadingHistoryLogged: false,
        historyLogged: action.payload.historys,
        pages: action.payload.pages,
        page: action.payload.page,
      };
    case USER_HISTORY_LOGED_FAIL:
      return {
        loadingHistoryLogged: false,
        errorHistoryLogged: action.payload,
      };
    default:
      return state;
  }
};

export const updateLastLoginUserReducer = (state = {}, action) => {
  switch (action.type) {
    case USER_UPDATE_LOGIN_REQUEST:
      return { loadingUpdateLastLogin: true };
    case USER_UPDATE_LOGIN_SUCCESS:
      return {
        loadingUpdateLastLogin: false,
        successUpdateLastLogin: true,
      };
    case USER_UPDATE_LOGIN_FAIL:
      return {
        loadingUpdateLastLogin: false,
        successUpdateLastLogin: false,
        errorUpdateLastLogin: action.payload,
      };
    default:
      return state;
  }
};

export const updatePasswordUserReducer = (state = {}, action) => {
  switch (action.type) {
    case USER_PASSWORD_UPDATE_REQUEST:
      return { loadingUserPasswordUpdate: true };
    case USER_PASSWORD_UPDATE_SUCCESS:
      toast.success("Your password has been successfully updated");
      return {
        loadingUserPasswordUpdate: false,
        successUserPasswordUpdate: true,
      };
    case USER_PASSWORD_UPDATE_FAIL:
      return {
        loadingUserPasswordUpdate: false,
        errorUserPasswordUpdate: action.payload,
        successUserPasswordUpdate: false,
      };
    default:
      return state;
  }
};

export const deleteUserReducer = (state = {}, action) => {
  switch (action.type) {
    case USER_DELETE_REQUEST:
      return { loadingUserDelete: true };
    case USER_DELETE_SUCCESS:
      toast.success("This Coordinator has been successfully deleted.");
      return {
        loadingUserDelete: false,
        successUserDelete: true,
      };
    case USER_DELETE_FAIL:
      return {
        loadingUserDelete: false,
        errorUsersDelete: action.payload,
        successUserDelete: false,
      };
    default:
      return state;
  }
};

export const updateProfileUserReducer = (state = {}, action) => {
  switch (action.type) {
    case USER_PROFILE_UPDATE_REQUEST:
      return { loadingUserProfileUpdate: true };
    case USER_PROFILE_UPDATE_SUCCESS:
      toast.success("Your profile has been successfully updated");
      return {
        loadingUserProfileUpdate: false,
        successUserProfileUpdate: true,
      };
    case USER_PROFILE_UPDATE_FAIL:
      return {
        loadingUserProfileUpdate: false,
        errorUserProfileUpdate: action.payload,
        successUserProfileUpdate: false,
      };
    default:
      return state;
  }
};

export const getProfileUserReducer = (state = { userProfile: [] }, action) => {
  switch (action.type) {
    case USER_PROFILE_REQUEST:
      return { loadingUserProfile: true };
    case USER_PROFILE_SUCCESS:
      return {
        loadingUserProfile: false,
        userProfile: action.payload.profile,
        successUserProfile: true,
      };
    case USER_PROFILE_FAIL:
      return {
        loadingUserProfile: false,
        errorUserProfile: action.payload,
        successUserProfile: false,
      };
    default:
      return state;
  }
};

export const createNewUserReducer = (state = {}, action) => {
  switch (action.type) {
    case USER_ADD_REQUEST:
      return { loadingUserAdd: true };
    case USER_ADD_SUCCESS:
      toast.success("This user has been added successfully");
      return {
        loadingUserAdd: false,
        successUserAdd: true,
      };
    case USER_ADD_FAIL:
      toast.error(action.payload);
      return {
        loadingUserAdd: false,
        successUserAdd: false,
        errorUserAdd: action.payload,
      };
    default:
      return state;
  }
};

export const usersListReducer = (state = { users: [] }, action) => {
  switch (action.type) {
    case USER_LIST_REQUEST:
      return { loadingUsers: true, users: [] };
    case USER_LIST_SUCCESS:
      return {
        loadingUsers: false,
        users: action.payload.users,
        pages: action.payload.pages,
        page: action.payload.page,
      };
    case USER_LIST_FAIL:
      return { loadingUsers: false, errorUsers: action.payload };
    default:
      return state;
  }
};

export const userLoginReducer = (state = {}, action) => {
  switch (action.type) {
    case USER_LOGIN_REQUEST:
      return { loading: true };
    case USER_LOGIN_SUCCESS:
      return { loading: false, userInfo: action.payload };
    case USER_LOGIN_FAIL:
      return { loading: false, error: action.payload };
    case USER_LOGOUT:
      return {};
    default:
      return state;
  }
};
