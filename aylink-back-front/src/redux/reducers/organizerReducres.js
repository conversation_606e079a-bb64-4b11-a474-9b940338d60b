import { toast } from "react-toastify";
import {
  ORGANIZER_ADD_SUCCESS,
  ORGANIZER_ADD_REQUEST,
  ORGANIZER_ADD_FAIL,
  //
  ORGANIZER_LIST_SUCCESS,
  ORGANIZER_LIST_REQUEST,
  ORGANIZER_LIST_FAIL,
  //
  ORGANIZER_DETAIL_SUCCESS,
  ORGANIZER_DETAIL_REQUEST,
  ORGANIZER_DETAIL_FAIL,
  //
  ORGANIZER_DELETE_SUCCESS,
  ORGANIZER_DELETE_REQUEST,
  ORGANIZER_DELETE_FAIL,
  //
  ORGANIZER_UPDATE_SUCCESS,
  ORGANIZER_UPDATE_REQUEST,
  ORGANIZER_UPDATE_FAIL,
  //
  ORGANIZER_USER_LIST_SUCCESS,
  ORGANIZER_USER_LIST_REQUEST,
  ORGANIZER_USER_LIST_FAIL,

  //
} from "../constants/organizerConstants";

export const deleteOrganizerReducer = (state = {}, action) => {
  switch (action.type) {
    case ORGANIZER_DELETE_REQUEST:
      return { loadingOrganizerDelete: true };
    case ORGANIZER_DELETE_SUCCESS:
      toast.success("This Organizer has been successfully deleted.");
      return {
        loadingOrganizerDelete: false,
        successOrganizerDelete: true,
      };
    case ORGANIZER_DELETE_FAIL:
      return {
        loadingOrganizerDelete: false,
        errorOrganizerDelete: action.payload,
        successOrganizerDelete: false,
      };
    default:
      return state;
  }
};

export const updateOrganizerReducer = (state = {}, action) => {
  switch (action.type) {
    case ORGANIZER_UPDATE_REQUEST:
      return { loadingOrganizerUpdate: true };
    case ORGANIZER_UPDATE_SUCCESS:
      toast.success("This Organizer has been successfully updated");
      return {
        loadingOrganizerUpdate: false,
        successOrganizerUpdate: true,
      };
    case ORGANIZER_UPDATE_FAIL:
      return {
        loadingOrganizerUpdate: false,
        errorOrganizerUpdate: action.payload,
        successOrganizerUpdate: false,
      };
    default:
      return state;
  }
};

export const detailOrganizerReducer = (
  state = { organizerInfo: {} },
  action
) => {
  switch (action.type) {
    case ORGANIZER_DETAIL_REQUEST:
      return { loadingOrganizerInfo: true };
    case ORGANIZER_DETAIL_SUCCESS:
      return {
        loadingOrganizerInfo: false,
        successOrganizerInfo: true,
        organizerInfo: action.payload.organizer,
      };
    case ORGANIZER_DETAIL_FAIL:
      return {
        loadingOrganizerInfo: false,
        successOrganizerInfo: false,
        errorOrganizerInfo: action.payload,
      };
    default:
      return state;
  }
};

export const createNewOrganizerReducer = (state = {}, action) => {
  switch (action.type) {
    case ORGANIZER_ADD_REQUEST:
      return { loadingOrganizerAdd: true };
    case ORGANIZER_ADD_SUCCESS:
      toast.success("This organizer has been added successfully");
      return {
        loadingOrganizerAdd: false,
        successOrganizerAdd: true,
      };
    case ORGANIZER_ADD_FAIL:
      toast.error(action.payload);
      return {
        loadingOrganizerAdd: false,
        successOrganizerAdd: false,
        errorOrganizerAdd: action.payload,
      };
    default:
      return state;
  }
};

export const getListUserOrganizerReducer = (state = { users: [] }, action) => {
  switch (action.type) {
    case ORGANIZER_USER_LIST_REQUEST:
      return { loadingListUserOrganizers: true, users: [] };
    case ORGANIZER_USER_LIST_SUCCESS:
      return {
        loadingListUserOrganizers: false,
        users: action.payload.users,
        pages: action.payload.pages,
        page: action.payload.page,
      };
    case ORGANIZER_USER_LIST_FAIL:
      return {
        loadingListUserOrganizers: false,
        errorListUserOrganizers: action.payload,
      };
    default:
      return state;
  }
};

export const getListOrganizerReducer = (state = { organizers: [] }, action) => {
  switch (action.type) {
    case ORGANIZER_LIST_REQUEST:
      return { loadingListOrganizers: true, organizers: [] };
    case ORGANIZER_LIST_SUCCESS:
      return {
        loadingListOrganizers: false,
        organizers: action.payload.organizers,
        pages: action.payload.pages,
        page: action.payload.page,
      };
    case ORGANIZER_LIST_FAIL:
      return {
        loadingListOrganizers: false,
        errorListOrganizers: action.payload,
      };
    default:
      return state;
  }
};
