import axios from "../../axios";
import {
  USER_LOGIN_REQUEST,
  USER_LOGIN_SUCCESS,
  USER_LOGIN_FAIL,
  USER_LOGOUT,
  //
  USER_ADD_SUCCESS,
  USER_ADD_REQUEST,
  USER_ADD_FAIL,
  //
  USER_LIST_SUCCESS,
  USER_LIST_REQUEST,
  USER_LIST_FAIL,
  //
  USER_DETAIL_SUCCESS,
  USER_DETAIL_REQUEST,
  USER_DETAIL_FAIL,
  //
  USER_PROFILE_SUCCESS,
  USER_PROFILE_REQUEST,
  USER_PROFILE_FAIL,
  //
  USER_PROFILE_UPDATE_SUCCESS,
  USER_PROFILE_UPDATE_REQUEST,
  USER_PROFILE_UPDATE_FAIL,
  //
  USER_PASSWORD_UPDATE_SUCCESS,
  USER_PASSWORD_UPDATE_REQUEST,
  USER_PASSWORD_UPDATE_FAIL,
  //
  USER_DELETE_SUCCESS,
  USER_DELETE_REQUEST,
  USER_DELETE_FAIL,
  //
  USER_UPDATE_SUCCESS,
  USER_UPDATE_REQUEST,
  USER_UPDATE_FAIL,
  //
  COORDINATOR_DETAIL_SUCCESS,
  COORDINATOR_DETAIL_REQUEST,
  COORDINATOR_DETAIL_FAIL,
  //
  COORDINATOR_UPDATE_SUCCESS,
  COORDINATOR_UPDATE_REQUEST,
  COORDINATOR_UPDATE_FAIL,
  //
  USER_UPDATE_LOGIN_SUCCESS,
  USER_UPDATE_LOGIN_REQUEST,
  USER_UPDATE_LOGIN_FAIL,
  //
  USER_HISTORY_LOGED_SUCCESS,
  USER_HISTORY_LOGED_REQUEST,
  USER_HISTORY_LOGED_FAIL,
  //
  USER_HISTORY_SUCCESS,
  USER_HISTORY_REQUEST,
  USER_HISTORY_FAIL,
  //
  USER_EMPLOYE_LIST_REQUEST,
  USER_EMPLOYE_LIST_SUCCESS,
  USER_EMPLOYE_LIST_FAIL,
  //
} from "../constants/userConstants";

// update user

export const updateUser = (id, user) => async (dispatch, getState) => {
  try {
    dispatch({
      type: USER_UPDATE_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.put(`/users/update-user/${id}/`, user, config);

    dispatch({
      type: USER_UPDATE_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_UPDATE_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

//
export const getUserDetail = (id) => async (dispatch, getState) => {
  try {
    dispatch({
      type: USER_DETAIL_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.get(`/users/detail/` + id + "/", config);

    dispatch({
      type: USER_DETAIL_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_DETAIL_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

// get list Employe
export const getListEmployesUsers =
  (service = "") =>
  async (dispatch, getState) => {
    try {
      dispatch({
        type: USER_EMPLOYE_LIST_REQUEST,
      });
      var {
        userLogin: { userInfo },
      } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userInfo.access}`,
        },
      };
      const { data } = await axios.get(
        `/users/employes/?service=${service}`,
        config
      );

      dispatch({
        type: USER_EMPLOYE_LIST_SUCCESS,
        payload: data,
      });
    } catch (error) {
      var err =
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail;
      if (err) {
        if (
          err === "Given token not valid for any token type" ||
          err === "User not found"
        ) {
          localStorage.removeItem("userInfoAylinkBackfront");
          document.location.href = "/";
        }
      }
      dispatch({
        type: USER_EMPLOYE_LIST_FAIL,
        payload:
          error.response && error.response.data.detail
            ? error.response.data.detail
            : error.detail,
      });
    }
  };

// last

export const getHistoryListCoordinator =
  (page, coordinator) => async (dispatch, getState) => {
    try {
      dispatch({
        type: USER_HISTORY_REQUEST,
      });
      var {
        userLogin: { userInfo },
      } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userInfo.access}`,
        },
      };
      const { data } = await axios.get(
        `/users/get-history-coordinator/${coordinator}/?page=${page}`,
        config
      );

      dispatch({
        type: USER_HISTORY_SUCCESS,
        payload: data,
      });
    } catch (error) {
      var err =
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail;
      if (err) {
        if (
          err === "Given token not valid for any token type" ||
          err === "User not found"
        ) {
          localStorage.removeItem("userInfoAylinkBackfront");
          document.location.href = "/";
        }
      }
      dispatch({
        type: USER_HISTORY_FAIL,
        payload:
          error.response && error.response.data.detail
            ? error.response.data.detail
            : error.detail,
      });
    }
  };

export const getHistoryListLogged = (page) => async (dispatch, getState) => {
  try {
    dispatch({
      type: USER_HISTORY_LOGED_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.get(
      `/users/get-history-byloged/?page=${page}`,
      config
    );

    dispatch({
      type: USER_HISTORY_LOGED_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_HISTORY_LOGED_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

export const updateLastLogin = () => async (dispatch, getState) => {
  try {
    dispatch({
      type: USER_UPDATE_LOGIN_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.put(`/users/update-login-time/`, {}, config);

    dispatch({
      type: USER_UPDATE_LOGIN_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_UPDATE_LOGIN_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

export const updateCoordinator =
  (id, coordinator) => async (dispatch, getState) => {
    try {
      dispatch({
        type: COORDINATOR_UPDATE_REQUEST,
      });
      var {
        userLogin: { userInfo },
      } = getState();
      const config = {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `Bearer ${userInfo.access}`,
        },
      };
      const { data } = await axios.put(
        `/users/coordinator-update/${id}/`,
        coordinator,
        config
      );

      dispatch({
        type: COORDINATOR_UPDATE_SUCCESS,
        payload: data,
      });
    } catch (error) {
      var err =
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail;
      if (err) {
        if (
          err === "Given token not valid for any token type" ||
          err === "User not found"
        ) {
          localStorage.removeItem("userInfoAylinkBackfront");
          document.location.href = "/";
        }
      }
      dispatch({
        type: COORDINATOR_UPDATE_FAIL,
        payload:
          error.response && error.response.data.detail
            ? error.response.data.detail
            : error.detail,
      });
    }
  };

export const getCoordinatorDetail = (id) => async (dispatch, getState) => {
  try {
    dispatch({
      type: COORDINATOR_DETAIL_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.get(`/users/coordinator/` + id, config);

    dispatch({
      type: COORDINATOR_DETAIL_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: COORDINATOR_DETAIL_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

export const updateUserPassword = (user) => async (dispatch, getState) => {
  try {
    dispatch({
      type: USER_PASSWORD_UPDATE_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.put(`/users/update-password/`, user, config);

    dispatch({
      type: USER_PASSWORD_UPDATE_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_PASSWORD_UPDATE_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : "Votre profile n'a pas été modifié, réessayez",
    });
  }
};

export const deleteUser = (id) => async (dispatch, getState) => {
  try {
    dispatch({
      type: USER_DELETE_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.delete(`/users/new-delete/${id}/`, config);

    dispatch({
      type: USER_DELETE_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_DELETE_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : "Votre profile n'a pas été modifié, réessayez",
    });
  }
};

export const updateUserProfile = (user) => async (dispatch, getState) => {
  try {
    dispatch({
      type: USER_PROFILE_UPDATE_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.put(`/users/update-profile/`, user, config);

    dispatch({
      type: USER_PROFILE_UPDATE_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_PROFILE_UPDATE_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : "Votre profile n'a pas été modifié, réessayez",
    });
  }
};

export const getUserProfile = () => async (dispatch, getState) => {
  try {
    dispatch({
      type: USER_PROFILE_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.get(`/users/profile/`, config);

    dispatch({
      type: USER_PROFILE_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_PROFILE_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

export const addNewUser = (user) => async (dispatch, getState) => {
  try {
    dispatch({
      type: USER_ADD_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.post(`/users/create-new-user/`, user, config);

    dispatch({
      type: USER_ADD_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_ADD_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : "This user has not been added, please try again.",
    });
  }
};

export const getListUsers = (page) => async (dispatch, getState) => {
  try {
    dispatch({
      type: USER_LIST_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.get(`/users/new-list/?page=${page}`, config);

    dispatch({
      type: USER_LIST_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_LIST_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

export const login = (username, password) => async (dispatch) => {
  try {
    dispatch({
      type: USER_LOGIN_REQUEST,
    });
    const config = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    const { data } = await axios.post(
      "/users/login/",
      {
        email: username,
        password: password,
      },
      config
    );
    if (data.role === "1") {
      dispatch({
        type: USER_LOGIN_SUCCESS,
        payload: data,
      });
      localStorage.setItem("userInfoAylinkBackfront", JSON.stringify(data));
    } else {
      dispatch({
        type: USER_LOGIN_FAIL,
        payload: "Email or Password is invalied",
      });
    }
  } catch (error) {
    console.log(error);

    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: USER_LOGIN_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

export const logout = () => (dispatch) => {
  localStorage.removeItem("userInfoAylinkBackfront");
  dispatch({ type: USER_LOGOUT });
  document.location.href = "/";
};
