import axios from "../../axios";
import {
  CATEGORY_ADD_SUCCESS,
  CATEGORY_ADD_REQUEST,
  CATEGORY_ADD_FAIL,
  //
  CATEGORY_LIST_SUCCESS,
  CATEGORY_LIST_REQUEST,
  CATEGORY_LIST_FAIL,
  //
  CATEGORY_DETAIL_SUCCESS,
  CATEGORY_DETAIL_REQUEST,
  CATEGORY_DETAIL_FAIL,
  //
  CATEGORY_DELETE_SUCCESS,
  CATEGORY_DELETE_REQUEST,
  CATEGORY_DELETE_FAIL,
  //
  CATEGORY_UPDATE_SUCCESS,
  CATEGORY_UPDATE_REQUEST,
  CATEGORY_UPDATE_FAIL,
  //

  //
} from "../constants/categoryConstants";

// delete category
export const deleteCategory = (id) => async (dispatch, getState) => {
  try {
    dispatch({
      type: CATEGORY_DELETE_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.delete(`/categories/delete/${id}/`, config);

    dispatch({
      type: CATEGORY_DELETE_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: CATEGORY_DELETE_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : "Votre profile n'a pas été modifié, réessayez",
    });
  }
};

// create category
export const updateCategory = (id, category) => async (dispatch, getState) => {
  try {
    dispatch({
      type: CATEGORY_UPDATE_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.put(
      `/categories/update/${id}/`,
      category,
      config
    );

    dispatch({
      type: CATEGORY_UPDATE_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: CATEGORY_UPDATE_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : "This user has not been added, please try again.",
    });
  }
};

// get category detail
export const getCategoryDetail = (id) => async (dispatch, getState) => {
  try {
    dispatch({
      type: CATEGORY_DETAIL_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.get(`/categories/detail/${id}/`, config);

    dispatch({
      type: CATEGORY_DETAIL_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (err === "Given token not valid for any token type") {
        localStorage.removeItem("userInfoUnimedCare");
        document.location.href = "/";
      }
    }
    dispatch({
      type: CATEGORY_DETAIL_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

// create category
export const addNewCategory = (category) => async (dispatch, getState) => {
  try {
    dispatch({
      type: CATEGORY_ADD_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.post(`/categories/create/`, category, config);

    dispatch({
      type: CATEGORY_ADD_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: CATEGORY_ADD_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : "This user has not been added, please try again.",
    });
  }
};

// get all organizers
export const getListCategories = (page) => async (dispatch, getState) => {
  try {
    dispatch({
      type: CATEGORY_LIST_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.get(`/categories/?page=${page}`, config);

    dispatch({
      type: CATEGORY_LIST_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: CATEGORY_LIST_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};
