import axios from "../../axios";
import {
  DASHBOARD_LIST_SUCCESS,
  DASHBOARD_LIST_REQUEST,
  DASHBOARD_LIST_FAIL,
  //
  DASHBOARD_ORDER_LIST_SUCCESS,
  DASHBOARD_ORDER_LIST_REQUEST,
  DASHBOARD_ORDER_LIST_FAIL,
  //

  //
} from "../constants/dashboardConstants";

export const getDashboardOrdersStats =
  (organizer = "", page = "0", startDate = "", endDate = "") =>
  async (dispatch, getState) => {
    try {
      dispatch({
        type: DASHBOARD_ORDER_LIST_REQUEST,
      });
      var {
        userLogin: { userInfo },
      } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userInfo.access}`,
        },
      };
      const { data } = await axios.get(
        `/stats-admin/orders-stats/?organizerId=${organizer}&startdate=${startDate}&enddate=${endDate}&page=${page}`,
        config
      );

      dispatch({
        type: DASHBOARD_ORDER_LIST_SUCCESS,
        payload: data,
      });
    } catch (error) {
      var err =
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail;
      if (err) {
        if (
          err === "Given token not valid for any token type" ||
          err === "User not found"
        ) {
          localStorage.removeItem("userInfoAylinkBackfront");
          document.location.href = "/";
        }
      }
      dispatch({
        type: DASHBOARD_ORDER_LIST_FAIL,
        payload:
          error.response && error.response.data.detail
            ? error.response.data.detail
            : error.detail,
      });
    }
  };

export const getDashboardInfo =
  (organizer, startDate = "", endDate = "") =>
  async (dispatch, getState) => {
    try {
      dispatch({
        type: DASHBOARD_LIST_REQUEST,
      });
      var {
        userLogin: { userInfo },
      } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userInfo.access}`,
        },
      };
      const { data } = await axios.get(
        `/stats-admin/?organizerId=${organizer}&startdate=${startDate}&enddate=${endDate}`,
        config
      );

      dispatch({
        type: DASHBOARD_LIST_SUCCESS,
        payload: data,
      });
    } catch (error) {
      var err =
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail;
      if (err) {
        if (
          err === "Given token not valid for any token type" ||
          err === "User not found"
        ) {
          localStorage.removeItem("userInfoAylinkBackfront");
          document.location.href = "/";
        }
      }
      dispatch({
        type: DASHBOARD_LIST_FAIL,
        payload:
          error.response && error.response.data.detail
            ? error.response.data.detail
            : error.detail,
      });
    }
  };
