import axios from "../../axios";
import {
  ORGANIZER_ADD_SUCCESS,
  ORGANIZER_ADD_REQUEST,
  ORGANIZER_ADD_FAIL,
  //
  ORGANIZER_LIST_SUCCESS,
  ORGANIZER_LIST_REQUEST,
  ORGANIZER_LIST_FAIL,
  //
  ORGANIZER_DETAIL_SUCCESS,
  ORGANIZER_DETAIL_REQUEST,
  ORGANIZER_DETAIL_FAIL,
  //
  ORGANIZER_DELETE_SUCCESS,
  ORGANIZER_DELETE_REQUEST,
  ORGANIZER_DELETE_FAIL,
  //
  ORGANIZER_UPDATE_SUCCESS,
  ORGANIZER_UPDATE_REQUEST,
  ORGANIZER_UPDATE_FAIL,
  //
  ORGANIZER_USER_LIST_SUCCESS,
  ORGANIZER_USER_LIST_REQUEST,
  ORGANIZER_USER_LIST_FAIL,

  //
} from "../constants/organizerConstants";

// delete organizer
export const deleteOrganizer = (id) => async (dispatch, getState) => {
  try {
    dispatch({
      type: ORGANIZER_DELETE_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.delete(`/organizers/delete/${id}/`, config);

    dispatch({
      type: ORGANIZER_DELETE_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: ORGANIZER_DELETE_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : "Votre profile n'a pas été modifié, réessayez",
    });
  }
};

export const updateOrganizer =
  (id, organizer) => async (dispatch, getState) => {
    try {
      dispatch({
        type: ORGANIZER_UPDATE_REQUEST,
      });
      var {
        userLogin: { userInfo },
      } = getState();
      const config = {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `Bearer ${userInfo.access}`,
        },
      };
      const { data } = await axios.put(
        `/organizers/update/${id}/`,
        organizer,
        config
      );

      dispatch({
        type: ORGANIZER_UPDATE_SUCCESS,
        payload: data,
      });
    } catch (error) {
      var err =
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail;
      if (err) {
        if (
          err === "Given token not valid for any token type" ||
          err === "User not found"
        ) {
          localStorage.removeItem("userInfoAylinkBackfront");
          document.location.href = "/";
        }
      }
      dispatch({
        type: ORGANIZER_UPDATE_FAIL,
        payload:
          error.response && error.response.data.detail
            ? error.response.data.detail
            : "This user has not been added, please try again.",
      });
    }
  };

export const getOrganizerDetail = (id) => async (dispatch, getState) => {
  try {
    dispatch({
      type: ORGANIZER_DETAIL_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.get(`/organizers/detail/${id}/`, config);

    dispatch({
      type: ORGANIZER_DETAIL_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (err === "Given token not valid for any token type") {
        localStorage.removeItem("userInfoUnimedCare");
        document.location.href = "/";
      }
    }
    dispatch({
      type: ORGANIZER_DETAIL_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

export const addNewOrganizer = (organizer) => async (dispatch, getState) => {
  try {
    dispatch({
      type: ORGANIZER_ADD_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.post(
      `/organizers/create-organizer/`,
      organizer,
      config
    );

    dispatch({
      type: ORGANIZER_ADD_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: ORGANIZER_ADD_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : "This user has not been added, please try again.",
    });
  }
};

export const getListUserOrganizers = (page) => async (dispatch, getState) => {
  try {
    dispatch({
      type: ORGANIZER_USER_LIST_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.get(
      `/organizers/list-users/?page=${page}`,
      config
    );

    dispatch({
      type: ORGANIZER_USER_LIST_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: ORGANIZER_USER_LIST_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};

// get all organizers
export const getListOrganizers = (page) => async (dispatch, getState) => {
  try {
    dispatch({
      type: ORGANIZER_LIST_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.get(
      `/organizers/list-organizers/?page=${page}`,
      config
    );

    dispatch({
      type: ORGANIZER_LIST_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: ORGANIZER_LIST_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail,
    });
  }
};
