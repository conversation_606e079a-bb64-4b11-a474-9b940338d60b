import axios from "../../axios";
import {
  TICKET_ADD_SUCCESS,
  TICKET_ADD_REQUEST,
  TICKET_ADD_FAIL,
  //
  TICKET_LIST_SUCCESS,
  TICKET_LIST_REQUEST,
  TICKET_LIST_FAIL,
  //
  TICKET_DETAIL_SUCCESS,
  TICKET_DETAIL_REQUEST,
  TICKET_DETAIL_FAIL,
  //
  TICKET_DELETE_SUCCESS,
  TICKET_DELETE_REQUEST,
  TICKET_DELETE_FAIL,
  //
  TICKET_UPDATE_SUCCESS,
  TICKET_UPDATE_REQUEST,
  TICKET_UPDATE_FAIL,
  //

  //
} from "../constants/ticketConstants";

// delete ticket
export const deleteTicket = (id) => async (dispatch, getState) => {
  try {
    dispatch({
      type: TICKET_DELETE_REQUEST,
    });
    var {
      userLogin: { userInfo },
    } = getState();
    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.access}`,
      },
    };
    const { data } = await axios.delete(`/tickets/delete/${id}/`, config);

    dispatch({
      type: TICKET_DELETE_SUCCESS,
      payload: data,
    });
  } catch (error) {
    var err =
      error.response && error.response.data.detail
        ? error.response.data.detail
        : error.detail;
    if (err) {
      if (
        err === "Given token not valid for any token type" ||
        err === "User not found"
      ) {
        localStorage.removeItem("userInfoAylinkBackfront");
        document.location.href = "/";
      }
    }
    dispatch({
      type: TICKET_DELETE_FAIL,
      payload:
        error.response && error.response.data.detail
          ? error.response.data.detail
          : "Votre profile n'a pas été modifié, réessayez",
    });
  }
};

// get all organizers

export const getListTickets =
  (page, organizer = "", search = "", status = "") =>
  async (dispatch, getState) => {
    try {
      dispatch({
        type: TICKET_LIST_REQUEST,
      });
      var {
        userLogin: { userInfo },
      } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userInfo.access}`,
        },
      };
      const { data } = await axios.get(
        `/tickets/admin-json-data/?page=${page}&organizer=${organizer}&q=${search}&status=${status}`,
        config
      );

      dispatch({
        type: TICKET_LIST_SUCCESS,
        payload: data,
      });
    } catch (error) {
      var err =
        error.response && error.response.data.detail
          ? error.response.data.detail
          : error.detail;
      if (err) {
        if (
          err === "Given token not valid for any token type" ||
          err === "User not found"
        ) {
          localStorage.removeItem("userInfoAylinkBackfront");
          document.location.href = "/";
        }
      }
      dispatch({
        type: TICKET_LIST_FAIL,
        payload:
          error.response && error.response.data.detail
            ? error.response.data.detail
            : error.detail,
      });
    }
  };
