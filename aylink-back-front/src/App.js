import "./App.css";
import "./axios.js";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import LoginScreen from "./screens/auth/LoginScreen";
import LogoutScreen from "./screens/auth/LogoutScreen.js";
import DashboardScreen from "./screens/dashboard/DashboardScreen.js";
import SettingsScreen from "./screens/settings/SettingsScreen.js";

import UserScreen from "./screens/users/UserScreen.js";
import AddUserScreen from "./screens/users/AddUserScreen.js";
import EditUserScreen from "./screens/users/EditUserScreen.js";
import OrganizerScreen from "./screens/organizers/OrganizerScreen.js";
import AddOrganizerScreen from "./screens/organizers/AddOrganizerScreen.js";
import EditOrganizerScreen from "./screens/organizers/EditOrganizerScreen.js";
import OrderScreen from "./screens/orders/OrderScreen.js";
import OrderDetailScreen from "./screens/orders/OrderDetailScreen.js";
import TicketScreen from "./screens/tickets/TicketScreen.js";
import CategoryScreen from "./screens/category/CategoryScreen.js";
import AddCategoryScreen from "./screens/category/AddCategoryScreen.js";
import EditCategoryScreen from "./screens/category/EditCategoryScreen.js";

const router = createBrowserRouter([
  {
    path: "/",
    element: <LoginScreen />,
  },
  {
    path: "/dashboard",
    element: <DashboardScreen />,
  },

  // users
  {
    path: "/users-space",
    element: <UserScreen />,
  },
  {
    path: "/users-space/new-user",
    element: <AddUserScreen />,
  },
  {
    path: "/users-space/edit/:id",
    element: <EditUserScreen />,
  },
  // organizers
  {
    path: "/organizers",
    element: <OrganizerScreen />,
  },
  {
    path: "/organizers/new-organizer",
    element: <AddOrganizerScreen />,
  },
  {
    path: "/organizers/edit/:id",
    element: <EditOrganizerScreen />,
  },
  // categories
  {
    path: "/categories",
    element: <CategoryScreen />,
  },
  {
    path: "/categories/new-category",
    element: <AddCategoryScreen />,
  },
  {
    path: "/categories/edit/:id",
    element: <EditCategoryScreen />,
  },
  //
  {
    path: "/orders",
    element: <OrderScreen />,
  },
  {
    path: "/orders/detail/:id",
    element: <OrderDetailScreen />,
  },
  //
  {
    path: "/tickets",
    element: <TicketScreen />,
  },

  //
  {
    path: "/settings",
    element: <SettingsScreen />,
  },

  {
    path: "/logout",
    element: <LogoutScreen />,
  },
]);

function App() {
  return <RouterProvider router={router} />;
}

export default App;
