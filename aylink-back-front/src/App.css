.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.card-bg {
  background: linear-gradient(
    253.99deg,
    #03023c 8.38%,
    rgba(3, 2, 60, 0.604952) 33.67%,
    rgba(3, 2, 60, 0.94) 92.23%
  );
}

.react-datepicker__input-container input,
.react-datepicker-wrapper {
  outline: none;
  border: 1px solid #f1f3ff;
  padding: 0.5rem 1rem;
  width: 100%;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}
