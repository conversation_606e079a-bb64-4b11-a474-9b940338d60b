import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import DefaultLayout from "../../layouts/DefaultLayout";
import {
  addNewOrganizer,
  getListUserOrganizers,
} from "../../redux/actions/organizerActions";
import { getListCategories } from "../../redux/actions/categoryActions";

import Select from "react-select";
import { toast } from "react-toastify";

function AddOrganizerScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();

  //
  const [userSelect, setUserSelect] = useState("");
  const [userSelectError, setUserSelectError] = useState("");

  const [isDaily, setIsDaily] = useState("False");
  const [isDailyError, setIsDailyError] = useState("");

  const [maxDate, setMaxDate] = useState("");
  const [maxDateError, setMaxDateError] = useState("");

  const [organizerName, setOrganizerName] = useState("");
  const [organizerNameError, setOrganizerNameError] = useState("");

  const [showInMail, setShowInMail] = useState("False");
  const [showInMailError, setShowInMailError] = useState("");

  const [categorySelect, setCategorySelect] = useState("");
  const [categorySelectError, setCategorySelectError] = useState("");

  const [organizerAvatar, setOrganizerAvatar] = useState("");
  const [organizerAvatarValue, setOrganizerAvatarValue] = useState("");
  const [organizerAvatarError, setOrganizerAvatarError] = useState("");

  const [organizerLogoTicket, setOrganizerLogoTicket] = useState("");
  const [organizerLogoTicketValue, setOrganizerLogoTicketValue] = useState("");
  const [organizerLogoTicketError, setOrganizerLogoTicketError] = useState("");

  const [organizerImage, setOrganizerImage] = useState("");
  const [organizerImageValue, setOrganizerImageValue] = useState("");
  const [organizerImageError, setOrganizerImageError] = useState("");

  const [organizerCover, setOrganizerCover] = useState("");
  const [organizerCoverValue, setOrganizerCoverValue] = useState("");
  const [organizerCoverError, setOrganizerCoverError] = useState("");

  const [organizerAbout, setOrganizerAbout] = useState("");
  const [organizerAboutError, setOrganizerAboutError] = useState("");

  const [organizerStatus, setOrganizerStatus] = useState("True");
  const [organizerStatusError, setOrganizerStatusError] = useState("");

  const [organizerPercent, setOrganizerPercent] = useState(10);
  const [organizerPercentError, setOrganizerPercentError] = useState("");

  const [organizerDateFrom, setOrganizerDateFrom] = useState("");
  const [organizerDateFromError, setOrganizerDateFromError] = useState("");

  const [organizerDateTo, setOrganizerDateTo] = useState("");
  const [organizerDateToError, setOrganizerDateToError] = useState("");

    const [isTicketCustomization, setIsTicketCustomization] = useState("False");
    const [isTicketCustomizationError, setIsTicketCustomizationError] = useState("");

  //
  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  const listUserOrganizers = useSelector((state) => state.getListUserOrganizer);
  const { users, loadingListUserOrganizers, errorListUserOrganizers, pages } =
    listUserOrganizers;

  const listCategories = useSelector((state) => state.getListCategories);
  const { categories, loadingListCategories, errorListCategories } =
    listCategories;

  const organizerAdd = useSelector((state) => state.createNewOrganizer);
  const { loadingOrganizerAdd, errorOrganizerAdd, successOrganizerAdd } =
    organizerAdd;

  const redirect = "/";

  useEffect(() => {
    if (!userInfo) {
      navigate(redirect);
    } else {
      dispatch(getListUserOrganizers("0"));
      dispatch(getListCategories("0"));
    }
  }, [navigate, userInfo, dispatch]);

  useEffect(() => {
    if (successOrganizerAdd) {
      setUserSelect("");
      setUserSelectError("");

      setOrganizerName("");
      setOrganizerNameError("");

      setCategorySelect("");
      setCategorySelectError("");

      setOrganizerAvatar("");
      setOrganizerAvatarValue("");
      setOrganizerAvatarError("");

      setOrganizerLogoTicket("");
      setOrganizerLogoTicketValue("");
      setOrganizerLogoTicketError("");

      setOrganizerImage("");
      setOrganizerImageValue("");
      setOrganizerImageError("");

      setOrganizerCover("");
      setOrganizerCoverValue("");
      setOrganizerCoverError("");

      setOrganizerAbout("");
      setOrganizerAboutError("");

      setOrganizerStatus("True");
      setOrganizerStatusError("");

      setOrganizerPercent(10);
      setOrganizerPercentError("");

      setOrganizerDateFrom("");
      setOrganizerDateFromError("");

      setOrganizerDateTo("");
      setOrganizerDateToError("");
      setIsDaily("False");
      setIsDailyError("");

      setIsTicketCustomization("False");
      setIsTicketCustomizationError("");

      setMaxDate("");
      setMaxDateError("");

      setShowInMail(false);
    }
  }, [successOrganizerAdd]);

  return (
    <DefaultLayout>
      <div>
        <div className="flex flex-row text-sm items-center my-1">
          {/* home */}
          <a href="/dashboard">
            <div className="flex flex-row  items-center hover:text-black ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
              <span className="mx-1">Dashboard</span>
            </div>
          </a>
          <a href="/categories">
            <div className="flex flex-row  items-center hover:text-black ">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  className="w-4 h-4"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="m8.25 4.5 7.5 7.5-7.5 7.5"
                  />
                </svg>
              </span>
              <div className="">Organizers List</div>
            </div>
          </a>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
              />
            </svg>
          </span>
          <div className="">Create New Organizer</div>
        </div>
        {/*  */}
        <div className="py-5 px-4 flex justify-between">
          <h4 className=" uppercase font-semibold text-black dark:text-white">
            New Organizer
          </h4>
        </div>
        {/*  */}
        <div className="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
          <div className="my-2 bg-white py-4 px-2 rounded-md">
            {/*  */}
            <div className="flex md:flex-row flex-col ">
              <div className="md:w-1/2 w-full md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  User <strong className="text-danger">*</strong>
                </div>
                <div>
                  <Select
                    value={userSelect}
                    onChange={(option) => {
                      setUserSelect(option);
                    }}
                    options={[
                      { value: "", label: "Select a User" }, // Add this as the first item
                      ...users?.map((user) => ({
                        value: user.id,
                        label: `${user.first_name} ${user.last_name}`,
                      })),
                    ]}
                    filterOption={(option, inputValue) =>
                      option.label
                        .toLowerCase()
                        .includes(inputValue.toLowerCase())
                    }
                    className="text-sm"
                    placeholder="Select a User..."
                    isSearchable
                    styles={{
                      control: (base, state) => ({
                        ...base,
                        background: "#fff",
                        border: userSelectError
                          ? "1px solid #d34053"
                          : "1px solid #b1b4c4",
                        boxShadow: state.isFocused ? "none" : "none",
                        "&:hover": {
                          border: "1px solid #b1b4c4",
                        },
                      }),
                      option: (base) => ({
                        ...base,
                        display: "flex",
                        alignItems: "center",
                      }),
                      singleValue: (base) => ({
                        ...base,
                        display: "flex",
                        alignItems: "center",
                      }),
                    }}
                  />

                  <div className=" text-[8px] text-danger">
                    {userSelectError ? userSelectError : ""}
                  </div>
                </div>
              </div>
              {/*  */}
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Is Daily ?
                </div>
                <div>
                  <select
                    value={isDaily}
                    onChange={(v) => setIsDaily(v.target.value)}
                    className={` outline-none border ${
                      isDailyError ? "border-danger" : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                  >
                    <option value={"False"}>No</option>
                    <option value={"True"}>Yes</option>
                  </select>
                  <div className=" text-[8px] text-danger">
                    {isDailyError ? isDailyError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}
            <div className="flex md:flex-row flex-col  ">
              <div className="w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Organizer Name <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      organizerNameError ? "border-danger" : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="text"
                    placeholder="Organizer Name"
                    value={organizerName}
                    onChange={(v) => setOrganizerName(v.target.value)}
                  />
                  <div className=" text-[8px] text-danger">
                    {organizerNameError ? organizerNameError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}
            <div className="flex md:flex-row flex-col ">
              <div className="md:w-1/2 w-full md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Organizer Category <strong className="text-danger">*</strong>
                </div>
                <div>
                  <Select
                    value={categorySelect}
                    onChange={(option) => {
                      setCategorySelect(option);
                    }}
                    options={[
                      { value: "", label: "Select a Category" }, // Add this as the first item
                      ...categories?.map((category) => ({
                        value: category.id,
                        label: category.name,
                      })),
                    ]}
                    filterOption={(option, inputValue) =>
                      option.label
                        .toLowerCase()
                        .includes(inputValue.toLowerCase())
                    }
                    className="text-sm"
                    placeholder="Select a Category..."
                    isSearchable
                    styles={{
                      control: (base, state) => ({
                        ...base,
                        background: "#fff",
                        border: categorySelectError
                          ? "1px solid #d34053"
                          : "1px solid #b1b4c4",
                        boxShadow: state.isFocused ? "none" : "none",
                        "&:hover": {
                          border: "1px solid #b1b4c4",
                        },
                      }),
                      option: (base) => ({
                        ...base,
                        display: "flex",
                        alignItems: "center",
                      }),
                      singleValue: (base) => ({
                        ...base,
                        display: "flex",
                        alignItems: "center",
                      }),
                    }}
                  />

                  <div className=" text-[8px] text-danger">
                    {categorySelectError ? categorySelectError : ""}
                  </div>
                </div>
              </div>
              {/*  */}
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Is Ticket Customization ?
                </div>
                <div>
                  <select
                    value={isTicketCustomization}
                    onChange={(v) => setIsTicketCustomization(v.target.value)}
                    className={` outline-none border ${
                      isTicketCustomizationError ? "border-danger" : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                  >
                    <option value={"False"}>No</option>
                    <option value={"True"}>Yes</option>
                  </select>
                  <div className=" text-[8px] text-danger">
                    {isTicketCustomizationError ? isTicketCustomizationError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}
            <div className="flex md:flex-row flex-col  ">
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Status <strong className="text-danger">*</strong>
                </div>
                <div>
                  <select
                    value={organizerStatus}
                    onChange={(v) => setOrganizerStatus(v.target.value)}
                    className={` outline-none border ${
                      organizerStatusError
                        ? "border-danger"
                        : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                  >
                    <option value={""}>-----</option>
                    <option value={"True"}>Active</option>
                    <option value={"False"}>No Active</option>
                  </select>
                  <div className=" text-[8px] text-danger">
                    {organizerStatusError ? organizerStatusError : ""}
                  </div>
                </div>
              </div>
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Send Mail <strong className="text-danger">*</strong>
                </div>
                <div>
                  <select
                    value={showInMail}
                    onChange={(v) => setShowInMail(v.target.value)}
                    className={` outline-none border ${
                      showInMailError ? "border-danger" : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                  >
                    <option value={""}>-----</option>
                    <option value={"True"}>Send</option>
                    <option value={"False"}>No Send</option>
                  </select>
                  <div className=" text-[8px] text-danger">
                    {showInMailError ? showInMailError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}
            <div className="flex md:flex-row flex-col  ">
              <div className="w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Percent fees % <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      organizerPercentError
                        ? "border-danger"
                        : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="number"
                    min={0}
                    max={100}
                    step={0.01}
                    placeholder="Percent fees"
                    value={organizerPercent}
                    onChange={(v) => setOrganizerPercent(v.target.value)}
                  />
                  <div className=" text-[8px] text-danger">
                    {organizerPercentError ? organizerPercentError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}
            <div className="flex md:flex-row flex-col  ">
              <div className="w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Organizer About <strong className="text-danger">*</strong>
                </div>
                <div>
                  <textarea
                    className={` outline-none border ${
                      organizerAboutError ? "border-danger" : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="text"
                    placeholder="Organizer About"
                    value={organizerAbout}
                    rows={5}
                    onChange={(v) => setOrganizerAbout(v.target.value)}
                  ></textarea>
                  <div className=" text-[8px] text-danger">
                    {organizerAboutError ? organizerAboutError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}
            <div className="flex md:flex-row flex-col  ">
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Avatar <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      organizerAvatarError
                        ? "border-danger"
                        : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="file"
                    placeholder="Organizer Avatar"
                    value={organizerAvatarValue}
                    onChange={(v) => {
                      setOrganizerAvatar(v.target.files[0]);
                      setOrganizerAvatarValue(v.target.value);
                    }}
                  />
                  <div className=" text-[8px] text-danger">
                    {organizerAvatarError ? organizerAvatarError : ""}
                  </div>
                </div>
              </div>
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Logo Ticket <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      organizerLogoTicketError
                        ? "border-danger"
                        : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="file"
                    placeholder="Organizer Ticket logo"
                    value={organizerLogoTicketValue}
                    onChange={(v) => {
                      setOrganizerLogoTicket(v.target.files[0]);
                      setOrganizerLogoTicketValue(v.target.value);
                    }}
                  />
                  <div className=" text-[8px] text-danger">
                    {organizerLogoTicketError ? organizerLogoTicketError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}
            <div className="flex md:flex-row flex-col  ">
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Organizer Image <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      organizerImageError ? "border-danger" : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="file"
                    placeholder="Organizer Image"
                    value={organizerImageValue}
                    onChange={(v) => {
                      setOrganizerImage(v.target.files[0]);
                      setOrganizerImageValue(v.target.value);
                    }}
                  />
                  <div className=" text-[8px] text-danger">
                    {organizerImageError ? organizerImageError : ""}
                  </div>
                </div>
              </div>
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Organizer Cover <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      organizerCoverError ? "border-danger" : "border-[#b1b4c4]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="file"
                    placeholder="Organizer Cover"
                    value={organizerCoverValue}
                    onChange={(v) => {
                      setOrganizerCover(v.target.files[0]);
                      setOrganizerCoverValue(v.target.value);
                    }}
                  />
                  <div className=" text-[8px] text-danger">
                    {organizerCoverError ? organizerCoverError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}
            {isDaily === "False" ? (
              <div className="flex md:flex-row flex-col  ">
                <div className="md:w-1/2 w-full  md:pr-1 my-1">
                  <div className="text-[#000000bf] font-bold text-xs  mb-1">
                    Organizer Date From{" "}
                    <strong className="text-danger">*</strong>
                  </div>
                  <div>
                    <input
                      className={` outline-none border ${
                        organizerDateFromError
                          ? "border-danger"
                          : "border-[#b1b4c4]"
                      } px-3 py-2 w-full rounded text-sm`}
                      type={"datetime-local"}
                      placeholder="date from"
                      value={organizerDateFrom}
                      onChange={(v) => {
                        setOrganizerDateFrom(v.target.value);
                      }}
                    />
                    <div className=" text-[8px] text-danger">
                      {organizerDateFromError ? organizerDateFromError : ""}
                    </div>
                  </div>
                </div>
                <div className="md:w-1/2 w-full  md:pr-1 my-1">
                  <div className="text-[#000000bf] font-bold text-xs  mb-1">
                    Organizer Date To <strong className="text-danger">*</strong>
                  </div>
                  <div>
                    <input
                      className={` outline-none border ${
                        organizerDateToError
                          ? "border-danger"
                          : "border-[#b1b4c4]"
                      } px-3 py-2 w-full rounded text-sm`}
                      type={"datetime-local"}
                      placeholder="date To"
                      value={organizerDateTo}
                      onChange={(v) => {
                        setOrganizerDateTo(v.target.value);
                      }}
                    />
                    <div className=" text-[8px] text-danger">
                      {organizerDateToError ? organizerDateToError : ""}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex md:flex-row flex-col  ">
                <div className="md:w-1/2 w-full  md:pr-1 my-1">
                  <div className="text-[#000000bf] font-bold text-xs  mb-1">
                    Organizer Date Max{" "}
                    <strong className="text-danger">*</strong>
                  </div>
                  <div>
                    <input
                      className={` outline-none border ${
                        maxDateError ? "border-danger" : "border-[#b1b4c4] "
                      } px-3 py-2 w-full rounded text-sm`}
                      type={"date"}
                      placeholder="Organizer Date Max"
                      value={maxDate}
                      min={new Date().toISOString().split("T")[0]}
                      onChange={(v) => {
                        setMaxDate(v.target.value);
                      }}
                    />
                    <div className=" text-[8px] text-danger">
                      {maxDateError ? maxDateError : ""}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/*  */}
            <div className="my-3 ">
              <div className="flex flex-row items-center justify-end my-3">
                <a
                  href="/organizers"
                  className="bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3"
                >
                  Back
                </a>
                <button
                  onClick={async () => {
                    var check = true;

                    setUserSelectError("");
                    setOrganizerNameError("");
                    setCategorySelectError("");

                    setOrganizerAvatarError("");
                    setOrganizerLogoTicketError("");
                    setOrganizerImageError("");
                    setOrganizerCoverError("");

                    setOrganizerAboutError("");
                    setOrganizerStatusError("");
                    setOrganizerPercentError("");
                    setOrganizerDateFromError("");
                    setOrganizerDateToError("");

                    setIsTicketCustomizationError("");

                    setIsDaily("");
                    setMaxDateError("");

                    setShowInMailError("");

                    if (isDaily === "False" && organizerDateTo === "") {
                      setOrganizerDateToError("These fields are required.");
                      check = false;
                    }

                    if (isDaily === "False" && organizerDateFrom === "") {
                      setOrganizerDateFromError("These fields are required.");
                      check = false;
                    }
                    if (isDaily === "True" && maxDate === "") {
                      setMaxDate("These fields are required.");
                      check = false;
                    }

                    if (organizerPercent === "") {
                      setOrganizerPercentError("These fields are required.");
                      check = false;
                    } else {
                      const isFloat =
                        !isNaN(organizerPercent) &&
                        parseFloat(organizerPercent) ===
                          Number(organizerPercent);
                      // Check if it is between 0 and 100
                      const inRange =
                        isFloat &&
                        organizerPercent >= 0 &&
                        organizerPercent <= 100;

                      if (!inRange) {
                        setOrganizerPercentError("These fields are not valid.");
                      }
                    }

                    if (organizerStatus === "") {
                      setOrganizerStatusError("These fields are required.");
                      check = false;
                    }

                    if (showInMail === "") {
                      showInMailError("These fields are required.");
                      check = false;
                    }

                    if (organizerAbout === "") {
                      setOrganizerAboutError("These fields are required.");
                      check = false;
                    }

                    if (organizerCoverValue === "") {
                      setOrganizerCoverError("These fields are required.");
                      check = false;
                    }

                    if (organizerImageValue === "") {
                      setOrganizerImageError("These fields are required.");
                      check = false;
                    }

                    if (organizerLogoTicketValue === "") {
                      setOrganizerLogoTicketError("These fields are required.");
                      check = false;
                    }

                    if (organizerAvatarValue === "") {
                      setOrganizerAvatarError("These fields are required.");
                      check = false;
                    }

                    if (userSelect === "" || userSelect.value === "") {
                      setUserSelectError("These fields are required.");
                      check = false;
                    }

                    if (isDaily !== "True" && isDaily !== "False") {
                      setIsDailyError("These fields are required.");
                      check = false;
                    }
                    if (categorySelect === "" || categorySelect.value === "") {
                      setCategorySelectError("These fields are required.");
                      check = false;
                    }
                    if (organizerName === "") {
                      setOrganizerNameError("These fields are required.");
                      check = false;
                    }

                    if (check) {
                      // setLoadEvent(true);
                      await dispatch(
                        addNewOrganizer({
                          organizer_name: organizerName,
                          category_select: categorySelect.value ?? "",
                          user_select: userSelect.value ?? "",
                          organizer_avatar: organizerAvatar,
                          logo_ticket: organizerLogoTicket,
                          organizer_image: organizerImage,
                          organizer_cover: organizerCover,
                          organizer_about: organizerAbout,
                          organizer_status: organizerStatus,
                          show_mail: showInMail,
                          organizer_percent: organizerPercent,
                          date_from:
                            isDaily === "False" ? organizerDateFrom : "",
                          date_to: isDaily === "False" ? organizerDateTo : "",
                          is_daily: isDaily,
                          max_date: isDaily === "False" ? "" : maxDate,
                          isticket_customization: isTicketCustomization,
                        })
                      ).then(() => {});
                      // setLoadEvent(false);
                    } else {
                      toast.error(
                        "Some fields are empty or invalid. please try again"
                      );
                    }
                  }}
                  className="text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full"
                >
                  {loadingOrganizerAdd ? "Loading ..." : "Create Organizer"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
}

export default AddOrganizerScreen;
