import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import DefaultLayout from "../../layouts/DefaultLayout";
import { addNewUser } from "../../redux/actions/userActions";
import { toast } from "react-toastify";

function AddUserScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  const [loadEvent, setLoadEvent] = useState(false);

  const [firstName, setFirstName] = useState("");
  const [firstNameError, setFirstNameError] = useState("");

  const [lastName, setLastName] = useState("");
  const [lastNameError, setLastNameError] = useState("");
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState("");
  const [phone, setPhone] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [role, setRole] = useState("");
  const [roleError, setRoleError] = useState("");
  const [password, setPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");

  const [confirmPassword, setConfirmPassword] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");

  const [selectServices, setSelectServices] = useState([]);
  const [selectServicesError, setSelectServicesError] = useState("");

  const [cityServices, setCityServices] = useState("");
  const [cityServicesError, setCityServicesError] = useState("");

  const [logo, setLogo] = useState("");
  const [logoValue, setLogoValue] = useState("");
  const [logoError, setLogoError] = useState("");

  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo, loading, error } = userLogin;

  const userAdd = useSelector((state) => state.createNewUser);
  const { loadingUserAdd, errorUserAdd, successUserAdd } = userAdd;

  const redirect = "/";
  useEffect(() => {
    if (!userInfo) {
      navigate(redirect);
    }
  }, [navigate, userInfo, dispatch]);

  useEffect(() => {
    if (successUserAdd) {
      setFirstName("");
      setFirstNameError("");
      setLastName("");
      setLastNameError("");
      setPassword("");
      setPasswordError("");
      setConfirmPassword("");
      setConfirmPasswordError("");
      setEmail("");
      setEmailError("");
      setPhone("");
      setPhoneError("");
      setRole("");
      setRoleError("");

      setLogo("");
      setLogoError("");
      setLogoValue("");
      setCityServices("");
      setCityServicesError("");
      setSelectServices([]);
      setSelectServicesError("");
    }
  }, [successUserAdd]);

  return (
    <DefaultLayout>
      <div>
        <div className="flex flex-row text-sm items-center my-1">
          {/* home */}
          <a href="/dashboard">
            <div className="flex flex-row  items-center hover:text-black ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
              <span className="mx-1">Dashboard</span>
            </div>
          </a>
          <a href="/users-space">
            <div className="flex flex-row  items-center hover:text-black ">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  className="w-4 h-4"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="m8.25 4.5 7.5 7.5-7.5 7.5"
                  />
                </svg>
              </span>
              <div className="">Users Space</div>
            </div>
          </a>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
              />
            </svg>
          </span>
          <div className="">Create New User</div>
        </div>
        {/*  */}
        <div className="py-5 px-4 flex justify-between">
          <h4 className=" uppercase font-semibold text-black dark:text-white">
            New User
          </h4>
        </div>
        {/*  */}
        <div className="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
          <div className="my-2 bg-white py-4 px-2 rounded-md">
            <div className="flex md:flex-row flex-col  ">
              <div className=" w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Role <strong className="text-danger">*</strong>
                </div>
                <div>
                  <select
                    className={` outline-none border ${
                      roleError ? "border-danger" : "border-[#F1F3FF]"
                    } px-3 py-2 w-full rounded text-sm`}
                    value={role}
                    onChange={(v) => setRole(v.target.value)}
                  >
                    <option value={""}>Select Role</option>
                    <option value={"1"}>Admin</option>
                    <option value={"2"}>Organizer</option>
                  </select>

                  <div className=" text-[8px] text-danger">
                    {roleError ? roleError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}

            {/*  */}
            <div className="flex md:flex-row flex-col  ">
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  First Name <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      firstNameError ? "border-danger" : "border-[#F1F3FF]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="text"
                    placeholder="First Name"
                    value={firstName}
                    onChange={(v) => setFirstName(v.target.value)}
                  />
                  <div className=" text-[8px] text-danger">
                    {firstNameError ? firstNameError : ""}
                  </div>
                </div>
              </div>
              {/*  */}
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Last Name
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      lastNameError ? "border-danger" : "border-[#F1F3FF]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="text"
                    placeholder="Last Name"
                    value={lastName}
                    onChange={(v) => setLastName(v.target.value)}
                  />
                  <div className=" text-[8px] text-danger">
                    {lastNameError ? lastNameError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}
            <div className="flex md:flex-row flex-col  ">
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Email <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      emailError ? "border-danger" : "border-[#F1F3FF]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="email"
                    placeholder="User Email"
                    value={email}
                    onChange={(v) => setEmail(v.target.value)}
                  />
                  <div className=" text-[8px] text-danger">
                    {emailError ? emailError : ""}
                  </div>
                </div>
              </div>
              {/*  */}
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Phone <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      phoneError ? "border-danger" : "border-[#F1F3FF]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="text"
                    placeholder="User Phone"
                    value={phone}
                    onChange={(v) => setPhone(v.target.value)}
                  />
                  <div className=" text-[8px] text-danger">
                    {phoneError ? phoneError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}
            <div className="flex md:flex-row flex-col  ">
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Password <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      passwordError ? "border-danger" : "border-[#F1F3FF]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="password"
                    placeholder="User Password"
                    value={password}
                    onChange={(v) => setPassword(v.target.value)}
                  />
                  <div className=" text-[8px] text-danger">
                    {passwordError ? passwordError : ""}
                  </div>
                </div>
              </div>
              {/*  */}
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Confirm Password <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      confirmPasswordError
                        ? "border-danger"
                        : "border-[#F1F3FF]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="password"
                    placeholder="Confirm Password"
                    value={confirmPassword}
                    onChange={(v) => setConfirmPassword(v.target.value)}
                  />
                  <div className=" text-[8px] text-danger">
                    {confirmPasswordError ? confirmPasswordError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}

            <div className="my-3 ">
              <div className="flex flex-row items-center justify-end my-3">
                <a
                  href="/users-space"
                  className="bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3"
                >
                  Back
                </a>
                <button
                  onClick={async () => {
                    var check = true;
                    setRoleError("");
                    setFirstNameError("");
                    setLastNameError("");
                    setEmailError("");
                    setPhoneError("");
                    setConfirmPasswordError("");
                    setPasswordError("");
                    setLogoError("");
                    setSelectServicesError("");
                    setCityServicesError("");

                    if (role === "") {
                      setRoleError("These fields are required.");
                      check = false;
                    }

                    if (firstName === "") {
                      setFirstNameError("These fields are required.");
                      check = false;
                    }
                    if (email === "") {
                      setEmailError("These fields are required.");
                      check = false;
                    }
                    if (phone === "") {
                      setPhoneError("These fields are required.");
                      check = false;
                    }
                    if (password === "") {
                      setPasswordError("These fields are required.");
                      check = false;
                    }
                    if (password !== confirmPassword) {
                      setConfirmPasswordError("Please confirm password");
                      check = false;
                    }

                    if (check) {
                      setLoadEvent(true);
                      await dispatch(
                        addNewUser({
                          first_name: firstName,
                          last_name: lastName,
                          full_name: firstName + " " + lastName,
                          email: email,
                          phone: phone,
                          role: role,
                          password: password,
                        })
                      ).then(() => {});
                      setLoadEvent(false);
                    } else {
                      toast.error(
                        "Some fields are empty or invalid. please try again"
                      );
                    }
                  }}
                  className="text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full"
                >
                  {loadingUserAdd ? "Loading ..." : "Create User"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
}

export default AddUserScreen;
