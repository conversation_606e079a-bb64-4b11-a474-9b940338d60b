import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Link,
  useLocation,
  useNavigate,
  useSearchParams,
} from "react-router-dom";
import ConfirmationModal from "../../components/ConfirmationModal";
import Paginate from "../../components/Paginate";
import Alert from "../../components/Alert";
import Loader from "../../components/Loader";
import DefaultLayout from "../../layouts/DefaultLayout";

import "leaflet/dist/leaflet.css";
import { getListOrganizers } from "../../redux/actions/organizerActions";
import {
  getDashboardInfo,
  getDashboardOrdersStats,
} from "../../redux/actions/dashboardActions";

import DatePicker from "react-datepicker";
import { format, addDays } from "date-fns";
import "react-datepicker/dist/react-datepicker.css";
import { getDashboardOrderStatsReducer } from "../../redux/reducers/dashboardReducers";

function DashboardScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const page = searchParams.get("page") || "1";
  const dispatch = useDispatch();

  const [organizerSelect, setOrganizerSelect] = useState(
    searchParams.get("organizer") || "all"
  );

  const [startDate, setStartDate] = useState(
    searchParams.get("startdate") || ""
  );
  const [endDate, setEndDate] = useState(searchParams.get("enddate") || "");

  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  const listOrganizers = useSelector((state) => state.getListOrganizerReducer);
  const { organizers, loadingListOrganizers, errorListOrganizers } =
    listOrganizers;

  const dashboardInfo = useSelector((state) => state.getDashboardInfo);
  const {
    loadingDashboardInfo,
    dashInfo,
    successDashboardInfo,
    errorDashboardInfo,
  } = dashboardInfo;

  const dashboardOrderStats = useSelector(
    (state) => state.getDashboardOrderStats
  );
  const {
    loadingDashboardOrderStats,
    orders,
    pages,
    successDashboardOrderStats,
    errorDashboardOrderStats,
  } = dashboardOrderStats;

  const redirect = "/";

  useEffect(() => {
    if (!userInfo) {
      navigate(redirect);
    } else {
      dispatch(getListOrganizers("0"));
      dispatch(
        getDashboardInfo(
          organizerSelect,
          startDate !== "" && startDate !== null && startDate !== undefined
            ? format(new Date(startDate), "yyyy-MM-dd")
            : "",
          endDate !== "" && endDate !== null && endDate !== undefined
            ? format(new Date(endDate), "yyyy-MM-dd")
            : ""
        )
      );
      dispatch(
        getDashboardOrdersStats(
          organizerSelect,
          page,
          startDate !== "" && startDate !== null && startDate !== undefined
            ? format(new Date(startDate), "yyyy-MM-dd")
            : "",
          endDate !== "" && endDate !== null && endDate !== undefined
            ? format(new Date(endDate), "yyyy-MM-dd")
            : ""
        )
      );
    }
  }, [navigate, userInfo, dispatch, page]);

  const formatDate = (dateString) => {
    if (dateString && dateString !== "") {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } else {
      return dateString;
    }
  };

  useEffect(() => {
    const params = new URLSearchParams();

    if (organizerSelect) params.set("organizer", organizerSelect);
    if (startDate)
      params.set(
        "startdate",
        startDate !== "" && startDate !== null && startDate !== undefined
          ? format(new Date(startDate), "yyyy-MM-dd")
          : ""
      );
    if (endDate)
      params.set(
        "enddate",
        endDate !== "" && endDate !== null && endDate !== undefined
          ? format(new Date(endDate), "yyyy-MM-dd")
          : ""
      );

    navigate({
      pathname: location.pathname,
      search: params.toString(),
    });
  }, [organizerSelect, startDate, endDate, navigate, location.pathname]);

  return (
    <DefaultLayout>
      <div>
        <div className="flex flex-row text-sm items-center my-1">
          {/* home */}
          <a href="/dashboard">
            <div className="flex flex-row  items-center hover:text-black ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
              <span className="mx-1">Dashboard</span>
            </div>
          </a>
        </div>
        {/*  */}
        <div className="flex flex-row justify-between  items-center my-3">
          <div className="mx-1 font-bold text-black ">Dashboard</div>
        </div>
        {/*  */}
        <div className="rounded-sm border border-stroke bg-white px-5 py-5 shadow-default   dark:bg-boxdark  my-3">
          <div className="container mx-auto flex md:flex-row flex-col">
            <div className="md:w-1/3 w-full px-2 py-1">
              <select
                className={` outline-none border border-[#F1F3FF]
                      px-3 py-2 w-full rounded text-sm`}
                value={organizerSelect}
                onChange={(v) => {
                  setOrganizerSelect(v.target.value);
                  // setStartDate("");
                  // setEndDate("");

                  dispatch(
                    getDashboardInfo(
                      v.target.value,
                      startDate !== "" &&
                        startDate !== null &&
                        startDate !== undefined
                        ? format(new Date(startDate), "yyyy-MM-dd")
                        : "",
                      endDate !== "" &&
                        endDate !== null &&
                        endDate !== undefined
                        ? format(new Date(endDate), "yyyy-MM-dd")
                        : ""
                    )
                  );
                  dispatch(
                    getDashboardOrdersStats(
                      v.target.value,
                      "1",
                      startDate !== "" &&
                        startDate !== null &&
                        startDate !== undefined
                        ? format(new Date(startDate), "yyyy-MM-dd")
                        : "",
                      endDate !== "" &&
                        endDate !== null &&
                        endDate !== undefined
                        ? format(new Date(endDate), "yyyy-MM-dd")
                        : ""
                    )
                  );
                }}
              >
                <option value={"all"}>All Organizers</option>
                {organizers?.map((organizer, index) => (
                  <option value={organizer._id}>{organizer.name}</option>
                ))}
              </select>
            </div>
            <div className="md:w-1/3 w-full px-2 py-1">
              <DatePicker
                selected={startDate}
                onChange={(date) => {
                  setStartDate(date);

                  dispatch(
                    getDashboardInfo(
                      organizerSelect,
                      date !== "" && date !== null && date !== undefined
                        ? format(new Date(date), "yyyy-MM-dd")
                        : "",
                      endDate !== "" &&
                        endDate !== null &&
                        endDate !== undefined
                        ? format(new Date(endDate), "yyyy-MM-dd")
                        : ""
                    )
                  );
                  dispatch(
                    getDashboardOrdersStats(
                      organizerSelect,
                      "1",
                      date !== "" && date !== null && date !== undefined
                        ? format(new Date(date), "yyyy-MM-dd")
                        : "",
                      endDate !== "" &&
                        endDate !== null &&
                        endDate !== undefined
                        ? format(new Date(endDate), "yyyy-MM-dd")
                        : ""
                    )
                  );
                }}
                selectsStart
                dateFormat="yyyy-MM-dd"
                startDate={startDate}
                endDate={endDate}
                placeholderText="Start Date"
              />
            </div>
            <div className="md:w-1/3 w-full px-2 py-1">
              <DatePicker
                disabled={
                  startDate === "" ||
                  startDate === null ||
                  startDate === undefined
                }
                selected={endDate}
                onChange={(date) => {
                  setEndDate(date);

                  dispatch(
                    getDashboardInfo(
                      organizerSelect,

                      startDate !== "" &&
                        startDate !== null &&
                        startDate !== undefined
                        ? format(new Date(startDate), "yyyy-MM-dd")
                        : "",
                      date !== "" && date !== null && date !== undefined
                        ? format(new Date(date), "yyyy-MM-dd")
                        : ""
                    )
                  );
                  dispatch(
                    getDashboardOrdersStats(
                      organizerSelect,
                      "1",
                      startDate !== undefined
                        ? format(new Date(startDate), "yyyy-MM-dd")
                        : "",
                      date !== "" && date !== null && date !== undefined
                        ? format(new Date(date), "yyyy-MM-dd")
                        : ""
                    )
                  );
                }}
                selectsStart
                startDate={startDate}
                endDate={endDate}
                dateFormat="yyyy-MM-dd"
                minDate={startDate ? addDays(new Date(startDate), 1) : null}
                placeholderText="End Date"
              />
            </div>
          </div>
        </div>
        {/*  */}
        <div className="rounded-sm border border-stroke bg-white px-5 py-5 shadow-default   dark:bg-boxdark  my-3">
          <div className="container mx-auto flex flex-col">
            {loadingDashboardInfo ? (
              <Loader />
            ) : errorDashboardInfo ? (
              <Alert type={"error"} message={errorDashboardInfo} />
            ) : (
              <>
                <div className="grid md:grid-cols-4 grid-cols-2 gap-2 lg:gap-8 mb-5">
                  <div className="border card-bg text-white rounded-xl px-4 py-3">
                    <div className="flex items-center uppercase gap-3 text-xs">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-6 h-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"
                        />
                      </svg>{" "}
                      Tickets Sold
                    </div>
                    <div className="py-2 text-2xl font-extrabold">
                      {dashInfo?.tickets_sold ?? "0"}
                    </div>
                  </div>

                  <div className="border card-bg text-white rounded-xl px-4 py-3">
                    <div className="flex items-center uppercase gap-3 text-xs">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-6 h-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z"
                        />
                      </svg>{" "}
                      Revenu
                    </div>

                    <div className="py-2 text-2xl font-extrabold">
                      {dashInfo?.revenu ?? "0"}
                    </div>
                  </div>
                  <div className="border card-bg text-white rounded-xl px-4 py-3">
                    <div className="flex items-center uppercase gap-3 text-xs">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-6 h-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"
                        />
                      </svg>{" "}
                      Total Places
                    </div>

                    <div className="py-2 text-2xl font-extrabold">
                      {dashInfo?.total_places ?? "0"}
                    </div>
                  </div>
                  <div className="border card-bg text-white rounded-xl px-4 py-3">
                    <div className="flex items-center uppercase gap-3 text-xs">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-6 h-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>{" "}
                      Net Profit
                    </div>

                    <div className="py-2 text-2xl font-extrabold">
                      {dashInfo?.net_profit ?? "0"}
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
        <div className="rounded-sm border border-stroke bg-white px-5 py-5 shadow-default   dark:bg-boxdark  my-3">
          <div className="container mx-auto flex flex-col">
            <div className="max-w-full overflow-x-auto ">
              <table className="w-full table-auto">
                <thead>
                  <tr className=" bg-[#F3F5FB] text-left ">
                    <th className="min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                      #
                    </th>

                    <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                      Client
                    </th>
                    <th className="min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max ">
                      Total
                    </th>
                    <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                      Paid At
                    </th>
                    <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                      Status
                    </th>
                    <th className="py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                      Operation
                    </th>
                  </tr>
                </thead>
                {/*  */}
                <tbody>
                  {loadingDashboardOrderStats ? (
                    <Loader />
                  ) : errorDashboardOrderStats ? (
                    <Alert type={"error"} message={errorDashboardOrderStats} />
                  ) : orders ? (
                    orders?.map((item, index) => (
                      <tr key={index}>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            #{item._id}
                          </p>
                        </td>

                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.user?.first_name ?? "---"}{" "}
                            {item.user?.last_name ?? ""}
                          </p>
                        </td>

                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {parseFloat(item.totalPrice).toFixed(2) + " MAD" ??
                              "---"}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.paidAt ?? "---"}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.isOffline ? "Offline" : "Online"}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max flex flex-row  ">
                            <Link
                              className="mx-1 detail-class"
                              to={"/orders/detail/" + item._id}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                className="w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                                />
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                                />
                              </svg>
                            </Link>
                          </p>
                        </td>
                      </tr>
                    ))
                  ) : null}
                </tbody>
              </table>
            </div>
            {/*  */}
            <div className="">
              <Paginate
                route={`/dashboard?startdate=${
                  startDate !== "" &&
                  startDate !== null &&
                  startDate !== undefined
                    ? format(new Date(startDate), "yyyy-MM-dd")
                    : ""
                }&enddate=${
                  endDate !== "" && endDate !== null && endDate !== undefined
                    ? format(new Date(endDate), "yyyy-MM-dd")
                    : ""
                }&`}
                search={""}
                page={page}
                pages={pages}
              />
            </div>
          </div>
        </div>

        {/*  */}

        <div className="grid md:grid-cols-2 w-full container mt-5"></div>
      </div>
    </DefaultLayout>
  );
}

export default DashboardScreen;
