import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { getListOrganizers } from "../../redux/actions/organizerActions";
import DefaultLayout from "../../layouts/DefaultLayout";
import { baseURLFile } from "../../constants";
import {
  deleteTicket,
  getListTickets,
} from "../../redux/actions/ticketActions";
import Alert from "../../components/Alert";
import Loader from "../../components/Loader";
import { getListOrder } from "../../redux/actions/orderActions";
import Paginate from "../../components/Paginate";
import ConfirmationModal from "../../components/ConfirmationModal";

function TicketScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();
  const page = searchParams.get("page") || "1";

  const [status, setStatus] = useState(searchParams.get("status") || "all");
  const [search, setSearch] = useState(searchParams.get("search") || "");
  const [organizerSelect, setOrganizerSelect] = useState(
    searchParams.get("organizer") || ""
  );

  const [eventType, setEventType] = useState("");
  const [ticketId, setTicketId] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [loadEvent, setLoadEvent] = useState(false);

  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  const listTickets = useSelector((state) => state.getListTicket);
  const { tickets, loadingListTickets, errorListTickets, pages } = listTickets;

  const listOrganizers = useSelector((state) => state.getListOrganizerReducer);
  const { organizers, loadingListOrganizers, errorListOrganizers } =
    listOrganizers;

  const ticketDelete = useSelector((state) => state.deleteTicket);
  const { successTicketDelete, loadingTicketDelete, errorTicketDelete } =
    ticketDelete;

  const redirect = "/";

  useEffect(() => {
    if (!userInfo) {
      navigate(redirect);
    } else {
      dispatch(getListOrganizers("0"));
      dispatch(getListTickets(page, organizerSelect, search, status));
    }
  }, [navigate, userInfo, dispatch, page]);

  useEffect(() => {
    if (successTicketDelete) {
      dispatch(getListTickets("1", organizerSelect, search, status));
    }
  }, [successTicketDelete]);

  useEffect(() => {
    const params = new URLSearchParams();

    if (organizerSelect) params.set("organizer", organizerSelect);
    if (search) params.set("search", search);
    if (status && status !== "all") params.set("status", status);

    // Add default page
    params.set("page", "1");

    // Update URL
    navigate({
      pathname: location.pathname,
      search: params.toString(),
    });
  }, [organizerSelect, search, status, dispatch, navigate, location.pathname]);

  return (
    <DefaultLayout>
      <div>
        <div className="flex flex-row text-sm items-center my-1">
          {/* home */}
          <a href="/dashboard">
            <div className="flex flex-row  items-center hover:text-black ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
              <span className="mx-1">Dashboard</span>
            </div>
          </a>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
              />
            </svg>
          </span>
          <div className="">Tickets List</div>
        </div>
        {/*  */}
        <div className="flex flex-row justify-between  items-center my-3">
          <div className="mx-1 font-bold text-black ">Tickets List</div>
        </div>
        {/* filter */}
        <div className="rounded-sm border border-stroke bg-white px-5 py-5 shadow-default   dark:bg-boxdark  my-3">
          <div className="container mx-auto flex md:flex-row flex-col">
            <div className="md:w-1/3 w-full px-2 py-1">
              <select
                className={` outline-none border border-[#F1F3FF]
                      px-3 py-2 w-full rounded text-sm`}
                value={organizerSelect}
                onChange={(v) => {
                  setOrganizerSelect(v.target.value);
                  dispatch(getListTickets("1", v.target.value, search, status));
                }}
              >
                <option value={""}>Select Organizer</option>
                {organizers?.map((organizer, index) => (
                  <option value={organizer._id}>{organizer.name}</option>
                ))}
              </select>
            </div>
            <div className="md:w-1/3 w-full px-2 py-1">
              <select
                className={` outline-none border border-[#F1F3FF]
                      px-3 py-2 w-full rounded text-sm`}
                value={status}
                onChange={(v) => {
                  setStatus(v.target.value);
                  dispatch(
                    getListTickets("1", organizerSelect, search, v.target.value)
                  );
                }}
              >
                <option value="all">All Tickets</option>
                <option value="scanned">Scanned Tickets</option>
                <option value="unscanned">UnScanned Tickets</option>
              </select>
            </div>
            <div className="md:w-1/3 w-full px-2 py-1">
              <input
                className={` outline-none border border-[#F1F3FF]
                      px-3 py-2 w-full rounded text-sm`}
                value={search}
                placeholder="Search by Ticket N..."
                onChange={(v) => {
                  setSearch(v.target.value);
                  dispatch(
                    getListTickets("1", organizerSelect, v.target.value, status)
                  );
                }}
              />
            </div>
          </div>
        </div>
        {/*  */}
        <div className="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1">
          <div className="container mx-auto flex flex-col">
            {loadingListTickets ? (
              <Loader />
            ) : errorListTickets ? (
              <Alert type={"error"} message={errorListTickets} />
            ) : (
              <div className="max-w-full overflow-x-auto ">
                <table className="w-full table-auto">
                  <thead>
                    <tr className=" bg-[#F3F5FB] text-left ">
                      <th className="min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        #
                      </th>

                      <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Ticket N
                      </th>
                      <th className="min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max ">
                        Organizer
                      </th>
                      <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Client
                      </th>
                      <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Order
                      </th>
                      <th className="py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Scanned
                      </th>
                      <th className="py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Operation
                      </th>
                    </tr>
                  </thead>
                  {/*  */}
                  <tbody>
                    {tickets?.map((item, index) => (
                      <tr key={index}>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            #{item.id}
                          </p>
                        </td>

                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.ticketNo ?? "---"}
                          </p>
                        </td>

                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.organizerName ?? "---"}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.firstName ?? "---"} {item.lastName ?? ""}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.orderNo ?? "----"}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.isScanned ? (
                              <span className="flex flex-row items-center ">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke-width="1.5"
                                  stroke="currentColor"
                                  class="size-4"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                  />
                                </svg>
                                <span className="px-1">
                                  {" "}
                                  {item.scannedAt ?? ""}{" "}
                                </span>
                              </span>
                            ) : (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                class="size-4"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                />
                              </svg>
                            )}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max flex flex-row  ">
                            <button
                              className="mx-1 delete-class"
                              onClick={() => {
                                setEventType("delete");
                                setTicketId(item.id);
                                setIsOpen(true);
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                className="w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                />
                              </svg>
                            </button>
                          </p>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            <div className="">
              <Paginate
                route={`/tickets?organizer=${organizerSelect}&search=${search}&status=${status}&`}
                search={""}
                page={page}
                pages={pages}
              />
            </div>
          </div>
        </div>
        {/*  */}
        <div className="grid md:grid-cols-2 w-full container mt-5"></div>
        <ConfirmationModal
          isOpen={isOpen}
          message={
            eventType === "delete"
              ? "Are you sure you want to delete this ticket?"
              : "Are you sure ?"
          }
          onConfirm={async () => {
            if (eventType === "cancel") {
              setIsOpen(false);
              setEventType("");
              setLoadEvent(false);
              setTicketId("");
            } else if (eventType === "delete" && ticketId !== "") {
              setLoadEvent(true);
              dispatch(deleteTicket(ticketId));
              setIsOpen(false);
              setEventType("");
              setLoadEvent(false);
            } else {
              setIsOpen(false);
              setEventType("");
              setLoadEvent(false);
              setTicketId("");
            }
          }}
          onCancel={() => {
            setIsOpen(false);
            setEventType("");
            setLoadEvent(false);
            setTicketId("");
          }}
          loadEvent={loadEvent}
        />
      </div>
    </DefaultLayout>
  );
}

export default TicketScreen;
