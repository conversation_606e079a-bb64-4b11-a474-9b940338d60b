import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Link,
  useLocation,
  useNavigate,
  useSearchParams,
} from "react-router-dom";
import {
  getListOrders,
  getListOrganizers,
} from "../../redux/actions/organizerActions";
import DefaultLayout from "../../layouts/DefaultLayout";
import Loader from "../../components/Loader";
import Alert from "../../components/Alert";
import { baseURLFile } from "../../constants";
import { deleteOrder, getListOrder } from "../../redux/actions/orderActions";
import Paginate from "../../components/Paginate";
import ConfirmationModal from "../../components/ConfirmationModal";
import axios from "../../axios";

function OrderScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();
  const page = searchParams.get("page") || "1";

  const [eventType, setEventType] = useState("");
  const [orderId, setOrderId] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [loadEvent, setLoadEvent] = useState(false);

  const [status, setStatus] = useState("All");
  const [search, setSearch] = useState("");
  const [organizerSelect, setOrganizerSelect] = useState("");

  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  const listOrders = useSelector((state) => state.getListOrder);
  const { orders, loadingListOrders, errorListOrders, pages } = listOrders;

  const listOrganizers = useSelector((state) => state.getListOrganizerReducer);
  const { organizers, loadingListOrganizers, errorListOrganizers } =
    listOrganizers;

  const orderDelete = useSelector((state) => state.deleteOrder);
  const { successOrderDelete, loadingOrderDelete, errorOrderDelete } =
    orderDelete;

  const redirect = "/";

  useEffect(() => {
    if (!userInfo) {
      navigate(redirect);
    } else {
      dispatch(getListOrganizers("0"));
      dispatch(getListOrder(page, organizerSelect, status, search));
    }
  }, [navigate, userInfo, dispatch, page]);

  useEffect(() => {
    if (successOrderDelete) {
      dispatch(getListOrganizers("0"));
      dispatch(getListOrder("1", organizerSelect, status, search));
    }
  }, [successOrderDelete]);

  const [loadExport, setLoadExport] = useState(false);

  const ExportData = () => {
    setLoadExport(true);
    axios
      .get(
        `/orders/export-to-csv/?organizer=${organizerSelect}&status=${status}&search=${search}`,
        {
          responseType: "blob",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userInfo.access}`,
          },
        }
      )
      .then((response) => {
        setLoadExport(false);
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", `order-export.csv`);
        document.body.appendChild(link);
        link.click();
        link.remove();
      })
      .catch((error) => {
        console.error("Export failed", error);
        setLoadExport(false);
      });
  };

  // const ExportData = () => {
  //   try {
  //     setLoadExport(true);
  //     axios
  //       .get(
  //         `/orders/export-to-csv/?organizer=${organizerSelect}&status=${status}&search=${search}`,
  //         {
  //           responseType: "blob",
  //         }
  //       )
  //       .then((response) => {
  //         setLoadExport(false);
  //         let url = window.URL.createObjectURL(new Blob([response.data]));
  //         let link = document.createElement("a");
  //         link.href = url;
  //         link.setAttribute("download", `order-01-023-filename.csv`);
  //         document.body.appendChild(link);
  //         link.click();
  //       });
  //   } catch (error) {
  //     setLoadExport(false);
  //   }
  // };

  return (
    <DefaultLayout>
      <div>
        <div className="flex flex-row text-sm items-center my-1">
          {/* home */}
          <a href="/dashboard">
            <div className="flex flex-row  items-center hover:text-black ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
              <span className="mx-1">Dashboard</span>
            </div>
          </a>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
              />
            </svg>
          </span>
          <div className="">Order list </div>
        </div>
        {/*  */}
        <div className="flex flex-row justify-between  items-center my-3">
          <div className="mx-1 font-bold text-black ">Order List</div>
          <button
            disabled={loadExport}
            onClick={ExportData}
            className="bg-primary flex items-center rounded-full px-4 py-1 text-white "
          >
            {loadExport ? (
                    <div className="size-5  animate-spin rounded-full border-4 border-solid border-white border-t-transparent"></div>

            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-6 h-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"
                />
              </svg>
            )}
            <span className="pl-2">Download</span>
          </button>
        </div>
        {/* filter */}
        <div className="rounded-sm border border-stroke bg-white px-5 py-5 shadow-default   dark:bg-boxdark  my-3">
          <div className="container mx-auto flex md:flex-row flex-col">
            <div className="md:w-1/3 w-full px-2 py-1">
              <select
                className={` outline-none border border-[#F1F3FF]
              px-3 py-2 w-full rounded text-sm`}
                value={organizerSelect}
                onChange={(v) => {
                  setOrganizerSelect(v.target.value);
                  dispatch(getListOrder(page, v.target.value, status, search));
                }}
              >
                <option value={""}>Select Organizer</option>
                {organizers?.map((organizer, index) => (
                  <option value={organizer._id}>{organizer.name}</option>
                ))}
              </select>
            </div>
            <div className="md:w-1/3 w-full px-2 py-1">
              <select
                className={` outline-none border border-[#F1F3FF]
              px-3 py-2 w-full rounded text-sm`}
                value={status}
                onChange={(v) => {
                  setStatus(v.target.value);
                  dispatch(
                    getListOrder(page, organizerSelect, v.target.value, search)
                  );
                }}
              >
                <option value={"All"}>All Orders</option>
                <option value={"False"}>Online Orders</option>
                <option value={"True"}>Offline Orders</option>
              </select>
            </div>
            <div className="md:w-1/3 w-full px-2 py-1">
              <input
                className={` outline-none border border-[#F1F3FF]
              px-3 py-2 w-full rounded text-sm`}
                value={search}
                placeholder="Search here ..."
                onChange={(v) => {
                  setSearch(v.target.value);
                  dispatch(
                    getListOrder(page, organizerSelect, status, v.target.value)
                  );
                }}
              />
            </div>
          </div>
        </div>
        {/*  */}
        <div className="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1">
          <div className="container mx-auto flex flex-col">
            {loadingListOrders ? (
              <Loader />
            ) : errorListOrders ? (
              <Alert type={"error"} message={errorListOrders} />
            ) : (
              <div className="max-w-full overflow-x-auto ">
                <table className="w-full table-auto">
                  <thead>
                    <tr className=" bg-[#F3F5FB] text-left ">
                      <th className="min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        #
                      </th>

                      <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Client
                      </th>
                      <th className="min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max ">
                        Total
                      </th>
                      <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Paid At
                      </th>
                      <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Status
                      </th>
                      <th className="py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Operation
                      </th>
                    </tr>
                  </thead>
                  {/*  */}
                  <tbody>
                    {orders?.map((item, index) => (
                      <tr key={index}>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            #{item._id}
                          </p>
                        </td>

                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.user?.first_name ?? "---"}{" "}
                            {item.user?.last_name ?? ""}
                          </p>
                        </td>

                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {parseFloat(item.totalPrice).toFixed(2) + " MAD" ??
                              "---"}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.paidAt ?? "---"}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.isOffline ? "Offline" : "Online"}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max flex flex-row  ">
                            <Link
                              className="mx-1 detail-class"
                              to={"/orders/detail/" + item._id}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                className="w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                                />
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                                />
                              </svg>
                            </Link>
                            <button
                              className="mx-1 delete-class"
                              onClick={() => {
                                setEventType("delete");
                                setOrderId(item._id);
                                setIsOpen(true);
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                className="w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                />
                              </svg>
                            </button>
                          </p>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            <div className="">
              <Paginate
                route={"/orders?"}
                search={""}
                page={page}
                pages={pages}
              />
            </div>
          </div>
        </div>
        {/*  */}
        <div className="grid md:grid-cols-2 w-full container mt-5"></div>
        <ConfirmationModal
          isOpen={isOpen}
          message={
            eventType === "delete"
              ? "Are you sure you want to delete this order?"
              : "Are you sure ?"
          }
          onConfirm={async () => {
            if (eventType === "cancel") {
              setIsOpen(false);
              setEventType("");
              setLoadEvent(false);
              setOrderId("");
            } else if (eventType === "delete" && orderId !== "") {
              setLoadEvent(true);
              dispatch(deleteOrder(orderId));
              setIsOpen(false);
              setEventType("");
              setLoadEvent(false);
            } else {
              setIsOpen(false);
              setEventType("");
              setLoadEvent(false);
              setOrderId("");
            }
          }}
          onCancel={() => {
            setIsOpen(false);
            setEventType("");
            setLoadEvent(false);
            setOrderId("");
          }}
          loadEvent={loadEvent}
        />
      </div>
    </DefaultLayout>
  );
}

export default OrderScreen;
