import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { getOrderDetail } from "../../redux/actions/orderActions";
import DefaultLayout from "../../layouts/DefaultLayout";
import Loader from "../../components/Loader";
import Alert from "../../components/Alert";
import { baseURL, baseURLFile } from "../../constants";

function OrderDetailScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  let { id } = useParams();

  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  const detailOrder = useSelector((state) => state.getDetailOrder);
  const {
    orderDetail,
    loadingOrderDetail,
    errorOrderDetail,
    successOrderDetail,
  } = detailOrder;

  const redirect = "/";
  useEffect(() => {
    if (!userInfo) {
      navigate(redirect);
    } else {
      dispatch(getOrderDetail(id));
    }
  }, [navigate, userInfo, dispatch, id]);

  return (
    <DefaultLayout>
      <div>
        <div className="flex flex-row text-sm items-center my-1">
          {/* home */}
          <a href="/dashboard">
            <div className="flex flex-row  items-center hover:text-black ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
              <span className="mx-1">Dashboard</span>
            </div>
          </a>
          <a href="/orders">
            <div className="flex flex-row  items-center hover:text-black ">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  className="w-4 h-4"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="m8.25 4.5 7.5 7.5-7.5 7.5"
                  />
                </svg>
              </span>
              <div className="">Order List</div>
            </div>
          </a>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
              />
            </svg>
          </span>
          <div className="">Order Detail</div>
        </div>
        {/*  */}
        <div className="flex flex-row justify-between  items-center my-3">
          <div className="mx-1 font-bold text-black ">Order Detail</div>
        </div>
        {/*  */}
        {loadingOrderDetail ? (
          <Loader />
        ) : errorOrderDetail ? (
          <Alert type={"error"} message={errorOrderDetail} />
        ) : orderDetail ? (
          <>
            {orderDetail.orderItems?.length === 0 ? (
              <Alert type={"error"} message={"Order is empty"} />
            ) : (
              <>
                <div className="rounded-sm border border-stroke bg-white px-5 py-5 shadow-default   dark:bg-boxdark  my-3">
                  <div className="container mx-auto flex flex-col">
                    <div className="my-3 font-bold text-black">
                      #{orderDetail._id ?? id}
                    </div>
                    <div>
                      {orderDetail.isOffline && (
                        <div className="text-danger pb-3 font-bold">
                          Offline Order
                        </div>
                      )}
                    </div>
                    <h1>
                      <span className="border-b font-bold text-black text-xl">
                        Customer info
                      </span>
                    </h1>
                    <div className="py-3">
                      {!orderDetail.isOffline && (
                        <div>
                          <div>
                            <b className="text-black ">First Name</b> :{" "}
                            {orderDetail.user?.first_name}
                          </div>
                          <div>
                            <b className="text-black ">Last Name</b> :{" "}
                            {orderDetail.user?.last_name}
                          </div>
                          <div>
                            <b className="text-black ">Email</b> :{" "}
                            {orderDetail.user?.email}
                          </div>
                          <div>
                            <b className="text-black ">Phone Number</b> :{" "}
                            {orderDetail.user?.phone}
                          </div>
                        </div>
                      )}
                      {orderDetail.isOffline && (
                        <div>
                          <div>
                            <b className="text-black ">First Name</b> :{" "}
                            {orderDetail.infoClient?.firstName}
                          </div>
                          <div>
                            <b className="text-black ">Last Name</b> :{" "}
                            {orderDetail.infoClient?.lastName}
                          </div>
                          <div>
                            <b className="text-black ">Email</b> :{" "}
                            {orderDetail.infoClient?.email}
                          </div>
                          <div>
                            <b className="text-black ">Phone Number</b> :{" "}
                            {orderDetail.infoClient?.phone}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {/*  */}
                <div className="rounded-sm border border-stroke bg-white px-5 py-5 shadow-default   dark:bg-boxdark  my-3 text-black">
                  <div className="container mx-auto flex flex-col">
                    <h1>
                      <span className="border-b">Items</span>
                    </h1>
                    {orderDetail.orderItems?.map((item, index) => (
                      <div className="pt-3" key={index}>
                        <div className="flex py-2 items-center border-b justify-between">
                          <div className="flex w-full ">
                            <img
                              src={
                                baseURLFile === "https://aylink.ma"
                                  ? item?.image
                                  : baseURLFile + item.image
                              }
                              alt={item.name}
                              className="w-24 h-24 rounded-xl object-cover"
                            />
                            <div className=" flex md:flex-row flex-col md:justify-between w-full">
                              <div className=" px-2">
                                <a
                                  className="text-md "
                                  target={"_blank"}
                                  href={`https://aylink.ma/p/${item.organizer.slug}/${item.product.slug}`}
                                >
                                  {item.name}
                                </a>
                                <div className="text- opacity-90 ">
                                  <p>
                                    {item.variationName} {item.variationValue}
                                  </p>
                                  <p>
                                    {item.customization &&
                                      `Customization : ${item.customization}`}
                                  </p>
                                </div>
                                {item.is_daily && item.event_date ? (
                                  <div className="text- opacity-90 ">
                                    <p>Date: {item.event_date}</p>
                                  </div>
                                ) : null}
                                <div className="text- opacity-90 ">
                                  Price : {item.price} MAD
                                </div>
                                {/* <div className="text- opacity-90 " >Organizer : {item.organizer} </div> */}
                              </div>
                              <div className="text-sm my-1 text-left mx-2">
                                {item.qty} x {item.price} MAD ={" "}
                                {item.qty * parseFloat(item.price)} MAD
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                {/*  */}

                <div className=" px-5 py-5  dark:bg-boxdark  text-black">
                  <div className="container mx-auto flex flex-row justify-end  w-full">
                    <div className="py-3 float-right  shadow-default  bg-white  rounded-md px-5  mt-3 w-max">
                      <h1 className="">CMI</h1>
                      <div className="text-sm opacity-75">
                        Transaction ID : {orderDetail.cmiTransId} <br />
                        Updated time : {orderDetail.cmiUpdatedTime} <br />
                        Email : {orderDetail.cmiEmail} <br />
                      </div>
                      <div className="pt-3 text-sm">
                        Paid At : {orderDetail.paidAt} <br />
                      </div>
                      <div className="py-2 font-semibold">
                        Total :
                        {orderDetail.orderItems?.reduce(function (a, b) {
                          return a + parseFloat(b.price) * b.qty;
                        }, 0)}{" "}
                        MAD
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </>
        ) : null}
      </div>
    </DefaultLayout>
  );
}

export default OrderDetailScreen;
