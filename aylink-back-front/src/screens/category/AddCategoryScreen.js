import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import DefaultLayout from "../../layouts/DefaultLayout";
import { addNewCategory } from "../../redux/actions/categoryActions";
import { toast } from "react-toastify";

function AddCategoryScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();

  //
  const [categoryName, setCategoryName] = useState("");
  const [categoryNameError, setCategoryNameError] = useState("");

  const [categoryImageValue, setCategoryImageValue] = useState("");
  const [categoryImage, setCategoryImage] = useState("");
  const [categoryImageError, setCategoryImageError] = useState("");

  const [categoryActive, setCategoryActive] = useState("True");
  const [categoryActiveError, setCategoryActiveError] = useState("");
  //
  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  const categoryCreate = useSelector((state) => state.createNewCategory);
  const { loadingCategoryAdd, errorCategoryAdd, successCategoryAdd } =
    categoryCreate;

  const redirect = "/";
  useEffect(() => {
    if (!userInfo) {
      navigate(redirect);
    }
  }, [navigate, userInfo, dispatch]);

  useEffect(() => {
    if (successCategoryAdd) {
      setCategoryName("");
      setCategoryNameError("");

      setCategoryImageValue("");
      setCategoryImage("");
      setCategoryImageError("");

      setCategoryActive("True");
      setCategoryActiveError("");
    }
  }, [successCategoryAdd]);

  return (
    <DefaultLayout>
      <div>
        <div className="flex flex-row text-sm items-center my-1">
          {/* home */}
          <a href="/dashboard">
            <div className="flex flex-row  items-center hover:text-black ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
              <span className="mx-1">Dashboard</span>
            </div>
          </a>
          <a href="/categories">
            <div className="flex flex-row  items-center hover:text-black ">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  className="w-4 h-4"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="m8.25 4.5 7.5 7.5-7.5 7.5"
                  />
                </svg>
              </span>
              <div className="">Categories List</div>
            </div>
          </a>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
              />
            </svg>
          </span>
          <div className="">Create New Category</div>
        </div>
        {/*  */}
        <div className="py-5 px-4 flex justify-between">
          <h4 className=" uppercase font-semibold text-black dark:text-white">
            New Category
          </h4>
        </div>
        {/*  */}
        <div className="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
          <div className="my-2 bg-white py-4 px-2 rounded-md">
            {/* category name */}
            <div className="flex md:flex-row flex-col  ">
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Category Name <strong className="text-danger">*</strong>
                </div>
                <div>
                  <input
                    className={` outline-none border ${
                      categoryNameError ? "border-danger" : "border-[#F1F3FF]"
                    } px-3 py-2 w-full rounded text-sm`}
                    type="text"
                    placeholder="Category Name"
                    value={categoryName}
                    onChange={(v) => setCategoryName(v.target.value)}
                  />
                  <div className=" text-[8px] text-danger">
                    {categoryNameError ? categoryNameError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/* category status */}
            <div className="flex md:flex-row flex-col  ">
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Status <strong className="text-danger">*</strong>
                </div>
                <div>
                  <select
                    value={categoryActive}
                    onChange={(v) => setCategoryActive(v.target.value)}
                    className={` outline-none border ${
                      categoryActiveError ? "border-danger" : "border-[#F1F3FF]"
                    } px-3 py-2 w-full rounded text-sm`}
                  >
                    <option value={""}>-----</option>
                    <option value={"True"}>Active</option>
                    <option value={"False"}>No Active</option>
                  </select>
                  <div className=" text-[8px] text-danger">
                    {categoryActiveError ? categoryActiveError : ""}
                  </div>
                </div>
              </div>
            </div>
            {/*  */}

            <div className="flex md:flex-row flex-col  ">
              <div className="md:w-1/2 w-full  md:pr-1 my-1">
                <div className="text-[#000000bf] font-bold text-xs  mb-1">
                  Image
                </div>
                <div>
                  <input
                    type="file"
                    value={categoryImageValue}
                    onChange={(v) => {
                      setCategoryImage(v.target.files[0]);
                      setCategoryImageValue(v.target.value);
                    }}
                    className={` outline-none border ${
                      categoryActiveError ? "border-danger" : "border-[#F1F3FF]"
                    } px-3 py-2 w-full rounded text-sm`}
                  />

                  <div className=" text-[8px] text-danger">
                    {categoryActiveError ? categoryActiveError : ""}
                  </div>
                </div>
              </div>
            </div>
            <div className="my-3 ">
              <div className="flex flex-row items-center justify-end my-3">
                <a
                  href="/categories"
                  className="bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3"
                >
                  Back
                </a>
                <button
                  onClick={async () => {
                    var check = true;
                    setCategoryNameError("");
                    setCategoryActiveError("");
                    setCategoryImageError("");

                    if (categoryName === "") {
                      setCategoryNameError("These fields are required.");
                      check = false;
                    }
                    if (categoryActive === "") {
                      setCategoryActiveError("These fields are required.");
                      check = false;
                    }

                    if (check) {
                      // setLoadEvent(true);
                      await dispatch(
                        addNewCategory({
                          category_name: categoryName,
                          category_image: categoryImage,
                          is_active: categoryActive,
                        })
                      ).then(() => {});
                      // setLoadEvent(false);
                    } else {
                      toast.error(
                        "Some fields are empty or invalid. please try again"
                      );
                    }
                  }}
                  className="text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full"
                >
                  {loadingCategoryAdd ? "Loading ..." : "Create Category"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
}

export default AddCategoryScreen;
