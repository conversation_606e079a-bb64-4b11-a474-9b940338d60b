import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Link,
  useLocation,
  useNavigate,
  useSearchParams,
} from "react-router-dom";
import {
  deleteCategory,
  getListCategories,
} from "../../redux/actions/categoryActions";
import Loader from "../../components/Loader";
import DefaultLayout from "../../layouts/DefaultLayout";
import Alert from "../../components/Alert";
import { baseURLFile } from "../../constants";
import ConfirmationModal from "../../components/ConfirmationModal";
import { deleteOrder } from "../../redux/actions/orderActions";

function CategoryScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();
  const page = searchParams.get("page") || "1";

  const [eventType, setEventType] = useState("");
  const [categoryId, setCategoryId] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [loadEvent, setLoadEvent] = useState(false);

  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  const listCategories = useSelector((state) => state.getListCategories);
  const { categories, loadingListCategories, errorListCategories } =
    listCategories;

  const categoryDelete = useSelector((state) => state.deleteCategory);
  const { successCategoryDelete, loadingCategoryDelete, errorCategoryDelete } =
    categoryDelete;

  const redirect = "/";

  useEffect(() => {
    if (!userInfo) {
      navigate(redirect);
    } else {
      dispatch(getListCategories("0"));
    }
  }, [navigate, userInfo, dispatch, page]);

  useEffect(() => {
    if (successCategoryDelete) {
      dispatch(getListCategories("0"));
    }
  }, [successCategoryDelete]);

  return (
    <DefaultLayout>
      <div>
        <div className="flex flex-row text-sm items-center my-1">
          {/* home */}
          <a href="/dashboard">
            <div className="flex flex-row  items-center hover:text-black ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
              <span className="mx-1">Dashboard</span>
            </div>
          </a>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
              />
            </svg>
          </span>
          <div className="">Categories List</div>
        </div>
        {/*  */}
        <div className="flex flex-row justify-between  items-center my-3">
          <div className="mx-1 font-bold text-black ">Categories List</div>
          <div className="flex flex-row items-center justify-end">
            <a
              href="/categories/new-category"
              className="mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="size-4 mx-1"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 4.5v15m7.5-7.5h-15"
                />
              </svg>

              <div>New Category</div>
            </a>
          </div>
        </div>
        {/*  */}
        <div className="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  ">
          <div className="container mx-auto flex flex-col">
            {loadingListCategories ? (
              <Loader />
            ) : errorListCategories ? (
              <Alert type={"error"} message={errorListCategories} />
            ) : (
              <div className="w-full overflow-x-auto ">
                <table className="w-full table-auto">
                  <thead>
                    <tr className=" bg-[#F3F5FB] text-left ">
                      <th className="min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        #
                      </th>
                      <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Category Image
                      </th>
                      <th className="min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max ">
                        Category Name
                      </th>
                      <th className="min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Status
                      </th>
                      <th className="py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max">
                        Operations
                      </th>
                    </tr>
                  </thead>
                  {/*  */}
                  <tbody>
                    {categories?.map((item, index) => (
                      <tr key={index}>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            #{item.id}
                          </p>
                        </td>

                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            <img
                              src={
                                baseURLFile === "https://aylink.ma"
                                  ? item.image
                                  : baseURLFile + item.image
                              }
                              className="size-10 rounded border-white border"
                            />
                          </p>
                        </td>

                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.name ?? "---"}
                          </p>
                        </td>
                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max  ">
                            {item.is_active ? (
                              <span className="flex flex-row items-center ">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke-width="1.5"
                                  stroke="currentColor"
                                  class="size-5"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                  />
                                </svg>
                              </span>
                            ) : (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                class="size-5"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                />
                              </svg>
                            )}
                          </p>
                        </td>

                        <td className=" py-3 px-4 min-w-[120px]  ">
                          <p className="text-black  text-xs w-max flex flex-row  ">
                            <Link
                              className="mx-1 update-class"
                              to={"/categories/edit/" + item.id}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth="1.5"
                                stroke="currentColor"
                                className="w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                                />
                              </svg>
                            </Link>

                            <div
                              onClick={() => {
                                setEventType("delete");
                                setCategoryId(item.id);
                                setIsOpen(true);
                              }}
                              className="mx-1 delete-class cursor-pointer"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                className="w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"
                                />
                              </svg>
                            </div>
                          </p>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
        {/*  */}
        <div className="grid md:grid-cols-2 w-full container mt-5"></div>
        <ConfirmationModal
          isOpen={isOpen}
          message={
            eventType === "delete"
              ? "Are you sure you want to delete this category?"
              : "Are you sure ?"
          }
          onConfirm={async () => {
            if (eventType === "cancel") {
              setIsOpen(false);
              setEventType("");
              setLoadEvent(false);
              setCategoryId("");
            } else if (eventType === "delete" && categoryId !== "") {
              setLoadEvent(true);
              dispatch(deleteCategory(categoryId));
              setIsOpen(false);
              setEventType("");
              setLoadEvent(false);
            } else {
              setIsOpen(false);
              setEventType("");
              setLoadEvent(false);
              setCategoryId("");
            }
          }}
          onCancel={() => {
            setIsOpen(false);
            setEventType("");
            setLoadEvent(false);
            setCategoryId("");
          }}
          loadEvent={loadEvent}
        />
      </div>
    </DefaultLayout>
  );
}

export default CategoryScreen;
