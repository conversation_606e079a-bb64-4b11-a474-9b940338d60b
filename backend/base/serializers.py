from rest_framework import serializers
from base.models import User
from base.models import (
    Product,
    Order,
    OrderItem,
    Section,
    Variation,
    VariationAttribute,
    Images,
    Organizer,
    Coupon,
    ExpiredOrganizer,
    Ticket,
    InfoClient,
    Donation,
    OrganizerCategory,
    TransferOrder,
    Notification,
)
from rest_framework_simplejwt.tokens import Refresh<PERSON>oken
from datetime import datetime, timedelta, date
from django.utils import timezone


class LowercaseEmailField(serializers.EmailField):
    def to_internal_value(self, data):
        # Convert the email address to lowercase
        data = data.lower()
        return super().to_internal_value(data)


class UserSerializer(serializers.ModelSerializer):
    """
    Currently unused in preference of the below.
    """

    email = LowercaseEmailField()
    # user_name = serializers.CharField(required=True)
    password = serializers.CharField(write_only=True)

    # password = serializers.CharField(min_length=8, write_only=True)
    role = serializers.CharField(read_only=True)

    class Meta:
        model = User
        fields = (
            "email",
            "role",
            "last_name",
            "first_name",
            "phone",
            "is_active",
            "id",
            "password",
        )
        extra_kwargs = {"password": {"write_only": True}, "id": {"read_only": True}}

    def create(self, validated_data):
        password = validated_data.pop("password", None)
        # as long as the fields are the same, we can just use this
        instance = self.Meta.model(**validated_data)
        if password is not None:
            instance.set_password(password)
        instance.save()
        return instance


class DonationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Donation
        # fields = '__all__'
        exclude = ("organizer",)


class OrganizerCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = OrganizerCategory
        fields = "__all__"


class ImagesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Images
        fields = "__all__"


class OrganizerSerializer(serializers.ModelSerializer):
    isEnded = serializers.BooleanField(read_only=True)
    organizer_date = serializers.CharField(read_only=True)

    class Meta:
        model = Organizer
        fields = "__all__"


class TicketSerializer(serializers.ModelSerializer):
    isAvailable = serializers.SerializerMethodField()
    organizerDate_parsed = serializers.SerializerMethodField()
    
    
    class Meta:
        model = Ticket
        fields = "__all__"
        
    def get_organizerDate_parsed(self, obj):
        if obj.organizerDate:
            try:
                organizerDate_str = obj.organizerDate
                organizerDate_clean = organizerDate_str.split(' à ')
                if len(organizerDate_clean) == 2:
                    date_part = organizerDate_clean[0]
                    time_part = organizerDate_clean[1]
                    dt = datetime.strptime(date_part + ' ' + time_part, "%m-%d-%Y %H:%M")
                    return dt
            except Exception as e:
                print("Error parsing date:", e)
        return None

    def get_isAvailable(self, obj):
        if obj.orderItem.organizer.show_mail:
            return True
        # Assuming obj.orderitem.organizer.fromDate is the path to get to the fromDate field
        # Adjust the path according to your actual model relationships
        if obj.is_daily == False:
            organizer_from_date = obj.orderItem.organizer.organizerDateFrom
        elif obj.is_daily == True and obj.event_date is not None:
            organizer_from_date = obj.event_date
        else:
            return False
        # organizer_from_date = obj.orderItem.organizer.organizerDateFrom

        # if isinstance(organizer_from_date, datetime):
        #     organizer_from_date = organizer_from_date.date()
        if isinstance(organizer_from_date, datetime):
            organizer_from_date = organizer_from_date.date()
        if not isinstance(organizer_from_date, date):
            return False
        # Compare dates
        current_date = timezone.now().date()  # Ensure timezone awareness
        # Check if fromDate is within the next day
        return current_date + timedelta(days=1) >= organizer_from_date


class TransferSerializer(serializers.ModelSerializer):
    ticket = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = TransferOrder
        fields = "__all__"

    def get_ticket(self, obj):
        ticket = obj.ticket
        serializer = TicketSerializer(ticket, many=False)
        return serializer.data


class ExpiredOrganizerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Organizer
        fields = "__all__"


class CouponSerializer(serializers.ModelSerializer):
    duration = serializers.CharField(read_only=True)
    is_valid = serializers.BooleanField(read_only=True)
    product = serializers.CharField(read_only=True)

    class Meta:
        model = Coupon
        fields = "__all__"

    def create(self, validated_data):
        return Coupon(**validated_data)

    def get_product(self, obj):
        product = obj.product
        return product.name


class VariationAttributeSerializer(serializers.ModelSerializer):

    class Meta:
        model = VariationAttribute
        fields = "__all__"


class VariationSerializer(serializers.ModelSerializer):
    attributes = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Variation
        fields = "__all__"

    def get_attributes(self, obj):
        var_attributes = obj.attributes.all()
        serializer = VariationAttributeSerializer(var_attributes, many=True)
        return serializer.data


class SectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Section
        fields = "__all__"


class ProductSerializer(serializers.ModelSerializer):

    images = serializers.SerializerMethodField(read_only=True)
    # attributes = serializers.SerializerMethodField(read_only=True)
    section = serializers.SerializerMethodField(read_only=True)
    organizer = serializers.SerializerMethodField(read_only=True)
    # coupon = serializers.SerializerMethodField(read_only=True)
    variation = serializers.SerializerMethodField(read_only=True)
    has_active_discount = serializers.BooleanField(read_only=True)
    event_date = serializers.DateField(required=False, allow_null=True)

    class Meta:
        model = Product
        fields = "__all__"

    # def get_reviews(self, obj):
    #     reviews = obj.review_set.all()
    #     serializer = ReviewSerializer(reviews, many=True)
    #     return serializer.data

    def get_section(self, obj):
        section = obj.section
        serializer = SectionSerializer(section, many=False)
        return serializer.data

    def get_organizer(self, obj):
        organizer = obj.organizer
        serializer = OrganizerSerializer(organizer, many=False)
        return serializer.data

    # def get_coupon(self, obj):
    #     if (obj.coupon):
    #         serializer = CouponSerializer(obj.coupon, many=False)
    #         return serializer.data
    #     return {}

    def get_variation(self, obj):
        variation = obj.variation
        serializer = VariationSerializer(variation, many=False)
        return serializer.data

    # def get_attributes(self, obj):
    #     attributes = obj.productvariationvalue_set.all()
    #     serializer = ProductVariationValueSerializer(attributes, many=True)
    #     return serializer.data

    def get_images(self, obj):
        images = obj.images_set.all().order_by("orderImage")
        serializer = ImagesSerializer(images, many=True)
        return serializer.data

    def validate_event_date(self, value):
        has_date = str(self.initial_data.get("has_date", "false")).lower() == "true"
        # Get organizer from context or initial data
        organizer = getattr(self.instance, 'organizer', None)

        # Check if organizer is None, try to get from initial_data
        if not organizer and 'organizer' in self.initial_data:
            from base.models import Organizer
            try:
                organizer_id = self.initial_data.get('organizer')
                if organizer_id:
                    organizer = Organizer.objects.get(id=organizer_id)
            except Exception:
                pass

        # Only validate if has_date is true and organizer exists and is_daily
        if has_date and organizer and getattr(organizer, 'is_daily', False):
            if not value:
                raise serializers.ValidationError(
                    "Event date is required when has_date is true for daily organizers."
                )

        return value


class OrderItemSerializer(serializers.ModelSerializer):
    product = serializers.SerializerMethodField(read_only=True)
    organizer = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = OrderItem
        fields = "__all__"

    def get_product(self, obj):
        if obj.product is None:
            return None

        product = obj.product
        serializer = ProductSerializer(product, many=False)
        return serializer.data

    def get_organizer(self, obj):
        organizer = obj.organizer
        serializer = OrganizerSerializer(organizer, many=False)
        return serializer.data


class InfoClientSerializer(serializers.ModelSerializer):
    class Meta:
        model = InfoClient
        fields = "__all__"


class OrderSerializer(serializers.ModelSerializer):
    orderItems = serializers.SerializerMethodField(read_only=True)
    user = serializers.SerializerMethodField(read_only=True)
    orderNumber = serializers.CharField(read_only=True)
    created_at = serializers.CharField(read_only=True)
    paid_at = serializers.CharField(read_only=True)
    infoClient = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Order
        fields = "__all__"

    def get_orderItems(self, obj):
        items = obj.orderitem_set.all()
        serializer = OrderItemSerializer(items, many=True)
        return serializer.data

    def get_user(self, obj):
        user = obj.user
        if user:
            # Logged-in user
            serializer = UserSerializer(user, many=False)
            return serializer.data
        else:
            # Guest user - return guest information
            return {
                'id': None,
                'email': obj.guest_email,
                'first_name': obj.guest_name.split(' ', 1)[0] if obj.guest_name else "",
                'last_name': obj.guest_name.split(' ', 1)[1] if obj.guest_name and ' ' in obj.guest_name else "",
                'phone': obj.guest_phone,
                'is_active': True,
                'role': 'guest'
            }

    def get_infoClient(self, obj):
        try:
            info_client = InfoClientSerializer(obj.infoclient, many=False).data
        except:
            info_client = {}
        return info_client


class OrderSerializerExcel(serializers.ModelSerializer):
    orderItems = serializers.SerializerMethodField(read_only=True)
    user = serializers.SerializerMethodField(read_only=True)
    infoClient = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Order
        fields = [
            "isOffline",
            "orderItems",
            "paymentMethod",
            "totalPrice",
            "user",
            "paidAt",
            "infoClient",
        ]

    def get_orderItems(self, obj):
        organizer_id = self.context.get("organizer")

        if organizer_id:
            try:
                org = Organizer.objects.get(pk=organizer_id)
                items = obj.orderitem_set.filter(organizer=org)
            except Organizer.DoesNotExist:
                items = obj.orderitem_set.all()
        else:
            items = obj.orderitem_set.all()

        data = ""
        for item in items:
            _line = f"{item.qty} x {item.name} ({item.price})"
            data += _line + "\n"

        # return data.strip()
        return str(data)

    def get_user(self, obj):
        user = obj.user
        if user:
            # Logged-in user
            serializer = UserSerializer(user, many=False)
            return serializer.data
        else:
            # Guest user - return guest information
            return {
                'id': None,
                'email': obj.guest_email,
                'first_name': obj.guest_name.split(' ', 1)[0] if obj.guest_name else "",
                'last_name': obj.guest_name.split(' ', 1)[1] if obj.guest_name and ' ' in obj.guest_name else "",
                'phone': obj.guest_phone,
                'is_active': True,
                'role': 'guest'
            }

    def get_infoClient(self, obj):
        try:
            info_client = InfoClientSerializer(obj.infoclient, many=False).data
        except:
            info_client = {}
        return info_client


class OrderOrganizerSerializer(serializers.ModelSerializer):
    orderItems = serializers.SerializerMethodField(read_only=True)
    user = serializers.SerializerMethodField(read_only=True)
    orderNumber = serializers.CharField(read_only=True)
    infoClient = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Order
        fields = "__all__"

    def get_orderItems(self, obj):
        org = Organizer.objects.get(pk=self.context.get("organizer"))
        # org = Organizer.objects.get(user=self.context.get("user"))
        items = obj.orderitem_set.filter(organizer=org)
        serializer = OrderItemSerializer(items, many=True)
        return serializer.data

    def get_user(self, obj):
        user = obj.user
        if user:
            # Logged-in user
            serializer = UserSerializer(user, many=False)
            return serializer.data
        else:
            # Guest user - return guest information
            return {
                'id': None,
                'email': obj.guest_email,
                'first_name': obj.guest_name.split(' ', 1)[0] if obj.guest_name else "",
                'last_name': obj.guest_name.split(' ', 1)[1] if obj.guest_name and ' ' in obj.guest_name else "",
                'phone': obj.guest_phone,
                'is_active': True,
                'role': 'guest'
            }

    def get_infoClient(self, obj):
        try:
            info_client = InfoClientSerializer(obj.infoclient, many=False).data

        except:
            info_client = {}

        return info_client


class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = "__all__"
