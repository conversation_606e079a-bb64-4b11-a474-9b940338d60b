from django import template
from datetime import datetime

register = template.Library()

@register.filter
def format_organizer_date(value):
    try:
        if value:
            print(f"Formatting organizer date: {value}")
            organizerDate_clean = value.split(' à ')
            if len(organizerDate_clean) == 2:
                date_part = organizerDate_clean[0]
                time_part = organizerDate_clean[1]
                print(f"Date part: {date_part}, Time part: {time_part}")
                dt = datetime.strptime(date_part + ' ' + time_part, "%m-%d-%Y %H:%M")
                formatted = dt.strftime("%d/%m/%Y a %H:%M")
                print(f"Formatted date: {formatted}")
                return formatted
    except Exception as e:
        print(f"Error parsing organizerDate '{value}': {e}")
        # Return the original value if parsing fails
    return value
