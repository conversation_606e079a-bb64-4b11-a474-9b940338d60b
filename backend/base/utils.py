from io import BytesIO,String<PERSON>
from django.http import HttpResponse
from django.template.loader import get_template,render_to_string
from xhtml2pdf import pisa
import random
import string
from backend import settings
import requests,os,re


def fetch_resources(uri, rel):
    if os.sep == '\\': # deal with windows and wrong slashes
        uri2 = os.sep.join(uri.split('/'))
    else:# else, just add the untouched path.
       uri2 = uri

    path = '%s%s' % ("aylink.ma", uri2)
    return path

def get_string(letters_count, digits_count):
    letters = ''.join((random.choice(string.ascii_letters) for i in range(letters_count)))
    digits = ''.join((random.choice(string.digits) for i in range(digits_count)))

    # Convert resultant string to list and shuffle it to mix letters and digits
    sample_list = list(letters + digits)
    # random.shuffle(sample_list)
    # convert list to string
    final_string = ''.join(sample_list)

    return final_string
    # print('Random string with', letters_count, 'letters', 'and', digits_count, 'digits', 'is:', final_string)


# def render_to_pdf(template_src, context_dict={}):
#     template = get_template(template_src)
#     html  = template.render(context_dict)
#     result = BytesIO()
#     # pdf = pisa.pisaDocument(BytesIO(html.encode("ISO-8859-1")), result, link_callback=fetch_resources)
    
#     pdf = pisa.pisaDocument(BytesIO(html.encode("ISO-8859-1")), result)
#     if not pdf.err:
#         return result.getvalue()
    
#     return None

def render_to_pdf(template_src, context_dict={}):
    template = get_template(template_src)
    html = template.render(context_dict)
    result = BytesIO()

    try:
        pdf = pisa.pisaDocument(BytesIO(html.encode("utf8")), result)
        if not pdf.err:
            return result.getvalue()
    except Exception as e:
        # Handle the encoding error
        print("Error occurred during encoding:", str(e))

    return None
 

import uuid

def is_valid_uuid(search_id):
    try:
        uuid_obj = uuid.UUID(search_id, version=4)
    except ValueError:
        return False

    return str(uuid_obj) == search_id

#  verfiy recaptcha =

def verify_recaptcha(token):
    response = requests.post(
        "https://www.google.com/recaptcha/api/siteverify",
        data={
            "secret": settings.RECAPTCHA_SECRET_KEY,
            "response": token,
        },
    )
    response_data = response.json()
    if response_data["success"]:
        return response_data["score"]
    else:
        return 0.0


def is_mobile(request):
    """Return True if the request comes from a mobile device."""

    MOBILE_AGENT_RE=re.compile(r".*(iphone|mobile|androidtouch)",re.IGNORECASE)

    if MOBILE_AGENT_RE.match(request.META['HTTP_USER_AGENT']):
        return True
    else:
        return False