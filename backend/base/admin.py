from django.contrib import admin
from .models import *
from django.apps import apps

# Register your models here.
from django.contrib.auth.admin import UserAdmin

from django.forms import TextInput, Textarea, CharField
from django import forms


class ProductImageInline(admin.TabularInline):
    model = Images
    fields = ["image_tag"]
    readonly_fields = ("name", "image_tag")
    extra = 0


class ProductAdmin(admin.ModelAdmin):
    list_display = [
        "_id",
        "createdAt",
        "name",
        "countInStock",
        "isActive",
        "price",
    ]
    ordering = ("-createdAt", "isActive")

    @admin.display()
    def get_customer(self, obj):
        if obj.user:
            full_name = obj.user.first_name + " " + obj.user.last_name
        else:
            full_name = "Deleted "
        return full_name

    inlines = [ProductImageInline]
    search_fields = (
        "_id",
        "name",
    )


admin.site.register(Product, ProductAdmin)


class UserAdminConfig(UserAdmin):
    model = User
    search_fields = (
        "email",
        "first_name",
    )
    list_filter = ("email", "first_name", "is_active", "is_staff")
    ordering = ("-start_date",)
    list_display = ("email", "first_name", "last_name", "is_active", "is_staff")
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "email",
                    "phone",
                    "role",
                    "first_name",
                    "last_name",
                    "is_deleted",
                    "password",
                )
            },
        ),
        ("Permissions", {"fields": ("is_staff", "is_superuser", "is_active")}),
        ("Personal", {"fields": ("about", "token_fb")}),
    )
    formfield_overrides = {
        models.TextField: {"widget": Textarea(attrs={"rows": 20, "cols": 60})},
    }
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "email",
                    "first_name",
                    "last_name",
                    "password1",
                    "password2",
                    "is_active",
                    "is_staff",
                ),
            },
        ),
    )


admin.site.register(User, UserAdminConfig)


class SectionAdmin(admin.ModelAdmin):
    list_display = ["_id", "name", "slug"]


admin.site.register(Section, SectionAdmin)


class EmailStatusAdmin(admin.ModelAdmin):
    list_display = ["user", "sent_count", "status"]


admin.site.register(EmailStatus, EmailStatusAdmin)

# class OrderAdmin(admin.ModelAdmin):
#     list_display = [ '_id','createdAt']


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    readonly_fields = ("product", "organizer")
    extra = 0


class Orderdmin(admin.ModelAdmin):
    list_display = [
        "_id",
        "createdAt",
        "get_customer",
        "isPaid",
        "totalPrice",
        "isOffline",
        "as_guest",
    ]
    ordering = ("-createdAt", "totalPrice")
    list_filter = ("isPaid", "isOffline", "as_guest")

    @admin.display()
    def get_customer(self, obj):
        if obj.as_guest:
            # For guest orders, show guest name and email
            return f"{obj.guest_name} (Guest: {obj.guest_email})"
        elif obj.user:
            full_name = obj.user.first_name + " " + obj.user.last_name
            return f"{full_name} ({obj.user.email})"
        else:
            return "Deleted User"

    inlines = [OrderItemInline]
    search_fields = (
        "_id",
        "user__first_name",
        "user__last_name",
        "user__email",
        "guest_name",
        "guest_email",
    )


admin.site.register(Order, Orderdmin)
# admin.site.register(OrderItem)

admin.site.register(VariationAttribute)
admin.site.register(Variation)
admin.site.register(ExpiredOrganizer)
# admin.site.register(ProductVariation)

admin.site.register(Images)
admin.site.register(OrderItem)


class TicketAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "ticketNo",
        "firstName",
        "lastName",
        "productName",
        "isScanned",
    ]
    # ordering = ('-createdAt','totalPrice')

    search_fields = ("ticketNo", "firstName", "lastName")


admin.site.register(Ticket, TicketAdmin)


class OrganizerAdmin(admin.ModelAdmin):
    list_display = ["name", "_id", "isEnded"]


admin.site.register(Organizer, OrganizerAdmin)

admin.site.register(OrganizerCategory)
admin.site.register(TransferOrder)

admin.site.register(Coupon)

admin.site.register(InfoClient)


class DonationAdmin(admin.ModelAdmin):
    list_display = ["fullName", "emailDon", "phoneDon", "totalDon", "isPaid"]
    ordering = ("-createdAt", "totalDon")


admin.site.register(Donation, DonationAdmin)
admin.site.register(Notification)
