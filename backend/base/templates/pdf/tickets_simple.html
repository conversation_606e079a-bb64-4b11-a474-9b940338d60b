{% load static %}
<html>
  <head>
    <meta charset="UTF-8" />
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #fff;
        margin: 5px;
      }
    </style>
  </head>

  <body style="width: 500px; margin: 0 auto">
    <h1>Order Tickets</h1>
    
    <div>
      <h2>Order ID: {{ order.id }}</h2>
      <p>Total Price: {{ order.totalPrice }} MAD</p>
      <p>Is Paid: {{ order.isPaid }}</p>
      <p>Show Mail: {{ showmail }}</p>
    </div>

    <div>
      <h3>Tickets:</h3>
      {% for ticket in tickets %}
        <div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
          <p><strong>Ticket #{{ ticket.ticketNo }}</strong></p>
          <p>Organizer: {{ ticket.organizerName }}</p>
          <p>Product: {{ ticket.productName }}</p>
          <p>Price: {{ ticket.price }} MAD</p>
          <p>First Name: {{ ticket.firstName }}</p>
          <p>Last Name: {{ ticket.lastName }}</p>
          {% if ticket.organizerDate %}
            <p>Date: {{ ticket.organizerDate }}</p>
          {% endif %}
          {% if ticket.event_date %}
            <p>Event Date: {{ ticket.event_date }}</p>
          {% endif %}
        </div>
      {% empty %}
        <p>No tickets found for this order.</p>
      {% endfor %}
    </div>
  </body>
</html>
