from django.shortcuts import render, get_object_or_404

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import Is<PERSON><PERSON><PERSON><PERSON>ted, IsAdminUser
from rest_framework.response import Response
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from base.permissions import IsOrganizer
from base.models import Ticket, Organizer, Order, TransferOrder, User
from base.serializers import TransferSerializer
from datetime import datetime
from rest_framework import status

from base.emails import send_email_transfer

from base.views.notification_views import save_notification

# from django.http import HttpResponse
# import qrcode
# from io import BytesIO


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def transferTicket(request):
    user = request.user
    email_to = request.data.get("emailTo")
    ticket_no = request.data.get("ticketNo")
    order_no = request.data.get("orderNo")

    if email_to == user.email:
        return Response({"detail": "Tciket can't be sent to yourself "}, status=400)

    # (2) Check the ticket number (it should not be transferred before)

    ticket = get_object_or_404(
        Ticket, ticketNo=ticket_no, transfered=False
    )  # Assuming 'visible' is a field indicating if ticket is active or not
    if TransferOrder.objects.filter(ticketNo=ticket_no).exists():
        return Response({"detail": "Ticket has already been transferred"}, status=400)
    # (3) Check the order number (order should be paid)
    try:
        order = get_object_or_404(
            Order, pk=order_no, isPaid=True
        )  # Assuming 'status' field exists with a 'PAID' value for paid orders
    except Order.DoesNotExist:
        return Response({"detail": "Order not existe"}, status=400)

    if ticket.orderItem.order != order or order.user != user:
        return Response({"detail": "Invalid ticket or order for the user"}, status=400)

    # (1) Check the received user
    # check is not deleted and active
    to_user = User.objects.filter(email=email_to, is_active=True, is_deleted=False)

    if len(to_user) == 0:
        # User does not exist, send an email
        send_email_transfer(email_to, "Someone tried to send you a ticket ")
        return Response(
            {"detail": "Email address not registered. Try again when email is created"},
            status=400,
        )

    # Validation to ensure the ticket and order match and belong to the requesting user

    # (4) Create transfer
    TransferOrder.objects.create(
        from_user=user,
        to_user=to_user[0],
        ticketNo=ticket_no,
        ticket=ticket,
        count=1,
        isActive=True,
        orderNo=order_no,
    )

    # (5) Make the previous ticket invisible and disable for the sender user
    # (6) Assuming you handle activation for the receiver user elsewhere or via model signals
    ticket.firstName = to_user[0].first_name
    ticket.lastName = to_user[0].last_name
    ticket.transfered = True
    ticket.save()
    # save notif
    save_notification(
        "Transférer un billet avec succès.",
        "Nous souhaitons vous informer que quelqu'un vous a transféré un billet.",
        to_user[0],
        None,
        2,
        None,
        False,
    )

    return Response({"detail": "Ticket transferred successfully"})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def reTransferTicket(request):

    user = request.user
    email_to = request.data.get("emailTo")
    ticket_no = request.data.get("ticketNo")
    order_no = request.data.get("orderNo")
    transfer_no = request.data.get("transferNo")

    if email_to == user.email:
        return Response({"detail": "Tciket can't be sent to yourself "}, status=400)

    # (2) Check the ticket number (it should not be transferred before)

    ticket = get_object_or_404(
        Ticket, ticketNo=ticket_no, transfered=True
    )  # Assuming 'visible' is a field indicating if ticket is active or not
    if TransferOrder.objects.filter(pk=transfer_no, isActive=False).exists():
        return Response({"detail": "Ticket has already been transferred"}, status=400)

    # (3) Check the order number (order should be paid)
    try:
        order = get_object_or_404(
            Order, pk=order_no, isPaid=True
        )  # Assuming 'status' field exists with a 'PAID' value for paid orders
    except Order.DoesNotExist:
        return Response({"detail": "Order not existe"}, status=400)

    #  check if this user has right to send this ticket
    print(transfer_no)
    tr = TransferOrder.objects.get(pk=transfer_no)
    print("-----")
    print(tr)
    if tr.to_user != user:
        return Response({"detail": "Invalid ticket or order for the user"}, status=400)

    # (1) Check the received user and check is not deleted and is active
    to_user = User.objects.filter(email=email_to, is_active=True, is_deleted=False)

    if len(to_user) == 0:
        # User does not exist, send an email
        send_email_transfer(email_to, "Someone tried to send you a ticket ")
        return Response(
            {"detail": "Email address not registered. Try again when email is created"},
            status=400,
        )

    # Validation to ensure the ticket and order match and belong to the requesting user

    # Disable old transfer
    # change ticket name
    if ticket:
        ticket.firstName = to_user[0].first_name
        ticket.lastName = to_user[0].last_name

    # (4) Create new transfer
    TransferOrder.objects.create(
        from_user=user,
        to_user=to_user[0],
        ticketNo=ticket_no,
        ticket=ticket,
        count=1,
        orderNo=order_no,
        isActive=True,
    )
    ticket.firstName = to_user[0].first_name
    ticket.lastName = to_user[0].last_name
    ticket.save()
    tr.isActive = False
    tr.save()

    # (5) Make the previous ticket invisible and disable for the sender user
    # (6) Assuming you handle activation for the receiver user elsewhere or via model signals
    # save notif
    save_notification(
        "Transférer un billet avec succès.",
        "Nous souhaitons vous informer que quelqu'un vous a transféré un billet.",
        to_user[0],
        None,
        2,
        None,
        False,
    )

    return Response({"detail": "Ticket has been re-transfered "})


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def getMyTransfers(request):
    # list of my transfers
    transfers = TransferOrder.objects.filter(to_user=request.user, isActive=True)

    serializer = TransferSerializer(transfers, many=True)

    return Response({"transfers": serializer.data})
