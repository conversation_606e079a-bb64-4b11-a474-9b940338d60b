from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from base.permissions import IsOrganizer
from base.models import OrderItem, Order, Organizer, Product, Ticket
from base.serializers import OrderItemSerializer
from datetime import datetime
from rest_framework import status
from django.http import JsonResponse

# for chart
from django.db.models import Count
from django.utils import timezone

from django.db.models.functions import (
    TruncMonth,
    TruncWeek,
    TruncDay,
    TruncHour,
    ExtractDay,
)
import decimal
from dateutil.relativedelta import relativedelta
from datetime import datetime, timedelta
from rest_framework.permissions import IsAuthenticated, IsAdminUser


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewOverview(request):
    organizerId = request.query_params.get("organizer", "")
    if organizerId and organizerId != "":
        organizer = Organizer.objects.get(pk=organizerId)
        products = Product.objects.filter(
            organizer=organizer, isDeleted=False
        ).order_by("-createdAt")
        data = []
        for product in products:
            _p = {}
            _orderItems = OrderItem.objects.filter(
                organizer=organizer, product=product, order__isPaid=True
            )
            _p["product"] = {
                "name": product.name,
                "countInStock": product.countInStock,
                "id": product._id,
            }
            amount = 0
            _qty = 0
            _sold_online = 0
            _sold_offline = 0
            for _o in _orderItems:
                amount += _o.price * _o.qty
                _qty += _o.qty
                if _o.order.isOffline:
                    _sold_offline += _o.qty
                else:
                    _sold_online += _o.qty

            _p["order"] = {
                "qty": _qty,
                "amount": amount,
                "sold_offline": _sold_offline,
                "sold_online": _sold_online,
            }
            data.append(_p)
        return JsonResponse(data, safe=False)
    else:
        products = Product.objects.filter(
            organizer__user=request.user, isDeleted=False
        ).order_by("-createdAt")

        data = []
        for product in products:
            _p = {}
            _orderItems = OrderItem.objects.filter(
                organizer__user=request.user, product=product, order__isPaid=True
            )
            _p["product"] = {"name": product.name, "countInStock": product.countInStock}
            amount = 0
            _qty = 0
            _sold_online = 0
            _sold_offline = 0
            for _o in _orderItems:
                amount += _o.price * _o.qty
                _qty += _o.qty
                if _o.order.isOffline:
                    _sold_offline += _o.qty
                else:
                    _sold_online += _o.qty

            _p["order"] = {
                "qty": _qty,
                "amount": amount,
                "sold_offline": _sold_offline,
                "sold_online": _sold_online,
            }
            data.append(_p)
        return JsonResponse(data, safe=False)


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewDaysLeft(request):
    organizerId = request.query_params.get("organizer", "")
    if organizerId and organizerId != "":
        organizer = Organizer.objects.get(pk=organizerId)
        date = organizer.organizerDateFrom
        delta = date - timezone.now()
        days_left = delta.days
        return Response({"days_left": days_left})
    else:

        return Response({"days_left": 0})


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewInventory(request):
    organizerId = request.query_params.get("organizer", "")
    if organizerId and organizerId != "":
        organizer = Organizer.objects.get(pk=organizerId)
        _orderitems = OrderItem.objects.filter(organizer=organizer, order__isPaid=True)

        products = Product.objects.filter(
            organizer=organizer, isDeleted=False, isActive=True
        )
    else:
        _orderitems = OrderItem.objects.filter(
            organizer__user=request.user, order__isPaid=True
        )

        products = Product.objects.filter(
            organizer__user=request.user, isDeleted=False, isActive=True
        )

    total = 0
    for _p in products:
        total += _p.countInStock
    tickets_sold = Ticket.objects.filter(orderItem__in=_orderitems).count()

    return Response({"sold": tickets_sold, "total": total})


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewChart(request):
    time_filter = request.query_params.get("time_filter", "today")
    organizerId = request.query_params.get("organizer", "")
    if organizerId and organizerId != "":
        organizer = Organizer.objects.get(pk=organizerId)
        _tickets = Ticket.objects.filter(orderItem__organizer=organizer)
    else:
        _tickets = Ticket.objects.filter(orderItem__organizer__user=request.user)

    if time_filter == "this_month":
        start_date = timezone.now().replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )
        trunc = TruncDay("paidAt")
        date_format = "%d"
    elif time_filter == "today":
        start_date = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        trunc = TruncHour("paidAt")
        date_format = "%H"
    elif time_filter == "all_time":
        start_date = None
        trunc = TruncMonth("paidAt")
        date_format = "%Y-%b"
    else:
        return Response({"error": "Invalid time_filter parameter"}, status=400)

    if start_date is not None:
        sales_data = (
            _tickets.filter(paidAt__gte=start_date)
            .annotate(truncated_date=trunc)
            .values("truncated_date")
            .annotate(sales=Count("id"))
            .order_by("truncated_date")
        )
    else:
        sales_data = (
            _tickets.annotate(truncated_date=trunc)
            .values("truncated_date")
            .annotate(sales=Count("id"))
            .order_by("truncated_date")
        )

    sales_data = [
        {"date": d["truncated_date"].strftime(date_format), "sales": d["sales"]}
        for d in sales_data
    ]

    return Response(sales_data)


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewStats(request):
    organizerId = request.query_params.get("organizer")
    if organizerId and organizerId != "":
        organizer = Organizer.objects.get(pk=organizerId)
        if organizer:
            _orderitems = OrderItem.objects.filter(
                organizer=organizer,
                order__isPaid=True,
            )
            fees = decimal.Decimal((100 - organizer.feesPercent) / 100)
            products = Product.objects.filter(
                organizer=organizer, isDeleted=False
            ).order_by("-createdAt")
            _sold = 0
            _revenu = 0
            # _net = 0
            for i in _orderitems:
                _sold = _sold + i.qty
                _revenu = _revenu + (i.qty * i.price)

            total_tickets = 0
            for product in products:
                total_tickets = total_tickets + product.countInStock

            return Response(
                {
                    "tickets_sold": _sold,
                    "revenu": _revenu,
                    "total_places": total_tickets,
                    "net_profit": _revenu * fees,
                }
            )
        else:
            return Response(
                {
                    "tickets_sold": 0,
                    "revenu": 0,
                    "total_places": 0,
                    "net_profit": 0,
                }
            )

    else:
        _orderitems = OrderItem.objects.filter(
            organizer__user=request.user,
            order__isPaid=True,
        )
        # fees = decimal.Decimal((100 - organizer.feesPercent) / 100)

        products = Product.objects.filter(
            organizer__user=request.user, isDeleted=False
        ).order_by("-createdAt")

        _sold = 0
        _revenu = 0
        # _net = 0
        for i in _orderitems:
            _sold = _sold + i.qty
            _revenu = _revenu + (i.qty * i.price)

        total_tickets = 0
        for product in products:
            total_tickets = total_tickets + product.countInStock

        return Response(
            {
                "tickets_sold": _sold,
                "revenu": _revenu,
                "total_places": total_tickets,
                "net_profit": 0,
                # "net_profit": _revenu * fees,
            }
        )


# last


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getStats(request):
    organizer = Organizer.objects.get(user=request.user)
    _orderitems = OrderItem.objects.filter(
        organizer=organizer,
        order__isPaid=True,
    )
    fees = decimal.Decimal((100 - organizer.feesPercent) / 100)

    products = Product.objects.filter(organizer=organizer, isDeleted=False).order_by(
        "-createdAt"
    )

    _sold = 0
    _revenu = 0
    # _net = 0
    for i in _orderitems:
        _sold = _sold + i.qty
        _revenu = _revenu + (i.qty * i.price)
        # if i.order.isOffline :
        #     _net -= float( i.qty * i.price ) * (i.organizer.feesPercent/100)
        # else :
        # _net += float( i.qty * i.price ) * (1 - i.organizer.feesPercent/100)

    total_tickets = 0
    for product in products:
        total_tickets = total_tickets + product.countInStock

    # orders = Order.objects.filter(orderitem__in=_orderitem,isPaid=True).order_by("-createdAt")
    # for order in orders:
    #     _revenu += order.

    # print(_orderitem[1].qty)
    # orders = Order.objects.filter(orderitem__in=_orderitem,isPaid=True).order_by("-createdAt")

    return Response(
        {
            "tickets_sold": _sold,
            "revenu": _revenu,
            "total_places": total_tickets,
            "net_profit": _revenu * fees,
        }
    )


@permission_classes([IsOrganizer])
@api_view(["GET"])
def getChart(request):
    time_filter = request.query_params.get("time_filter", "today")
    organizer = Organizer.objects.get(user=request.user)
    _tickets = Ticket.objects.filter(orderItem__organizer=organizer)

    if time_filter == "this_month":
        start_date = timezone.now().replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )
        trunc = TruncDay("paidAt")
        date_format = "%d"
    elif time_filter == "today":
        start_date = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        trunc = TruncHour("paidAt")
        date_format = "%H"
    elif time_filter == "all_time":
        start_date = None
        trunc = TruncMonth("paidAt")
        date_format = "%Y-%b"
    else:
        return Response({"error": "Invalid time_filter parameter"}, status=400)

    if start_date is not None:
        sales_data = (
            _tickets.filter(paidAt__gte=start_date)
            .annotate(truncated_date=trunc)
            .values("truncated_date")
            .annotate(sales=Count("id"))
            .order_by("truncated_date")
        )
    else:
        sales_data = (
            _tickets.annotate(truncated_date=trunc)
            .values("truncated_date")
            .annotate(sales=Count("id"))
            .order_by("truncated_date")
        )

    sales_data = [
        {"date": d["truncated_date"].strftime(date_format), "sales": d["sales"]}
        for d in sales_data
    ]

    return Response(sales_data)


# @permission_classes([IsOrganizer])
# @api_view(['GET'])
# def getChart(request):
#     time_filter = request.query_params.get('time_filter', 'today')
#     if time_filter == 'last_month':
#         end_date = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
#         start_date = (end_date - relativedelta(months=1)).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
#         trunc = TruncDay('paidAt')
#         date_format = '%d'
#     elif time_filter == 'this_month':
#         start_date = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
#         end_date = (start_date + relativedelta(months=1)).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
#         trunc = TruncDay('paidAt')
#         date_format = '%d'
#     elif time_filter == 'today':
#         start_date = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
#         end_date = start_date + timedelta(days=1)
#         trunc = TruncHour('paidAt')
#         date_format = '%H'
#     elif time_filter == 'all_time':
#         start_date = None
#         end_date = None
#         trunc = TruncDay('paidAt')
#         date_format = '%d-%b'
#     else:
#         return Response({'error': 'Invalid time_filter parameter'}, status=400)

#     if start_date is not None:
#         all_dates = [start_date + timedelta(days=i) for i in range((end_date - start_date).days)]
#         sales_data = Ticket.objects.filter(paidAt__gte=start_date, paidAt__lt=end_date) \
#             .annotate(truncated_date=trunc) \
#             .values('truncated_date') \
#             .annotate(sales=Count('id')) \
#             .order_by('truncated_date')
#     else:
#         sales_data = Ticket.objects.annotate(truncated_date=trunc) \
#             .values('truncated_date') \
#             .annotate(sales=Count('id')) \
#             .order_by('truncated_date')
#         all_dates = [timezone.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=i) for i in range(30)]

#     merged_data = []
#     sales_data_index = 0
#     for date in all_dates:
#         sales_count = 0
#         if sales_data_index < len(sales_data) and sales_data[sales_data_index]['truncated_date'] == date:
#             sales_count = sales_data[sales_data_index]['sales']
#             sales_data_index += 1
#         merged_data.append({'date': date.strftime(date_format), 'sales': sales_count})

#     return Response(merged_data)


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getOverview(request):
    organizer = Organizer.objects.get(user=request.user)
    products = Product.objects.filter(organizer=organizer, isDeleted=False).order_by(
        "-createdAt"
    )

    data = []
    for product in products:
        _p = {}
        _orderItems = OrderItem.objects.filter(
            organizer=organizer, product=product, order__isPaid=True
        )
        _p["product"] = {"name": product.name, "countInStock": product.countInStock}
        amount = 0
        _qty = 0
        _sold_online = 0
        _sold_offline = 0
        for _o in _orderItems:
            amount += _o.price * _o.qty
            _qty += _o.qty
            if _o.order.isOffline:
                _sold_offline += _o.qty
            else:
                _sold_online += _o.qty

        _p["order"] = {
            "qty": _qty,
            "amount": amount,
            "sold_offline": _sold_offline,
            "sold_online": _sold_online,
        }
        data.append(_p)
    return JsonResponse(data, safe=False)


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getInventory(request):
    organizer = Organizer.objects.get(user=request.user)
    _orderitems = OrderItem.objects.filter(organizer=organizer, order__isPaid=True)

    products = Product.objects.filter(
        organizer=organizer, isDeleted=False, isActive=True
    )

    total = 0
    for _p in products:
        total += _p.countInStock
    tickets_sold = Ticket.objects.filter(orderItem__in=_orderitems).count()

    return Response({"sold": tickets_sold, "total": total})


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getDaysLeft(request):
    organizer = Organizer.objects.get(user=request.user)
    date = organizer.organizerDateFrom
    delta = date - timezone.now()
    days_left = delta.days
    return Response({"days_left": days_left})
