from django.shortcuts import render, get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from base.permissions import IsOrganizer
from rest_framework.response import Response
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

from base.models import (
    Product,
    Variation,
    Images,
    Section,
    Organizer,
    VariationAttribute,
    Ticket,
)
from base.serializers import ProductSerializer

from rest_framework import status
from django.db.models import Q
from django.core.exceptions import ObjectDoesNotExist

from django.views.decorators.csrf import csrf_exempt

# new


@api_view(["POST"])
def checkProductByDate(request, pk):
    try:
        product = Product.objects.get(_id=pk)
        if product:
            date = request.data["date"]
            quantity = 1

            if "quantity" in request.data and request.data["quantity"] != "":
                quantity = request.data["quantity"]

            try:
                quantity = int(quantity)
            except ValueError:
                return Response(
                    {"detail": "Quantity must be a valid number"}, status=401
                )
            #
            if date and date != "":
                tickets = Ticket.objects.filter(
                    Q(orderItem__product=product) & Q(event_date=date)
                )
                if tickets.count() + quantity <= product.countInStock:
                    return Response(
                        {"detail": "This product is available on this date"}
                    )
                else:
                    return Response(
                        {
                            "detail": "Sorry, this product is not available on this date,please change the date or change the quantity"
                        },
                        status=401,
                    )
            else:
                return Response(
                    {
                        "detail": "Sorry, this product is not available on this date,please change the date or change the quantity"
                    },
                    status=401,
                )

        else:
            return Response(
                {"detail": "Sorry, This Product does not exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Product does not exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {"detail": "Sorry, an error occurred while sending the data. Try Again"},
            status=401,
        )


@api_view(["PUT"])
@permission_classes([IsOrganizer])
def updateNewProduct(request, pk):
    try:
        organizer = Organizer.objects.get(pk=request.data["selectOrganizer"])
        if organizer:
            data = request.data
            print(data)

            product = get_object_or_404(Product, _id=pk)
            section = None
            print("DEBUG:", type(data.get("has_date")), data.get("has_date"))
            if "section" in data and data["section"] != "":
                section = Section.objects.get(pk=data["section"])
            has_date = str(data.get("has_date", "")).strip().lower() == "true"
            product.has_date = has_date
            if has_date:
                if product.organizer.is_daily:
                    event_date = data.get("event_date")
                    product.product_date=None
                    # Validate if event_date is missing, empty, or None
                    if not event_date:
                        return Response(
                            {"error": "Event date is required when has date is true."},
                            status=400,
                        )
                    else:
                        product.event_date = event_date
                else:
                    product.product_date = data.get("product_date",None)
                    product.event_date = None
            else:
                product.event_date = None
                product.product_date = data.get("product_date",None)
            # product.has_date=True if "has_date" in data and data["has_date"]!="" and str(data["has_date"])=="true" else False

            hasVariation = data["hasVariation"]
            if not hasVariation:
                product.variation = None
                product.price = 0.0
                product.save()

            product.organizer = organizer
            product.save()

            serializer = ProductSerializer(product, data=request.data, partial=True)

            if serializer.is_valid(raise_exception=True):
                serializer.save(section=section)
                return Response({"detail": "Product has been updated !"})
            return Response({"detail": "Product has not been updated !"}, status=400)
        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        import traceback

        print("ERROR:", traceback.format_exc())
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@csrf_exempt
@api_view(["POST"])
@permission_classes([IsOrganizer])
def createNewProduct(request):
    print("nice")
    try:
        organizer = Organizer.objects.get(pk=request.data["selectOrganizer"])
        if organizer:
            data = request.data

            # organizer = Organizer.objects.get(user=request.user, isActive=True)
            section = None
            if "section" in data and data["section"] != "":
                section = Section.objects.get(pk=data["section"])

            #

            # Determine if product has date
            has_date = str(data.get("has_date", "")).lower() == "true"

            # Prepare the product data to pass to the serializer
            if has_date:
                if organizer.is_daily:
                    event_date = data.get("event_date")
                    if not event_date:
                        return Response(
                            {"error": "Event date is required when has date is true."},
                            status=400,
                        )
                    data["event_date"] = event_date  # Set event_date in data
                    data["product_date"] = None
                else:
                    data["product_date"] = data.get("product_date", None)
                    data["event_date"] = None
                    
            else:
                data["event_date"] = None
                data["product_date"] = None

            #
            serializer = ProductSerializer(data=request.data)
            if serializer.is_valid(raise_exception=True):
                serializer.save(
                    organizer=organizer,
                    section=section,
                )
                return Response(serializer.data)
            return Response({"detail": "Product has not been added !"}, status=400)
        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["DELETE"])
@permission_classes([IsOrganizer])
def deleteNewProduct(request, pkorg, pk):
    try:
        organizer = Organizer.objects.get(pk=pkorg)
        if organizer:
            product = Product.objects.get(_id=pk)
            if product.organizer == organizer and organizer.user == request.user:
                # product.delete()
                product.isDeleted = True
                product.save()
                return Response("Product Deleted")
            else:
                return Response("Error occured. Please try later !")
        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewAdminProducts(request):
    try:
        organizerId = request.query_params.get("organizer", "")

        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer:

                query = request.query_params.get("keyword")

                if query == None:
                    query = ""

                # products = Product.objects.all().filter(organizer=organizer).order_by('-createdAt')
                products = organizer.product_set.filter(isDeleted=False).order_by(
                    "-createdAt"
                )

                page = request.query_params.get("page")
                paginator = Paginator(products, 12)

                try:
                    products = paginator.page(page)
                except PageNotAnInteger:
                    products = paginator.page(1)
                except EmptyPage:
                    products = paginator.page(paginator.num_pages)

                if page == None:
                    page = 1
                page = int(page)
                if page == 0:

                    products = (
                        organizer.product_set.all()
                        .filter(isDeleted=False, isActive=True)
                        .order_by("-createdAt")
                    )

                serializer = ProductSerializer(products, many=True)
                return Response(
                    {
                        "products": serializer.data,
                        "page": page,
                        "pages": paginator.num_pages,
                    }
                )

            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
                )

        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# last


@api_view(["GET"])
def getProducts(request):

    query = request.query_params.get("keyword")

    products = Product.objects.filter(isActive=True, isDeleted=False).order_by(
        "-createdAt"
    )

    if query == None:
        query = ""

    page = 1
    page = int(page)

    serializer = ProductSerializer(products, many=True)

    return Response({"products": serializer.data, "page": page})


@api_view(["GET"])
def getProductsByOrganizertest(request, organizer):

    section = request.query_params.get("section")
    # section = Section.objects.get(pk=section)

    products = Product.objects.filter(
        organizer__slug=organizer, isActive=True, isDeleted=False
    ).order_by("-createdAt")

    if section:
        products = products.filter(section___id=section)
    else:
        organizer_instance = get_object_or_404(Organizer, slug=organizer)
        all_sections = Section.objects.filter(organizer=organizer_instance)
        filtered_products = []

        for section in all_sections:
            section_products = products.filter(section=section)
            filtered_products.extend(section_products)

        unsectioned_products = products.filter(section__isnull=True)
        filtered_products.extend(unsectioned_products)

        products = filtered_products
        # selectOrgenezer = get_object_or_404(Organizer, slug=organizer)
        # allSections = Section.objects.filter(organizer=selectOrgenezer)
        # prorList = []
        # for sectn in allSections:
        #     for prod in products:
        #         if prod.section is not None and prod.section == sectn:
        #             prorList.append(prod)

        # for prod in products:
        #     if prod.section is None:
        #         prorList.append(prod)

        # products = prorList

    page = request.query_params.get("page")
    paginator = Paginator(products, 9)

    try:
        products = paginator.page(page)
    except PageNotAnInteger:
        products = paginator.page(1)
    except EmptyPage:
        products = paginator.page(paginator.num_pages)

    if page == None:
        page = 1

    page = int(page)

    serializer = ProductSerializer(products, many=True)

    return Response(
        {"products": serializer.data, "page": page, "pages": paginator.num_pages}
    )


@api_view(["GET"])
def getProductsByOrganizer(request, organizer):

    section = request.query_params.get("section")
    # section = Section.objects.get(pk=section)
    products = Product.objects.filter(
        organizer__slug=organizer, isActive=True, isDeleted=False
    ).order_by("-createdAt")
    
    if organizer and organizer in ["caprices-morocco"]:
        products = Product.objects.filter(
            organizer__slug=organizer, isActive=True, isDeleted=False
        ).order_by("-index_order")

    if section:
        products = products.filter(section___id=section)
    else:
        organizer_instance = get_object_or_404(Organizer, slug=organizer)
        all_sections = Section.objects.filter(organizer=organizer_instance)
        filtered_products = []

        for section in all_sections:
            section_products = products.filter(section=section)
            filtered_products.extend(section_products)

        unsectioned_products = products.filter(section__isnull=True)
        filtered_products.extend(unsectioned_products)

        products = filtered_products

    page = request.query_params.get("page")
    if page is not None:
        paginator = Paginator(products, 9)

        try:
            products = paginator.page(page)
        except PageNotAnInteger:
            products = paginator.page(1)
        except EmptyPage:
            products = paginator.page(paginator.num_pages)

        pages = paginator.num_pages

    if page == None:
        page = 1
        pages = 1

    page = int(page)

    serializer = ProductSerializer(products, many=True)

    return Response({"products": serializer.data, "page": page, "pages": pages})


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getAdminProducts(request):
    organizer = Organizer.objects.get(user=request.user)

    query = request.query_params.get("keyword")

    if query == None:
        query = ""

    # products = Product.objects.all().filter(organizer=organizer).order_by('-createdAt')
    products = organizer.product_set.filter(isDeleted=False).order_by("-createdAt")

    page = request.query_params.get("page")
    paginator = Paginator(products, 12)

    try:
        products = paginator.page(page)
    except PageNotAnInteger:
        products = paginator.page(1)
    except EmptyPage:
        products = paginator.page(paginator.num_pages)

    if page == None:
        page = 1
    page = int(page)
    if page == 0:

        products = (
            organizer.product_set.all()
            .filter(isDeleted=False, isActive=True)
            .order_by("-createdAt")
        )

    serializer = ProductSerializer(products, many=True)
    return Response(
        {"products": serializer.data, "page": page, "pages": paginator.num_pages}
    )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getProductStats(request):

    products_count = Product.objects.all().count()
    products_active_count = Product.objects.filter(isActive=True).count()

    return Response({"all": products_count, "active": products_active_count})


# @api_view(['GET'])
# def getTopProducts(request):
#     products = Product.objects.filter(rating__gte=4).order_by('-rating')[0:5]
#     serializer = ProductSerializer(products, many=True)
#     return Response(serializer.data)


## get admin product


def isNum(data):
    try:
        int(data)
        return True
    except ValueError:
        return False


@api_view(["GET"])
# @permission_classes([IsOrganizer])
def getProduct(request, pk):
    # if (isNum(pk)):
    #     return Response({"detail":"Error "})
    # else :
    isNumber = False
    try:
        int(pk)
        isNumber = True
    except ValueError:
        isNumber = False
    if isNumber:

        product = get_object_or_404(Product, _id=pk)

        serializer = ProductSerializer(product, many=False)
        return Response(serializer.data)
    else:
        return Response({"detail": "Pas trouvé."}, status=400)


@api_view(["GET"])
def getProductBySlug(request, slug):
    product = get_object_or_404(Product, slug=slug, isActive=True, isDeleted=False)
    serializer = ProductSerializer(product, many=False)
    return Response(serializer.data)


# @api_view(['POST'])
# @permission_classes([IsOrganizer])
# def createProduct(request):
#     data = request.data
#     organizer = Organizer.objects.get(user=request.user)

#     serializer = ProductSerializer(data=request.data)

#     if serializer.is_valid(raise_exception=True):
#         serializer.save(organizer=organizer)
#         return Response(serializer.data)
#     return Response({"detail":"Product has not been added !"},status=400)


@api_view(["POST"])
@permission_classes([IsOrganizer])
def createProduct(request):
    data = request.data
    organizer = Organizer.objects.get(user=request.user, isActive=True)
    section = None
    if "section" in data and data["section"] != "":
        section = Section.objects.get(pk=data["section"])
    serializer = ProductSerializer(data=request.data)
    if serializer.is_valid(raise_exception=True):
        serializer.save(organizer=organizer, section=section)
        return Response(serializer.data)
    return Response({"detail": "Product has not been added !"}, status=400)


# @api_view(['PUT'])
# @permission_classes([IsOrganizer])
# def updateProduct(request, pk):
#     data = request.data
#     product  = get_object_or_404(Product,_id=pk)
#     section = None
#     if "section" in data :
#         section = Section.objects.get(_id=data["section"])
#     serializer = ProductSerializer(product,data=request.data,partial=True)

#     if serializer.is_valid(raise_exception=True):
#         serializer.save(section=section)
#         return Response({"detail":"Product has been updated !"})
#     return Response({"detail":"Product has not been updated !"},status=400)


@api_view(["PUT"])
@permission_classes([IsOrganizer])
def updateProduct(request, pk):
    data = request.data

    product = get_object_or_404(Product, _id=pk)
    section = None
    if "section" in data and data["section"] != "":
        section = Section.objects.get(pk=data["section"])

    hasVariation = data["hasVariation"]
    if not hasVariation:
        product.variation = None
        product.price = 0.0
        product.save()

    serializer = ProductSerializer(product, data=request.data, partial=True)

    if serializer.is_valid(raise_exception=True):
        serializer.save(section=section)
        return Response({"detail": "Product has been updated !"})
    return Response({"detail": "Product has not been updated !"}, status=400)


# @api_view(['DELETE'])
# @permission_classes([IsOrganizer])
# def deleteProduct(request, pk):
#     product = Product.objects.get(_id=pk)
#     if product.organizer == Organizer.objects.get(user=request.user):
#         product.delete()
#         return Response('Product Deleted')
#     else :
#         return Response('Error occured. Please try later !')


@api_view(["DELETE"])
@permission_classes([IsOrganizer])
def deleteProduct(request, pk):
    product = Product.objects.get(_id=pk)
    if product.organizer == Organizer.objects.get(user=request.user):
        # product.delete()
        product.isDeleted = True
        product.save()
        return Response("Product Deleted")
    else:
        return Response("Error occured. Please try later !")


@api_view(["POST"])
@permission_classes([IsOrganizer])
def createProductImages(request, pk):
    product = Product.objects.get(_id=pk)
    count = Images.objects.count()

    images = request.FILES.getlist("images")

    for image in images:
        Images.objects.create(
            name=product.name, product=product, orderImage=count + 1, image=image
        )
    return Response("Images was uploaded")


@api_view(["DELETE"])
@permission_classes([IsOrganizer])
def deleteProductImage(request, pk, imageId):

    try:
        product = Product.objects.get(_id=pk, isDeleted=False)

    except Product.DoesNotExist:
        return Response("Product was not found ")

    try:
        image = Images.objects.get(_id=imageId)
        image.delete()
        return Response("produt image was deleted ")
    except Images.DoesNotExist:
        return Response("produt image was not found ")


@api_view(["POST"])
@permission_classes([IsAdminUser])
def uploadImage(request):
    data = request.data

    product_id = data["product_id"]
    product = Product.objects.get(_id=product_id)

    product.image = request.FILES.get("image")
    product.save()

    return Response("Image was uploaded")


@api_view(["POST"])
@permission_classes([IsOrganizer])
def createProductVariation(request, pk):
    # Variation (name,attributes M:M)

    product = Product.objects.get(pk=pk)

    var = Variation.objects.create(name=request.data["name"], isPriceVary=True)
    if "attributes" in request.data:
        for i in request.data["attributes"]:
            att = VariationAttribute.objects.create(name=i["name"], price=i["price"])
            var.attributes.add(att)

        var.save()
        product.hasVariation = True
        product.variation = var
        product.save()
        return Response("Variation Added")
    return Response({"detail": "variation not added"})


@api_view(["PUT"])
@permission_classes([IsOrganizer])
def updateProductVariation(request, pk):
    varAtt = VariationAttribute.objects.get(pk=pk)
    varAtt.price = request.data["price"]
    # var.image = request.data["image"]
    varAtt.save()

    return Response("Variation updated")


@api_view(["DELETE"])
@permission_classes([IsOrganizer])
def deleteProductVariation(request, pk, varId):
    _var = Variation.objects.get(pk=varId)
    _var.delete()

    product = Product.objects.get(pk=pk)
    product.hasVariation = False
    product.save()

    return Response("Variation deleted")


@api_view(["PUT"])
@permission_classes([IsOrganizer])
def disableProduct(request, pk):
    product = Product.objects.get(pk=pk)
    product.isActive = request.data["isActive"]

    product.save()
    serializer = ProductSerializer(product, many=False)
    return Response(serializer.data)
