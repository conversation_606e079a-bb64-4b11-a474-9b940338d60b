from rest_framework.decorators import api_view, permission_classes
from base.permissions import IsOrganizer
from base.models import Coupon, Organizer, Product, Order
from rest_framework.response import Response
from base.permissions import IsOrganizer
from base.serializers import CouponSerializer
from rest_framework import status
from django.utils import timezone
from decimal import Decimal
from django.core.exceptions import ObjectDoesNotExist


# new


@api_view(["POST"])
@permission_classes([IsOrganizer])
def coupon_new_add(request):
    try:
        data = request.data
        organizerId = data["organizer"]
        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer and organizer.user == request.user:
                code = data["code"]
                valid_from = data["valid_from"]
                valid_to = data["valid_to"]
                discount = data["discount"]
                max_count = data["max_count"]
                has_max = data["has_max"]
                product = None
                if data["product"] != "":
                    product = Product.objects.get(_id=data["product"])

                coupon = Coupon.objects.create(
                    organizer=organizer,
                    product=product,
                    code=code,
                    valid_from=valid_from,
                    valid_to=valid_to,
                    discount=discount,
                    active=True,
                    has_max=has_max,
                    max_count=max_count,
                )
                if product:
                    product.coupon = coupon
                    product.save()

                serializer = CouponSerializer(coupon, many=False)

                return Response({"detail": "Coupon was added"})
            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
                )

        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as error:
        return Response(
            {
                "detail": "Désolé, une erreur s'est produite lors de l'envoi des données. Veuillez réessayer",
                "error": str(error),
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def coupon_new_list(request):
    try:
        organizerId = request.query_params.get("organizer")
        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer and organizer.user == request.user:
                # organizer = Organizer.objects.get(user=request.user)
                coupons = Coupon.objects.filter(organizer=organizer)

                serializer = CouponSerializer(coupons, many=True)

                return Response({"coupons": serializer.data})
            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
                )

        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# last


@api_view(["GET"])
@permission_classes([IsOrganizer])
def coupon_list(request):

    organizer = Organizer.objects.get(user=request.user)

    coupons = Coupon.objects.filter(organizer=organizer)

    serializer = CouponSerializer(coupons, many=True)

    return Response({"coupons": serializer.data})


@api_view(["GET", "DELETE"])
@permission_classes([IsOrganizer])
def coupon_detail(request, pk):
    coupon = Coupon.objects.get(pk=pk)
    if request.method == "DELETE":
        coupon.delete()
        return Response({"detail": "Coupon was deleted"})
    if request.method == "GET":
        serializer = CouponSerializer(coupon, many=False)
        return Response({"coupon": serializer.data})


@api_view(["POST"])
def coupon_apply(request):
    now = timezone.now()
    data = request.data

    if "code" and "order_id" in data:
        code = data["code"]
        order_id = data["order_id"]
        try:

            coupon = Coupon.objects.get(
                code__iexact=code, valid_from__lte=now, valid_to__gte=now, active=True
            )

            if coupon.has_max and (coupon.max_count <= 0 or coupon.max_count <= coupon.used):
                return Response(
                    {"detail": "Coupon not exist", "check": "here"}, status=400
                )

            order = Order.objects.get(pk=order_id)
            order.hasDiscount = True
            order.couponId = coupon.id
            # calculer discount
            total = 0
            isOk = False
            for i in order.orderitem_set.all():
                # print(i)
                product = Product.objects.get(_id=i.product._id)

                if coupon.product is not None:
                    if product._id == coupon.product._id:
                        # if coupon.code.contains("QTCO") and i.qty !=5:
                        #     return Response(
                        #         {"detail": "Coupon not exist for this product"}, status=400
                        #     )
                        total += (
                            i.price - (Decimal(float(coupon.discount / 100)) * i.price)
                        ) * i.qty
                        i.price = i.price - (
                            Decimal(float(coupon.discount / 100)) * i.price
                        )
                        i.save()
                        isOk = True
                    else:
                        total += i.price * i.qty
                else:
                    if product.organizer._id == coupon.organizer._id:
                        total += (
                            i.price - (Decimal(float(coupon.discount / 100)) * i.price)
                        ) * i.qty
                        i.price = i.price - (
                            Decimal(float(coupon.discount / 100)) * i.price
                        )
                        i.save()
                        isOk = True
                    else:
                        total += i.price * i.qty
                # print("discount applied for this product")

            order.code = coupon.code
            order.discount = coupon.discount
            order.totalPrice = total
            if isOk:
                # if coupon.has_max:
                #     coupon.max_count = coupon.max_count - 1
                # coupon.used = coupon.used + 1
                # coupon.save()
                order.save()
            else:
                return Response(
                    {"detail": "Coupon not exist for this product"}, status=400
                )

            return Response({"coupon_id": coupon.id})

        except Coupon.DoesNotExist:
            return Response({"detail": "Coupon not exist"}, status=400)
        except Exception as error:
            return Response(
                {
                    "detail": "Désolé, une erreur s'est produite lors de l'envoi des données. Veuillez réessayer",
                    "error": str(error),
                },
                status=401,
            )

    return Response({"coupon": ""})


# organizer
# code
# valid_from
# valid_to
# discount
# product


@api_view(["POST"])
@permission_classes([IsOrganizer])
def coupon_add(request):
    try:
        data = request.data
        organizer = Organizer.objects.get(user=request.user)
        code = data["code"]
        valid_from = data["valid_from"]
        valid_to = data["valid_to"]
        discount = data["discount"]
        max_count = data["max_count"]
        has_max = data["has_max"]
        product = None
        if data["product"] != "":
            product = Product.objects.get(_id=data["product"])

        coupon = Coupon.objects.create(
            organizer=organizer,
            product=product,
            code=code,
            valid_from=valid_from,
            valid_to=valid_to,
            discount=discount,
            active=True,
            has_max=has_max,
            max_count=max_count,
        )
        if product:
            product.coupon = coupon
            product.save()

        serializer = CouponSerializer(coupon, many=False)

        return Response({"detail": "Coupon was added"})
    except Exception as error:
        return Response(
            {
                "detail": "Désolé, une erreur s'est produite lors de l'envoi des données. Veuillez réessayer",
                "error": str(error),
            },
            status=401,
        )


@api_view(["POST"])
@permission_classes([IsOrganizer])
def coupon_disable(request, pk):

    found_organizer = Organizer.objects.get(user=request.user)
    coupon = Coupon.objects.get(id=pk)

    if coupon.organizer._id == found_organizer._id:
        if not coupon.active:
            return Response({"detail": "Coupon already disabled"})
        coupon.active = False
        coupon.save()
        return Response({"detail": "Coupon was disabled"})
    else:
        return Response(
            {"detail": "You are not authorized "}, status=status.HTTP_401_UNAUTHORIZED
        )
    return Response(
        {"detail": "Error, Coupons wasn't disabled "},
        status=status.HTTP_400_BAD_REQUEST,
    )
