from django.shortcuts import render, get_object_or_404

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsA<PERSON><PERSON>icated, IsAdminUser
from rest_framework.response import Response
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from base.permissions import IsOrganizer
from base.models import Ticket, Organizer, Order, TransferOrder, User
from base.serializers import TicketSerializer, OrganizerSerializer
from datetime import datetime
from rest_framework import status
from collections import defaultdict

from django.http import HttpResponse
import qrcode
from io import BytesIO
from django.core.exceptions import ObjectDoesNotExist

from base.utils import render_to_pdf, is_valid_uuid

# new


@api_view(["DELETE"])
@permission_classes([IsAdminUser])
def delete_ticket(request, pk):
    try:
        ticket = Ticket.objects.get(pk=pk)
        if ticket:
            # delete tecket
            ticket.delete()

            return Response({"detail": "This Ticket has been deleted"}, status=200)
        else:
            return Response({"detail": "Sorry, This Ticket Does Not Exist"}, status=401)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Ticket Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsAdminUser])
def getNewAllTickets(request):
    organizer_filter = request.query_params.get("organizer")
    status_filter = request.query_params.get("status")
    ticket_no_filter = request.query_params.get("q")

    tickets = Ticket.objects.all()

    # Filter by organizer
    if organizer_filter:
        try:
            organizer_check = Organizer.objects.get(pk=organizer_filter)
            tickets = tickets.filter(orderItem__organizer=organizer_check)
        except Organizer.DoesNotExist:
            return Response({"error": "Organizer not found"}, status=404)

    # Filter by status
    if status_filter and status_filter.lower() != "all":
        if status_filter.lower() == "scanned":
            tickets = tickets.filter(isScanned=True)
        elif status_filter.lower() == "unscanned":
            tickets = tickets.filter(isScanned=False)
    if ticket_no_filter and ticket_no_filter != "":
        tickets = tickets.filter(ticketNo__icontains=ticket_no_filter)

    # Pagination
    page = request.query_params.get("page")
    paginator = Paginator(tickets, 20)

    try:
        tickets_page = paginator.page(page)
    except PageNotAnInteger:
        tickets_page = paginator.page(1)
    except EmptyPage:
        tickets_page = paginator.page(paginator.num_pages)

    page = int(page) if page else 1

    serializer = TicketSerializer(tickets_page, many=True)
    return Response(
        {
            "tickets": serializer.data,
            "page": page,
            "pages": paginator.num_pages,
            "count": paginator.count,
        }
    )


# @api_view(["GET"])
# @permission_classes([IsAdminUser])
# def getNewAllTickets(request):
#     organizer_filter = request.query_params.get("organizer")
#     status_filter = request.query_params.get("status")
#     tickets = Ticket.objects.all()

#     if organizer_filter and organizer_filter != "":
#         organizer_check = Organizer.objects.get(pk=organizer_filter)
#         tickets = Ticket.objects.all()
#         if organizer_check:
#             # by organizer
#             # organizer - > OrderItem
#             ticket_list = []
#             for tick in tickets:
#                 organizer = tick.orderItem.organizer
#                 if organizer == organizer_check:
#                     ticket_list.append(tick)
#             tickets = ticket_list
#     if status_filter and status_filter != "" and status_filter != "all":
#         if status_filter == "scanned":
#             tickets = tickets.filter(isScanned=True)
#         else:
#             tickets = tickets.filter(isScanned=False)

#     page = request.query_params.get("page")
#     paginator = Paginator(tickets, 20)

#     try:
#         tickets = paginator.page(page)
#     except PageNotAnInteger:
#         tickets = paginator.page(1)
#     except EmptyPage:
#         tickets = paginator.page(paginator.num_pages)

#     if page == None:
#         page = 1

#     page = int(page)

#     serializer = TicketSerializer(tickets, many=True)
#     return Response(
#         {
#             "tickets": serializer.data,
#             "page": page,
#             "pages": paginator.num_pages,
#             "count": paginator.count,
#         }
#     )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewTickets(request, pk):
    try:
        organizer_check = Organizer.objects.get(pk=pk)
        if organizer_check:
            # by organizer
            # organizer - > OrderItem
            _tickets = Ticket.objects.all()
            tickets = []
            for tick in _tickets:
                organizer = tick.orderItem.organizer
                if organizer == organizer_check:
                    # print("ok")
                    tickets.append(tick)
            serializer = TicketSerializer(tickets, many=True)
            return Response({"tickets": serializer.data})
        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["POST"])
@permission_classes([IsOrganizer])
def confirmNewScannTicket(request, pk):
    try:
        organizer_check = Organizer.objects.get(pk=pk)
        if organizer_check:
            qrcode = request.data["code"]
            ticket = Ticket.objects.get(ticketNo=qrcode)
            organizer = ticket.orderItem.organizer
            if organizer == organizer_check:
                if not ticket.isScanned:
                    ticket.isScanned = True
                    ticket.scannedAt = datetime.now()
                    ticket.save()
                    return Response({"detail": "Scann Confirmed ! "}, status=200)
                else:
                    return Response({"detail": "Already Confirmed ! "}, status=200)
            else:
                return Response(
                    {"detail": "This ticket cannot be confirmed"}, status=400
                )
        else:
            return Response({"detail": "This ticket cannot be confirmed"}, status=400)
    except ObjectDoesNotExist:
        return Response({"detail": "This ticket cannot be confirmed"}, status=400)
    except Exception as e:
        print(e)
        return Response(
            {"detail": "This ticket cannot be confirmed"},
            status=400,
        )


@api_view(["POST"])
@permission_classes([IsOrganizer])
def scannNewTicket(request, pk):
    try:
        organizer_check = Organizer.objects.get(pk=pk)
        if organizer_check:
            qrcode = request.data["code"]
            try:
                ticket = Ticket.objects.get(ticketNo=qrcode)
                organizer = ticket.orderItem.organizer
                # check if organizer
                if organizer == organizer_check:
                    serializer = TicketSerializer(ticket, many=False)
                    if not ticket.isScanned:
                        return Response({"ticket": serializer.data})
                    return Response(
                        {"detail": "Already Scanned", "ticket": serializer.data},
                        status=200,
                    )
                else:
                    return Response({"detail": "Not Found "}, status=400)
            except Ticket.DoesNotExist:
                return Response({"detail": "Not Found "}, status=400)
        else:
            return Response({"detail": "Not Found "}, status=400)
    except ObjectDoesNotExist:
        return Response({"detail": "Not Found "}, status=400)
    except Exception as e:
        print(e)
        return Response(
            {"detail": "Not Found "},
            status=400,
        )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getTicketsStatsScan(request, pk):
    try:
        organizer = Organizer.objects.get(pk=pk)
        if organizer:
            tickets_scanned = Ticket.objects.filter(
                orderItem__organizer=organizer, isScanned=True
            ).count()

            tickets_not_scanned = Ticket.objects.filter(
                orderItem__organizer=organizer, isScanned=False
            ).count()
            # serializer = TicketSerializer(tickets, many=True)
            return Response(
                {
                    "tickets_scanned": tickets_scanned,
                    "tickets_not_scanned": tickets_not_scanned,
                }
            )
        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewOrganizerEvents(request):
    organizers = Organizer.objects.filter(user=request.user)
    # my_events = Organizer.objects.get(user=request.user)
    serializer = OrganizerSerializer(organizers, many=True)
    return Response(serializer.data)


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewTicketsScanned(request):
    try:
        organizerId = request.query_params.get("organizer")
        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer and organizer.user == request.user:

                tickets_scanned = Ticket.objects.filter(
                    orderItem__organizer=organizer,
                    # isScanned=True
                ).order_by("-scannedAt")

                page = request.query_params.get("page")
                paginator = Paginator(tickets_scanned, 20)

                try:
                    tickets_scanned = paginator.page(page)
                except PageNotAnInteger:
                    tickets_scanned = paginator.page(1)
                except EmptyPage:
                    tickets_scanned = paginator.page(paginator.num_pages)

                if page == None:
                    page = 1

                page = int(page)

                serializer = TicketSerializer(tickets_scanned, many=True)
                return Response(
                    {
                        "tickets": serializer.data,
                        "page": page,
                        "pages": paginator.num_pages,
                    }
                )
            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
                )

        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewTicketsStats(request):
    try:
        organizerId = request.query_params.get("organizer")
        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer and organizer.user == request.user:
                tickets_scanned = Ticket.objects.filter(
                    orderItem__organizer=organizer, isScanned=True
                ).count()

                tickets_not_scanned = Ticket.objects.filter(
                    orderItem__organizer=organizer, isScanned=False
                ).count()

                # serializer = TicketSerializer(tickets, many=True)
                return Response(
                    {
                        "tickets_scanned": tickets_scanned,
                        "tickets_not_scanned": tickets_not_scanned,
                    }
                )
            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
                )

        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# last


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getOrganizerEvents(request):
    my_events = Organizer.objects.get(user=request.user)
    serializer = OrganizerSerializer(my_events, many=False)
    return Response(serializer.data)


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getTickets(request):
    # by organizer
    # organizer - > OrderItem
    _tickets = Ticket.objects.all()
    tickets = []
    for tick in _tickets:
        organizer = tick.orderItem.organizer
        if organizer == Organizer.objects.get(user=request.user):
            # print("ok")
            tickets.append(tick)
    serializer = TicketSerializer(tickets, many=True)
    return Response({"tickets": serializer.data})


# @api_view(['GET'])
# @permission_classes([IsAuthenticated])
# def getOrderTickets(request,pk):
#     user = request.user
#     order = Order.objects.get(pk=pk)
#     if user == order.user :
#         tickets = Ticket.objects.filter(orderItem__in=order.orderitem_set.all())
#         serializer = TicketSerializer(tickets, many=True)
#         return Response(serializer.data)
#     else :
#         return Response({"detail":"Not authorized "},status=401)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def getOrderTickets(request, pk):
    user = request.user
    try:
        order = Order.objects.get(pk=pk)
    except Order.DoesNotExist:
        return Response({"detail": "Order not found"}, status=404)

    # if user != order.user:
    #     return Response({"detail": "Not authorized"}, status=401)

    # Filter tickets for the order
    tickets = Ticket.objects.filter(orderItem__order=order)

    # Group tickets by organizerName
    tickets_by_organizer = defaultdict(list)
    for ticket in tickets:
        tickets_by_organizer[ticket.organizerName].append(ticket)

    # Serialize the grouped tickets
    organizers_data = []
    for organizer_name, tickets in tickets_by_organizer.items():
        serializer = TicketSerializer(tickets, many=True)
        organizers_data.append(
            {"organizer": organizer_name, "tickets": serializer.data}
        )
        # organizers_data[organizer_name] = serializer.data
    return Response(organizers_data)


# @api_view(['POST'])
# @permission_classes([IsAuthenticated])
# def transferTicket(request):
#     user = request.user
#     ## Scenario
#     # (1) check the recieved user
#     to_user = User.objects.get(email=request.data["emailTo"])

#     # (2) check the ticket number (it should not be transfered before)


#     # (3) check the order number (order should be paid )


#     # (4) create transfer
#     # (5) make the previous ticket invisible and disable for the sender user
#     # (6) activate it for the reciever user

#     TransferOrder.objects.create(
#         from_user= request.user,
#         to_user = to_user,
#         ticketNo = request.data["ticketNo"],
#         orderNo = request.data["orderNo"])

#     return Response({'detail': "Tciket Transfered"})


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getTicketsStats(request):

    tickets_scanned = Ticket.objects.filter(
        orderItem__organizer__user=request.user, isScanned=True
    ).count()

    tickets_not_scanned = Ticket.objects.filter(
        orderItem__organizer__user=request.user, isScanned=False
    ).count()

    # serializer = TicketSerializer(tickets, many=True)
    return Response(
        {"tickets_scanned": tickets_scanned, "tickets_not_scanned": tickets_not_scanned}
    )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getTicketsScanned(request):

    tickets_scanned = Ticket.objects.filter(
        orderItem__organizer__user=request.user,
        # isScanned=True
    ).order_by("-scannedAt")

    page = request.query_params.get("page")
    paginator = Paginator(tickets_scanned, 20)

    try:
        tickets_scanned = paginator.page(page)
    except PageNotAnInteger:
        tickets_scanned = paginator.page(1)
    except EmptyPage:
        tickets_scanned = paginator.page(paginator.num_pages)

    if page == None:
        page = 1

    page = int(page)

    serializer = TicketSerializer(tickets_scanned, many=True)
    return Response(
        {"tickets": serializer.data, "page": page, "pages": paginator.num_pages}
    )


def getQrCode(request):
    data = request.GET.get("code")
    # Generate the QR code image
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=20,
        border=1,
    )
    qr.add_data(data)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")

    # Save the image to a BytesIO object
    buffer = BytesIO()
    img.save(buffer, format="PNG")

    # Return the image as a response
    response = HttpResponse(buffer.getvalue(), content_type="image/png")
    return response


@api_view(["POST"])
@permission_classes([IsOrganizer])
def scannTicket(request):
    qrcode = request.data["code"]
    try:
        ticket = Ticket.objects.get(ticketNo=qrcode)
        organizer = ticket.orderItem.organizer
        # check if organizer
        if organizer == Organizer.objects.get(user=request.user):
            serializer = TicketSerializer(ticket, many=False)
            if not ticket.isScanned:
                return Response({"ticket": serializer.data})
            return Response(
                {"detail": "Already Scanned", "ticket": serializer.data}, status=200
            )
        else:
            return Response({"detail": "Not Found "}, status=400)
    except Ticket.DoesNotExist:
        return Response({"detail": "Not Found "}, status=400)


@api_view(["POST"])
@permission_classes([IsOrganizer])
def confirmScannTicket(request):
    qrcode = request.data["code"]
    ticket = Ticket.objects.get(ticketNo=qrcode)
    organizer = ticket.orderItem.organizer
    if organizer == Organizer.objects.get(user=request.user):
        if not ticket.isScanned:
            ticket.isScanned = True
            ticket.scannedAt = datetime.now()
            ticket.save()
            return Response({"detail": "Scann Confirmed ! "}, status=200)
        else:
            return Response({"detail": "Already Confirmed ! "}, status=200)
    else:
        return Response({"detail": "This ticket cannot be confirmed"}, status=400)


@api_view(["GET"])
# @permission_classes([IsAuthenticated])
def exportTicketPdf(request, pk):
    try:
        ticket = Ticket.objects.get(pk=pk)
        # if ticket.firstName == request.user.first_name and  ticket.lastName == request.user.last_name:
        data = {"ticket": ticket}
        pdf = render_to_pdf("pdf/ticket.html", data)
        return HttpResponse(pdf, content_type="application/pdf")
        # else :
        #     return Response({"detail":"You are not allowed "},status=401)
    except Ticket.DoesNotExist:
        return Response({"detail": "Ticket does not exist "}, status=400)
