from django.shortcuts import render

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import Is<PERSON><PERSON><PERSON><PERSON>ted, IsAdminUser
from rest_framework.response import Response
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from base.permissions import IsOrganizer
from base.models import OrganizerCategory
from base.serializers import OrganizerCategorySerializer
from django.core.exceptions import ObjectDoesNotExist

from rest_framework import status

# new


@api_view(["DELETE"])
@permission_classes([IsAdminUser])
def delete_catergory(request, pk):
    try:
        category = OrganizerCategory.objects.get(pk=pk)
        if category:
            category.delete()

            return Response(
                {"detail": "This Category has been deleted successfull"}, status=200
            )
        else:
            return Response(
                {"detail": "Sorry, This Category Does Not Exist"}, status=401
            )

    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Category Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["PUT"])
@permission_classes([IsAdminUser])
def update_category(request, pk):
    try:
        print(request.data)
        category = OrganizerCategory.objects.get(pk=pk)
        if category:

            category_image = category.image
            if (
                "category_image" in request.data
                and request.data["category_image"] != ""
            ):
                category_image = request.FILES.get("category_image")

            category.name = request.data["category_name"]
            category.image = category_image
            category.is_active = request.data["is_active"]

            category.save()

            return Response({"detail": "This Category has been updated successfully."})
        else:
            return Response(
                {"detail": "Sorry, This Category Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Category Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsAdminUser])
def get_catergory_detail(request, pk):
    try:
        category = OrganizerCategory.objects.get(pk=pk)
        if category:
            serializer = OrganizerCategorySerializer(category, many=False)
            return Response(data={"category": serializer.data})
        else:
            return Response(
                {"detail": "Sorry, This Category Does Not Exist"}, status=401
            )

    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Category Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["POST"])
@permission_classes([IsAdminUser])
def create_catergory(request):
    try:
        category_image = None
        if "category_image" in request.data and request.data["category_image"] != "":
            category_image = request.FILES.get("category_image")
        OrganizerCategory.objects.create(
            name=request.data["category_name"],
            image=category_image,
            is_active=request.data["is_active"],
        )
        return Response({"detail": "This category has been added successfully"})

    except Exception as e:
        print(f"Error creating order: {e}")
        return Response(
            {"detail": "Something went wrong while creating the category."}, status=401
        )


# last


@api_view(["GET"])
# @permission_classes([IsOrganizer])
def getCategories(request):

    categories = OrganizerCategory.objects.all()

    serializer = OrganizerCategorySerializer(categories, many=True)

    return Response({"categories": serializer.data})


@api_view(["POST"])
@permission_classes([IsOrganizer])
def createCategory(request):
    data = request.data
    name = data["name"]
    image = request.FILES.get("image")

    category = OrganizerCategory.objects.create(name=name, image=image)

    serializer = OrganizerCategorySerializer(category, many=False)
    return Response({"category": serializer.data})


@api_view(["PUT"])
@permission_classes([IsAdminUser])
def updateCategory(request, pk):
    data = request.data
    category = OrganizerCategory.objects.get(_id=pk)
    category.name = data["name"]

    if data["image"] != "":
        category.image = request.FILES.get("image")

    category.save()
    serializer = OrganizerCategorySerializer(category, many=False)
    return Response({"category": serializer.data})


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def deleteCategory(request, pk):
    category = OrganizerCategory.objects.get(_id=pk)
    category.delete()
    serializer = OrganizerCategorySerializer(category, many=False)
    return Response({"category": serializer.data})
