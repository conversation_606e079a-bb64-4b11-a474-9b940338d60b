
from django.shortcuts import render,HttpResponse
import csv
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response

from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.shortcuts import get_object_or_404

from base.models import (
    Product,Section, Order, OrderItem,
    Organizer,Ticket,EmailStatus,InfoClient,
    VariationAttribute)

from base.serializers import ProductSerializer, OrderSerializer, OrderOrganizerSerializer, OrderSerializerExcel, UserSerializer
from rest_framework import status
from datetime import datetime
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.db.models import Q

from base.utils import render_to_pdf, is_valid_uuid #created in step 4

import io   
import qrcode.image.svg 
import qrcode
from io import BytesIO


@api_view(['GET'])
@permission_classes([IsAdminUser])
def getAdminOrders(request):
    status = request.query_params.get("status")
    organizerId = request.query_params.get("organizerId",None)
    monthId = request.query_params.get("monthId",None)
    
    search = request.query_params.get("q",None)
    # orders = Order.objects.filter(isPaid=True).order_by("-createdAt")
    orders = Order.objects.filter(isPaid=True).order_by("-createdAt")
    
    if monthId != "-1" :
        orders = orders.filter(createdAt__month = monthId,createdAt__year = 2023)
    
    if organizerId != "all" and organizerId is not None:
        organizer = Organizer.objects.get(pk=organizerId)
        _orderitem = OrderItem.objects.filter(organizer=organizer)
        orders = orders.filter(isPaid=True,orderitem__in=_orderitem).order_by("-createdAt").distinct()
        
    if status and status != 'All':
        orders = orders.filter(isOffline=status)
    if  status == 'All' :
        orders = orders

    if search :
        search_terms = search.split()
        if len(search_terms) == 1:  
            if is_valid_uuid(search_terms[0]):
                orders = orders.filter(Q(_id__contains=search_terms[0]))
            else :
                orders = orders.filter(Q(user__first_name__icontains=search_terms[0]) | Q(user__last_name__icontains=search_terms[0]) )
        elif len(search_terms) == 2:
            orders = orders.filter(Q(user__first_name__icontains=search_terms[0]) | Q(user__last_name__icontains=search_terms[1]) )

    page = request.query_params.get('page')
    paginator = Paginator(orders, 20)

    try:
        orders = paginator.page(page)
    except PageNotAnInteger:
        orders = paginator.page(1)
    except EmptyPage:
        orders = paginator.page(paginator.num_pages)

    if page == None:
        page = 1

    page = int(page)

    serializer = OrderSerializer(orders, many=True)
    return Response({'orders': serializer.data, 'page': page,"pages":paginator.num_pages,"count":paginator.count})