from django.shortcuts import render, HttpResponse
import csv
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response

from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.shortcuts import get_object_or_404
from django.core.exceptions import ObjectDoesNotExist
from base.models import (
    Product,
    Section,
    Order,
    OrderItem,
    Organizer,
    Ticket,
    EmailStatus,
    InfoClient,
    VariationAttribute,
    Coupon,
    TransferOrder,
)
import traceback
from base.serializers import (
    ProductSerializer,
    OrderSerializer,
    OrderOrganizerSerializer,
    OrderSerializerExcel,
    UserSerializer,
    VariationAttributeSerializer,
)
from base.permissions import IsOrganizer

from rest_framework import status
from datetime import datetime

from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.db.models import Q

from base.utils import render_to_pdf, is_valid_uuid  # created in step 4

import io
import qrcode.image.svg
import qrcode
from io import BytesIO
from base.views.notification_views import save_notification
from django.utils import timezone
import requests

# new


@api_view(["GET"])
# @permission_classes([IsAuthenticated])
def updateOrderToPaidConfirmOrdr(request, pk):
    try:
        order = Order.objects.get(_id=pk)
        # print(order.email)
        user = order.user
        emailFrom = "AyLink <<EMAIL>>"

        # Handle both logged-in users and guest users
        if user:
            # Logged-in user
            to = [user.email]
            toEmail = user.email
            first_name = user.first_name
            last_name = user.last_name
        else:
            # Guest user
            to = [order.guest_email]
            toEmail = order.guest_email
            # Split guest_name into first and last name
            name_parts = order.guest_name.split(' ', 1)
            first_name = name_parts[0] if name_parts else ""
            last_name = name_parts[1] if len(name_parts) > 1 else ""

        subjectEmail = "Aylink Booking confirmation"
        orderid = order._id

        order_len = Order.objects.filter(isPaid=True).count()

        subject, from_email = subjectEmail, emailFrom

        if order.isPaid:
            show_mail = False
             # send email to the user
            html_content = render_to_string(
                "order_confirmation.html",
                {
                    "order": order,
                    "items": order.orderitem_set.all,
                    "orderid": orderid,
                    "showmail": show_mail,
                },
            )  # render with dynamic value

            text_content = strip_tags(
                html_content
            )  # Strip the html tag. So people can see the pure text at least.
            # msg = EmailMultiAlternatives(subject, text_content, from_email, ["<EMAIL>"])
            msg = EmailMultiAlternatives(subject, text_content, from_email, to)

            msg.attach_alternative(html_content, "text/html")

            sent_count = msg.send()

            # Only create EmailStatus for logged-in users
            if user:
                EmailStatus.objects.create(
                    user=user, sent_count=sent_count, status="ORDER PAID"
                )

            
            return Response({"detail": "Order already paid, and mail sending"}, status=200)

        # if order.totalPrice != 0:
        #     return Response({"detail": "Order was not confirmed price"}, status=400)

        if order.hasDiscount and order.code:
            now = timezone.now()
            data = request.data
            coupon = Coupon.objects.get(
                code__iexact=order.code,
                valid_from__lte=now,
                valid_to__gte=now,
                active=True,
            )
            # if coupon:
            #     if coupon.has_max and coupon.max_count <= coupon.used:
            #         return Response(
            #             {"detail": "Code Coupon has expired please try again"},
            #             status=status.HTTP_400_BAD_REQUEST,
            #         )
            # else:
            #     return Response(
            #         {"detail": "Code Coupon has expired please try again"},
            #         status=status.HTTP_400_BAD_REQUEST,
            #     )

        if order.orderitem_set.all() and len(order.orderitem_set.all()) > 0:
            # check er
            for i in order.orderitem_set.all():
                product = Product.objects.get(_id=i.product._id)

                if not product.isActive or product.isDeleted:
                    return Response(
                        {
                            "detail": "NOT ACTIVE : One or many products is no longer availabe !"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                # condition on varia also
                if not order.hasDiscount and not product.hasVariation:
                    if (
                        float(product.price) != float(i.price)
                        or int(i.qty) > product.countInStock
                    ):
                        return Response(
                            {
                                "detail": "One or many items are updated ! Please double update your cart "
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                if not order.hasDiscount and product.hasVariation:
                    variationAtt = get_object_or_404(
                        VariationAttribute, pk=i.variationAttributeId.id
                    )
                    if (
                        float(variationAtt.price) != float(i.price)
                        or int(i.qty) > product.countInStock
                    ):
                        return Response(
                            {
                                "detail": "One or many items are updated ! Please double update your cart "
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                # add this 27-2-25
                if product.organizer.is_daily:
                    tickets = Ticket.objects.filter(
                        Q(orderItem__product=product) & Q(event_date=i.event_date)
                    )

                    if tickets.count() + int(i.qty) > product.countInStock:
                        return Response(
                            {
                                "detail": "One or many items are updated ! Please double update your cart "
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

            _total = 0.0
            for i in order.orderitem_set.all():
                _total += float(i.price) * float(i.qty)

            if float(order.totalPrice) != float(_total):
                return Response({"detail": "Something went wrong "}, status=400)

        order.isPaid = True
        order.paidAt = timezone.now()
        order.save()

        show_mail = False
        for i in order.orderitem_set.all():
            if i.organizer.show_mail:
                show_mail = True
            if i.qty > 1:
                for item in range(1, i.qty + 1):
                    check_isticket_customization = i.isticket_customization
                    nameticket = ""

                    if check_isticket_customization and i.list_ticket_name != "":
                        reconstructed_list = i.list_ticket_name.split("||")

                        if len(reconstructed_list) >= item:
                            nameticket = reconstructed_list[item - 1]
                        else:
                            nameticket = ""

                    t = Ticket.objects.create(
                        orderItem=i,
                        price=i.price,
                        paidAt=i.order.paidAt,
                        organizerPhoto=i.organizer.cover,
                        organizerLogo=i.organizer.avatar,
                        organizerDate=i.organizer.organizer_date,
                        organizerName=i.organizer.name,
                        firstName=first_name,
                        lastName=last_name,
                        orderNo=i.order._id,
                        productName=i.product.name,
                        variationName=i.variationName,
                        variationValue=i.variationValue,
                        customization=i.customization,
                        typeOrder=i.order.isOffline,
                        # add 27-2-25
                        is_daily=i.is_daily,
                        has_date=i.has_date,
                        event_date=i.event_date,
                        product_date=i.product_date,
                        isticket_customization=i.isticket_customization,
                        ticket_name=nameticket,
                    )
            elif i.qty == 1:
                check_isticket_customization = i.isticket_customization
                nameticket = ""

                if check_isticket_customization and i.list_ticket_name != "":
                    reconstructed_list = i.list_ticket_name.split("||")

                    if len(reconstructed_list) >= 1:
                        nameticket = reconstructed_list[0]
                    else:
                        nameticket = ""
                        
                if i.product.name in ["5 for 4 Group Ticket - 3 Day Pass","5 for 4 Group Ticket - Friday 09/05","5 for 4 Group Ticket - Saturday 10/05","5 for 4 Group Ticket - Sunday 11/05"]:
                    for item in range(1, 6):
                        Ticket.objects.create(
                            orderItem=i,
                            price=i.price,
                            paidAt=i.order.paidAt,
                            organizerPhoto=i.organizer.cover,
                            organizerLogo=i.organizer.avatar,
                            organizerDate=i.organizer.organizer_date,
                            organizerName=i.organizer.name,
                            firstName=first_name,
                            lastName=last_name,
                            orderNo=i.order._id,
                            productName=i.product.name,
                            variationName=i.variationName,
                            variationValue=i.variationValue,
                            customization=i.customization,
                            typeOrder=i.order.isOffline,
                            # add 27-2-25
                            is_daily=i.is_daily,
                            has_date=i.has_date,
                            event_date=i.event_date,
                            product_date=i.product_date,
                            isticket_customization=i.isticket_customization,
                            ticket_name=nameticket,
                        )
                else:
                    Ticket.objects.create(
                        orderItem=i,
                        price=i.price,
                        paidAt=i.order.paidAt,
                        organizerPhoto=i.organizer.cover,
                        organizerLogo=i.organizer.avatar,
                        organizerDate=i.organizer.organizer_date,
                        organizerName=i.organizer.name,
                        firstName=first_name,
                        lastName=last_name,
                        orderNo=i.order._id,
                        productName=i.product.name,
                        variationName=i.variationName,
                        variationValue=i.variationValue,
                        customization=i.customization,
                        typeOrder=i.order.isOffline,
                        # add 27-2-25
                        is_daily=i.is_daily,
                        has_date=i.has_date,
                        event_date=i.event_date,
                        product_date=i.product_date,
                        isticket_customization=i.isticket_customization,
                        ticket_name=nameticket,
                    )
            product = Product.objects.get(_id=i.product._id)
            if not product.organizer.is_daily:
                product.countInStock -= int(i.qty)
            product.save()

        if order.hasDiscount and order.code:
            now = timezone.now()
            data = request.data
            coupon = Coupon.objects.get(
                code__iexact=order.code,
                valid_from__lte=now,
                valid_to__gte=now,
                active=True,
            )
            if coupon:
                if coupon.has_max:
                    coupon.max_count = coupon.max_count - 1
                coupon.used = coupon.used + 1
                coupon.save()

        # send email to the user
        html_content = render_to_string(
            "order_confirmation.html",
            {
                "order": order,
                "items": order.orderitem_set.all,
                "orderid": orderid,
                "showmail": show_mail,
            },
        )  # render with dynamic value

        text_content = strip_tags(
            html_content
        )  # Strip the html tag. So people can see the pure text at least.
        msg = EmailMultiAlternatives(subject, text_content, from_email, ["<EMAIL>"])
        # msg = EmailMultiAlternatives(subject, text_content, from_email, to)

        msg.attach_alternative(html_content, "text/html")

        sent_count = msg.send()

        # Only create EmailStatus for logged-in users
        if user:
            EmailStatus.objects.create(
                user=user, sent_count=sent_count, status="ORDER PAID"
            )

        
        return Response({"detail": "Order was confirmed"})
    except ObjectDoesNotExist:
        return Response({"detail": "Order was not confirmed ex"}, status=400)
    except Exception as error:
        print(error)
        error_details = traceback.format_exc()
        print("Full Traceback:\n", error_details)
        return Response(
            {
                "detail": "Order was not confirmed",
                "error": str(error),
                "trace": error_details,
            },
            status=400,
        )

# 



@api_view(["GET"])
@permission_classes([IsAdminUser])
def exportOrdersToExcel(request):
    try:
        
        search = request.query_params.get("search")
        status = request.query_params.get("status")
        organizerId = request.query_params.get("organizer")
        
        orders = Order.objects.filter(isPaid=True).order_by("-createdAt").distinct()
        
        
        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer:
                _orderitem = OrderItem.objects.filter(organizer=organizer)
                orders = orders.filter(orderitem__in=_orderitem)
            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
                )
            
        if status and status != "All":
            orders = orders.filter(isOffline=status)
        if status == "All":
            orders = orders
            
        if search and search != "":
            search_terms = search.split()
            if len(search_terms) == 1:
                if is_valid_uuid(search_terms[0]):
                    orders = orders.filter(Q(_id__contains=search_terms[0]))
                else:
                    orders = orders.filter(
                        Q(user__first_name__icontains=search_terms[0])
                        | Q(user__last_name__icontains=search_terms[0])
                        | Q(_id__contains=search_terms[0])
                    )

            elif len(search_terms) == 2:
                orders = orders.filter(
                    Q(user__first_name__icontains=search_terms[0])
                    | Q(user__last_name__icontains=search_terms[1])
                )

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = 'attachment; filename="export_.csv"'

        serializer = OrderSerializerExcel(
            orders,
            many=True,
            context={"user": request.user, "organizer": organizerId},
        )
        header = OrderSerializerExcel.Meta.fields
        writer = csv.DictWriter(
            response,
            fieldnames=[
                "Date",
                "firstName",
                "lastName",
                "phone",
                "email",
                "items",
                "totalPrice",
                "statut",
            ],
        )
        writer.writeheader()

        for row in serializer.data:
            typeOrder = "Online"
            first_name = row["user"]["first_name"]
            last_name = row["user"]["last_name"]
            email = row["user"]["email"]
            phone = row["user"]["phone"]
            # Check if 'isOffline' key exists in the row before accessing it
            if row.get("isOffline", False):
                typeOrder = "Offline"
                first_name = row["infoClient"]["firstName"]
                last_name = row["infoClient"]["lastName"]
                email = row["infoClient"]["email"]
                phone = row["infoClient"]["phone"]
            _row = {
                "Date": row["paidAt"],
                "firstName": first_name,
                "lastName": last_name,
                "phone": phone,
                "email": email,
                "items": row["orderItems"],
                "totalPrice": row["totalPrice"],
                "statut": typeOrder,
            }
            writer.writerow(_row)

        return response
        
    
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getOrdersByProduct(request, pk):
    try:
        product = Product.objects.get(pk=pk)
        organizerId = request.query_params.get("organizer")
        if product:

            _orderitem = OrderItem.objects.filter(product=product)
            orders = (
                Order.objects.filter(orderitem__in=_orderitem, isPaid=True)
                .order_by("-createdAt")
                .distinct()
            )

            page = request.query_params.get("page")
            # paginator = Paginator(orders, 1)
            paginator = Paginator(orders, 20)

            try:
                orders = paginator.page(page)
            except PageNotAnInteger:
                orders = paginator.page(1)
            except EmptyPage:
                orders = paginator.page(paginator.num_pages)

            if page == None:
                page = 1

            page = int(page)

            serializer = OrderOrganizerSerializer(
                orders,
                context={"user": request.user, "organizer": product.organizer._id},
                many=True,
            )
            return Response(
                {
                    "orders": serializer.data,
                    "page": page,
                    "pages": paginator.num_pages,
                    "count": paginator.count,
                }
            )

        else:
            return Response(
                {"detail": "Sorry, This Product Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Product Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["DELETE"])
@permission_classes([IsAdminUser])
def delete_order(request, pk):
    try:
        order = Order.objects.get(_id=pk)
        if order:
            # delete tecket
            Ticket.objects.filter(orderNo=pk).delete()
            TransferOrder.objects.filter(orderNo=pk).delete()
            order.delete()

            return Response({"detail": "This order has been deleted"}, status=200)
        else:
            return Response({"detail": "Sorry, This Order Does Not Exist"}, status=401)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Order Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
# @permission_classes([IsAdminUser])
def getOrdersList(request):
    organizer_filter = request.query_params.get("organizer")
    status = request.query_params.get("status")
    search = request.query_params.get("q", None)

    orders = Order.objects.filter(isPaid=True).order_by("-createdAt").distinct()

    if organizer_filter and organizer_filter != "":
        organizer = Organizer.objects.get(pk=organizer_filter)
        if organizer:
            _orderitem = OrderItem.objects.filter(organizer=organizer)
            orders = orders.filter(orderitem__in=_orderitem)

    if status and status != "All":
        orders = orders.filter(isOffline=status)
    if status == "All":
        orders = orders

    if search:
        search_terms = search.split()
        if len(search_terms) == 1:
            if is_valid_uuid(search_terms[0]):
                orders = orders.filter(Q(_id__contains=search_terms[0]))
            else:
                orders = orders.filter(
                    Q(user__first_name__icontains=search_terms[0])
                    | Q(user__last_name__icontains=search_terms[0])
                    | Q(_id__contains=search_terms[0])
                )

        elif len(search_terms) == 2:
            orders = orders.filter(
                Q(user__first_name__icontains=search_terms[0])
                | Q(user__last_name__icontains=search_terms[1])
            )

    page = request.query_params.get("page")
    paginator = Paginator(orders, 20)

    try:
        orders = paginator.page(page)
    except PageNotAnInteger:
        orders = paginator.page(1)
    except EmptyPage:
        orders = paginator.page(paginator.num_pages)

    if page == None:
        page = 1

    page = int(page)

    serializer = OrderSerializer(orders, context={"user": request.user}, many=True)
    return Response(
        {
            "orders": serializer.data,
            "page": page,
            "pages": paginator.num_pages,
            "count": paginator.count,
        }
    )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewOrganizerOrderById(request, pk):
    try:
        organizerId = request.query_params.get("organizer")
        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer and organizer.user == request.user:
                order = Order.objects.get(_id=pk)
                if order:

                    serializer = OrderOrganizerSerializer(
                        order,
                        many=False,
                        context={"user": request.user, "organizer": organizerId},
                    )
                    return Response(serializer.data)
                else:
                    Response(
                        {"detail": "Not authorized to view this order"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
                )

        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def exportNewOrdersExcel(request):
    try:
        organizerId = request.query_params.get("organizer")
        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer and organizer.user == request.user:
                # orders = Order.objects.filter(isPaid=True)
                # organizer = Organizer.objects.get(user=request.user)
                _orderitem = OrderItem.objects.filter(organizer=organizer)

                orders = (
                    Order.objects.filter(orderitem__in=_orderitem, isPaid=True)
                    .order_by("-createdAt")
                    .distinct()
                )

                response = HttpResponse(content_type="text/csv")
                response["Content-Disposition"] = 'attachment; filename="export_.csv"'

                serializer = OrderSerializerExcel(
                    orders,
                    many=True,
                    context={"user": request.user, "organizer": organizerId},
                )
                header = OrderSerializerExcel.Meta.fields
                writer = csv.DictWriter(
                    response,
                    fieldnames=[
                        "Date",
                        "firstName",
                        "lastName",
                        "phone",
                        "email",
                        "items",
                        "totalPrice",
                        "statut",
                    ],
                )
                writer.writeheader()

                for row in serializer.data:
                    typeOrder = "Online"
                    first_name = row["user"]["first_name"]
                    last_name = row["user"]["last_name"]
                    email = row["user"]["email"]
                    phone = row["user"]["phone"]
                    # Check if 'isOffline' key exists in the row before accessing it
                    if row.get("isOffline", False):
                        typeOrder = "Offline"
                        first_name = row["infoClient"]["firstName"]
                        last_name = row["infoClient"]["lastName"]
                        email = row["infoClient"]["email"]
                        phone = row["infoClient"]["phone"]
                    _row = {
                        "Date": row["paidAt"],
                        "firstName": first_name,
                        "lastName": last_name,
                        "phone": phone,
                        "email": email,
                        "items": row["orderItems"],
                        "totalPrice": row["totalPrice"],
                        "statut": typeOrder,
                    }
                    writer.writerow(_row)

                return response
            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
                )

        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewOrders(request):
    try:
        organizerId = request.query_params.get("organizer")
        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer and organizer.user == request.user:
                status = request.query_params.get("status")
                search = request.query_params.get("q", None)

                # organizer = Organizer.objects.get(user=request.user)
                _orderitem = OrderItem.objects.filter(organizer=organizer)
                orders = (
                    Order.objects.filter(orderitem__in=_orderitem, isPaid=True)
                    .order_by("-createdAt")
                    .distinct()
                )

                if status and status != "All":
                    orders = orders.filter(isOffline=status)
                if status == "All":
                    orders = orders

                if search:
                    search_terms = search.split()
                    if len(search_terms) == 1:
                        if is_valid_uuid(search_terms[0]):
                            orders = orders.filter(Q(_id__contains=search_terms[0]))
                        else:
                            orders = orders.filter(
                                Q(user__first_name__icontains=search_terms[0])
                                | Q(user__last_name__icontains=search_terms[0])
                            )

                    elif len(search_terms) == 2:
                        orders = orders.filter(
                            Q(user__first_name__icontains=search_terms[0])
                            | Q(user__last_name__icontains=search_terms[1])
                        )

                page = request.query_params.get("page")
                paginator = Paginator(orders, 20)

                try:
                    orders = paginator.page(page)
                except PageNotAnInteger:
                    orders = paginator.page(1)
                except EmptyPage:
                    orders = paginator.page(paginator.num_pages)

                if page == None:
                    page = 1

                page = int(page)

                serializer = OrderOrganizerSerializer(
                    orders,
                    context={"user": request.user, "organizer": organizerId},
                    many=True,
                )
                return Response(
                    {
                        "orders": serializer.data,
                        "page": page,
                        "pages": paginator.num_pages,
                        "count": paginator.count,
                    }
                )

            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
                )

        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# last


@api_view(["PUT"])
def updateOrderToPaidConfirm(request, pk):
    try:
        print(f"=== CONFIRM ORDER START: {pk} ===")
        order = Order.objects.get(_id=pk)
        print(f"Order found - ID: {order._id}, Total: {order.totalPrice}, isPaid: {order.isPaid}")
        # print(order.email)
        user = order.user
        emailFrom = "AyLink <<EMAIL>>"

        # Handle both logged-in users and guest users
        if user:
            # Logged-in user
            to = [user.email]
            toEmail = user.email
            first_name = user.first_name
            last_name = user.last_name
        else:
            # Guest user
            to = [order.guest_email]
            toEmail = order.guest_email
            # Split guest_name into first and last name
            name_parts = order.guest_name.split(' ', 1)
            first_name = name_parts[0] if name_parts else ""
            last_name = name_parts[1] if len(name_parts) > 1 else ""

        subjectEmail = "Aylink Booking confirmation"
        orderid = order._id

        order_len = Order.objects.filter(isPaid=True).count()

        subject, from_email = subjectEmail, emailFrom

        if order.isPaid:
            print(f"Order {pk} already paid - returning error")
            return Response({"detail": "Order already paid"}, status=400)

        # This function is for confirming free orders (totalPrice = 0)
        # For paid orders, use the payment gateway confirmation
        if order.totalPrice != 0:
            print(f"Order {pk} is paid order (total: {order.totalPrice}) - rejecting confirm request")
            return Response({"detail": "This endpoint is for free orders only. Use payment gateway for paid orders."}, status=400)

        if order.hasDiscount and order.code:
            now = timezone.now()
            data = request.data
            coupon = Coupon.objects.get(
                code__iexact=order.code,
                valid_from__lte=now,
                valid_to__gte=now,
                active=True,
            )
            if coupon:
                if coupon.has_max and coupon.max_count <= coupon.used:
                    return Response(
                        {"detail": "Code Coupon has expired please try again"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                return Response(
                    {"detail": "Code Coupon has expired please try again"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if order.orderitem_set.all() and len(order.orderitem_set.all()) > 0:
            # check er
            for i in order.orderitem_set.all():
                product = Product.objects.get(_id=i.product._id)

                if not product.isActive or product.isDeleted:
                    return Response(
                        {
                            "detail": "NOT ACTIVE : One or many products is no longer availabe !"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                # condition on varia also
                if not order.hasDiscount and not product.hasVariation:
                    if (
                        float(product.price) != float(i.price)
                        or int(i.qty) > product.countInStock
                    ):
                        return Response(
                            {
                                "detail": "One or many items are updated ! Please double update your cart "
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                if not order.hasDiscount and product.hasVariation:
                    variationAtt = get_object_or_404(
                        VariationAttribute, pk=i.variationAttributeId.id
                    )
                    if (
                        float(variationAtt.price) != float(i.price)
                        or int(i.qty) > product.countInStock
                    ):
                        return Response(
                            {
                                "detail": "One or many items are updated ! Please double update your cart "
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                # add this 27-2-25
                if product.organizer.is_daily:
                    tickets = Ticket.objects.filter(
                        Q(orderItem__product=product) & Q(event_date=i.event_date)
                    )

                    if tickets.count() + int(i.qty) > product.countInStock:
                        return Response(
                            {
                                "detail": "One or many items are updated ! Please double update your cart "
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

            _total = 0.0
            for i in order.orderitem_set.all():
                _total += float(i.price) * float(i.qty)

            if float(order.totalPrice) != float(_total):
                return Response({"detail": "Something went wrong "}, status=400)

        order.isPaid = True
        order.paidAt = timezone.now()
        order.save()

        show_mail = False
        for i in order.orderitem_set.all():
            if i.organizer.show_mail:
                show_mail = True
            if i.qty > 1:
                for item in range(1, i.qty + 1):
                    check_isticket_customization = i.isticket_customization
                    nameticket = ""

                    if check_isticket_customization and i.list_ticket_name != "":
                        reconstructed_list = i.list_ticket_name.split("||")

                        if len(reconstructed_list) >= item:
                            nameticket = reconstructed_list[item - 1]
                        else:
                            nameticket = ""

                    t = Ticket.objects.create(
                        orderItem=i,
                        price=i.price,
                        paidAt=i.order.paidAt,
                        organizerPhoto=i.organizer.cover,
                        organizerLogo=i.organizer.avatar,
                        organizerDate=i.organizer.organizer_date,
                        organizerName=i.organizer.name,
                        firstName=first_name,
                        lastName=last_name,
                        orderNo=i.order._id,
                        productName=i.product.name,
                        variationName=i.variationName,
                        variationValue=i.variationValue,
                        customization=i.customization,
                        typeOrder=i.order.isOffline,
                        # add 27-2-25
                        is_daily=i.is_daily,
                        has_date=i.has_date,
                        event_date=i.event_date,
                        product_date=i.product_date,
                        isticket_customization=i.isticket_customization,
                        ticket_name=nameticket,
                    )
            elif i.qty == 1:
                check_isticket_customization = i.isticket_customization
                nameticket = ""

                if check_isticket_customization and i.list_ticket_name != "":
                    reconstructed_list = i.list_ticket_name.split("||")

                    if len(reconstructed_list) >= 1:
                        nameticket = reconstructed_list[0]
                    else:
                        nameticket = ""
                        
                if i.product.name in ["5 for 4 Group Ticket - 3 Day Pass","5 for 4 Group Ticket - Friday 09/05","5 for 4 Group Ticket - Saturday 10/05","5 for 4 Group Ticket - Sunday 11/05"]:
                    for item in range(1, 6):
                        Ticket.objects.create(
                            orderItem=i,
                            price=i.price,
                            paidAt=i.order.paidAt,
                            organizerPhoto=i.organizer.cover,
                            organizerLogo=i.organizer.avatar,
                            organizerDate=i.organizer.organizer_date,
                            organizerName=i.organizer.name,
                            firstName=first_name,
                            lastName=last_name,
                            orderNo=i.order._id,
                            productName=i.product.name,
                            variationName=i.variationName,
                            variationValue=i.variationValue,
                            customization=i.customization,
                            typeOrder=i.order.isOffline,
                            # add 27-2-25
                            is_daily=i.is_daily,
                            has_date=i.has_date,
                            event_date=i.event_date,
                            product_date=i.product_date,
                            isticket_customization=i.isticket_customization,
                            ticket_name=nameticket,
                        )
                else:
                    Ticket.objects.create(
                        orderItem=i,
                        price=i.price,
                        paidAt=i.order.paidAt,
                        organizerPhoto=i.organizer.cover,
                        organizerLogo=i.organizer.avatar,
                        organizerDate=i.organizer.organizer_date,
                        organizerName=i.organizer.name,
                        firstName=first_name,
                        lastName=last_name,
                        orderNo=i.order._id,
                        productName=i.product.name,
                        variationName=i.variationName,
                        variationValue=i.variationValue,
                        customization=i.customization,
                        typeOrder=i.order.isOffline,
                        # add 27-2-25
                        is_daily=i.is_daily,
                        has_date=i.has_date,
                        event_date=i.event_date,
                        product_date=i.product_date,
                        isticket_customization=i.isticket_customization,
                        ticket_name=nameticket,
                    )
            product = Product.objects.get(_id=i.product._id)
            if not product.organizer.is_daily:
                product.countInStock -= int(i.qty)
            product.save()

        if order.hasDiscount and order.code:
            now = timezone.now()
            data = request.data
            coupon = Coupon.objects.get(
                code__iexact=order.code,
                valid_from__lte=now,
                valid_to__gte=now,
                active=True,
            )
            if coupon:
                if coupon.has_max:
                    coupon.max_count = coupon.max_count - 1
                coupon.used = coupon.used + 1
                coupon.save()

        # send email to the user
        try:
            html_content = render_to_string(
                "order_confirmation.html",
                {
                    "order": order,
                    "items": order.orderitem_set.all,
                    "orderid": orderid,
                    "showmail": show_mail,
                    "is_admin_email": False,  # Show download button for customer
                },
            )  # render with dynamic value

            text_content = strip_tags(
                html_content
            )  # Strip the html tag. So people can see the pure text at least.
            msg = EmailMultiAlternatives(subject, text_content, from_email, to)

            msg.attach_alternative(html_content, "text/html")

            sent_count = msg.send()
            print(f"Customer email sent successfully to: {to}")
        except Exception as email_error:
            print(f"Error sending customer email: {email_error}")
            sent_count = 0

        # Only create EmailStatus for logged-in users
        if user:
            EmailStatus.objects.create(
                user=user, sent_count=sent_count, status="ORDER PAID"
            )

        # send email to Aylink (Admin notification)
        try:
            html_content2 = render_to_string(
                "order_confirmation.html",
                {
                    "order": order,
                    "items": order.orderitem_set.all,
                    "orderid": orderid,
                    "is_admin_email": True,  # Hide download button for admin
                },
            )  # render with dynamic value

            text_content2 = strip_tags(
                html_content2
            )  # Strip the html tag. So people can see the pure text at least.

            # Use appropriate email for the subject line
            customer_email = user.email if user else order.guest_email
            msg2 = EmailMultiAlternatives(
                f"🔔 Nouvelle commande #{orderid} de {customer_email}",
                text_content2,
                from_email,
                [
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            )
            msg2.attach_alternative(html_content2, "text/html")
            msg2.send()
            print(f"Admin notification email sent successfully for order: {orderid}")
        except Exception as admin_email_error:
            print(f"Error sending admin notification email: {admin_email_error}")

        # Only save notification for logged-in users
        try:
            if user:
                save_notification(
                    "Confirmation de réservation Aylink",
                    "Nous vous remercions d'avoir choisi AyLink pour acheter vos billets en ligne.",
                    user,
                    orderid,
                    3,
                    None,
                    False,
                )
                print(f"Notification saved for user: {user.email}")
        except Exception as notification_error:
            print(f"Error saving notification: {notification_error}")

        print(f"Order {orderid} confirmed successfully - sending response")

        # Return response immediately
        return Response({"detail": "Order was confirmed"})
    except ObjectDoesNotExist:
        return Response({"detail": "Order was not confirmed ex"}, status=400)
    except Exception as error:
        print(error)
        error_details = traceback.format_exc()
        print("Full Traceback:\n", error_details)
        return Response(
            {
                "detail": "Order was not confirmed",
                "error": str(error),
                "trace": error_details,
            },
            status=400,
        )


@api_view(["POST"])
def addOrderItems(request):
    data = request.data
    orderItems = data["orderItems"]

    # Check if this is a guest checkout
    is_guest = data.get("is_guest", False)
    user = None
    guest_info = {}

    if is_guest:
        # Validate guest information
        guest_info = {
            'guest_name': data.get("guest_name", ""),
            'guest_email': data.get("guest_email", ""),
            'guest_phone': data.get("guest_phone", "")
        }

        # Validate required guest fields
        if not guest_info['guest_name'] or not guest_info['guest_email']:
            return Response(
                {"detail": "Guest name and email are required for guest checkout"},
                status=status.HTTP_400_BAD_REQUEST
            )
    else:
        # For logged-in users, require authentication
        if not request.user.is_authenticated:
            return Response(
                {"detail": "Authentication required for non-guest checkout"},
                status=status.HTTP_401_UNAUTHORIZED
            )
        user = request.user

    # (0.1) Check number of items
    if orderItems and len(orderItems) == 0:
        return Response(
            {"detail": "No Order Items"}, status=status.HTTP_400_BAD_REQUEST
        )

    # (0.2) Check if quantity & price  is OK

    if orderItems and len(orderItems) > 0:
        # check er
        for i in orderItems:
            product = Product.objects.get(_id=i["product"])
            if not product.isActive or product.isDeleted:
                return Response(
                    {"detail": "One or many products is no longer availabe !"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if (
                not product.organizer.is_daily
                and product.organizer.organizerDateFrom < timezone.now()
            ):
                return Response(
                    {
                        "detail": "One or many items are updated ! Please double update your cart"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not product.hasVariation:
                if (
                    float(product.price) != float(i["price"])
                    or int(i["qty"]) > product.countInStock
                ):
                    return Response(
                        {
                            "detail": "One or many items are updated ! Please double update your cart "
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            if product.hasVariation:
                variationAtt = get_object_or_404(
                    VariationAttribute, pk=i["variation_attribute_id"]
                )
                if (
                    float(variationAtt.price) != float(i["price"])
                    or int(i["qty"]) > product.countInStock
                ):
                    return Response(
                        {
                            "detail": "One or many items are updated ! Please double update your cart "
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            if product.organizer.is_daily:
                tickets = Ticket.objects.filter(
                    Q(orderItem__product=product) & Q(event_date=i["event_date"])
                )
                if tickets.count() + int(i["qty"]) > product.countInStock:
                    return Response(
                        {
                            "detail": "One or many items are updated ! Please double update your cart "
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

    # (0.3) Check if the totalPrice is OK

    tf_price = data["totalPrice"]
    tb_price = 0
    for i in orderItems:
        tb_price += float(i["price"]) * int(i["qty"])

    if float(tf_price) != float(tb_price):
        return Response(
            {"detail": "Something went wrong !"}, status=status.HTTP_400_BAD_REQUEST
        )

    # (1) Create order

    # (2) table of tickets inserted

    order_data = {
        'paymentMethod': data["paymentMethod"],
        'taxPrice': data["taxPrice"],
        'totalPrice': data["totalPrice"],
    }

    if is_guest:
        # For guest orders, set user to None and add guest info
        order_data.update({
            'user': None,
            'guest_name': guest_info['guest_name'],
            'guest_email': guest_info['guest_email'],
            'guest_phone': guest_info['guest_phone'],
            'as_guest': True
        })
    else:
        # For logged-in users
        order_data.update({
            'user': user,
            'as_guest': False
        })

    order = Order.objects.create(**order_data)

    # (3) Create order items and set order to orderItem relationship

    for i in orderItems:
        product = Product.objects.get(_id=i["product"])
        if not product.isActive or product.isDeleted:
            return Response(
                {"detail": "One or many products is no longer availabe !"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        eventdt = None
        productDt = None
        if str(i["is_daily"]).lower() in ["true", "1", "yes", "y", "on"]:
            if product.has_date and product.event_date:
                eventdt = product.event_date
            else:
                eventdt = i["event_date"]
        else:
            if product.has_date and product.product_date:
                productDt = product.product_date
                
        

        # list_ticket_name = i["list_ticket_name"]

        # list_ticket_name_str = "||".join(list_ticket_name)
        list_ticket_name = i.get("list_ticket_name", [])
        try:
            # Step 1: Remove the brackets and strip whitespace
            cleaned = list_ticket_name.strip("[]").strip()

            # Step 2: Split by comma and strip each element
            list_ticket_name = [item.strip() for item in cleaned.split(",")]
        except:
            list_ticket_name = list_ticket_name

        list_ticket_name_str = (
            "||".join(list_ticket_name) if isinstance(list_ticket_name, list) else ""
        )

        isticket_customization = str(
            i.get("isticket_customization", False)
        ).lower() in ["true", "1", "yes", "y", "on"]

        # print("list_ticket_name str : " + list_ticket_name_str)
        # reconstructed_list = list_ticket_name_str.split("||")
        # print("list_ticket_name list : " + str(reconstructed_list))

        item = OrderItem.objects.create(
            product=product,
            order=order,
            name=product.name,
            organizer=product.organizer,
            qty=i["qty"],
            price=i["price"],
            image=i["image"],
            customization=i["customization"],
            variationName=i["variation_name"],
            variationValue=i["variation_value"],
            variationAttributeId_id=i["variation_attribute_id"],
            is_daily=str(i["is_daily"]).lower() in ["true", "1", "yes", "y", "on"],
            has_date=str(i["has_date"]).lower() in ["true", "1", "yes", "y", "on"],
            isticket_customization=isticket_customization,
            list_ticket_name=list_ticket_name_str,
            event_date=eventdt,
            product_date=productDt,
        )

    # send email / order processing
    # emailFrom = "<EMAIL>"
    # to = [user.email]
    # subjectEmail="Commande en attante de paiement"
    # order.id= order._id
    # subject, from_email = subjectEmail, emailFrom
    # html_content = render_to_string('order_processing.html', {'order':order,"items":order.orderitem_set.all}) # render with dynamic value
    # text_content = strip_tags(html_content) # Strip the html tag. So people can see the pure text at least.
    # msg = EmailMultiAlternatives(subject, text_content, from_email, to)
    # msg.attach_alternative(html_content, "text/html")
    # msg.send()

    serializer = OrderSerializer(order, many=False)
    return Response(serializer.data)


@api_view(["POST"])
@permission_classes([IsOrganizer])
def addOrderOfflineItems(request):

    emailFrom = "AyLink <<EMAIL>>"
    data = request.data
    orderItems = data["orderItems"]
    infoClient = data["infoClient"]

    if orderItems and len(orderItems) == 0:
        return Response(
            {"detail": "No Order Items"}, status=status.HTTP_400_BAD_REQUEST
        )
    else:

        # (0) Get Order Organizer data['organizer']
        # (1) Create order

        order = Order.objects.create(
            user=request.user,
            paymentMethod=data["paymentMethod"],
            isOffline=True,
            isPaid=True,
            paidAt=timezone.now(),
            isFree=data["isFree"],
            taxPrice=data["taxPrice"],
            totalPrice=data["totalPrice"],
        )

        info = InfoClient.objects.create(
            order=order,
            firstName=infoClient["firstName"],
            lastName=infoClient["lastName"],
            phone=infoClient["phone"],
            email=infoClient["email"],
        )
        if order.hasDiscount and order.totalPrice == (
            order.totalPrice - (order.totalPrice * order.disount)
        ):
            return Response("Something went wrong ")
        # (3) Create order items adn set order to orderItem relationship
        for i in orderItems:
            
            product = Product.objects.get(_id=i["product"])
            if not product.isActive or product.isDeleted or product.countInStock <= 0:
                return Response(
                    {"detail": "One or many products is no longer availabe !"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if (
                not product.organizer.is_daily
                and product.organizer.organizerDateFrom < timezone.now()
            ):
                return Response(
                    {"detail": "One or many items are updated ! "},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # if not product.hasVariation :
            #     if float(product.price) != float( i["price"]) or int(i["qty"]) > product.countInStock :
            #         return Response({'detail': 'One or many items are updated ! Please double update your cart '}, status=status.HTTP_400_BAD_REQUEST)

            # if product.hasVariation :
            #     variationAtt  = get_object_or_404(VariationAttribute,pk=i["variation_attribute_id"])
            #     if float(variationAtt.price) != float( i["price"]) or int(i["qty"]) > product.countInStock :
            #         return Response({'detail': 'One or many items are updated ! Please double update your cart '}, status=status.HTTP_400_BAD_REQUEST)
            eventdt = None
            if str(i["is_daily"]).lower() in ["true", "1", "yes", "y", "on"]:
                eventdt = i["event_date"]

            print(product)
            item = OrderItem.objects.create(
                product=product,
                order=order,
                name=product.name,
                organizer=product.organizer,
                qty=i["qty"],
                price=i["price"],
                image=i["image"],
                # comment=i['comment'],
                customization=i["customization"],
                variationName=i["variation_name"],
                variationValue=i["variation_value"],
                variationAttributeId_id=i["variation_attribute_id"],
                is_daily=str(i["is_daily"]).lower() in ["true", "1", "yes", "y", "on"],
                has_date=str(i["has_date"]).lower() in ["true", "1", "yes", "y", "on"],
                event_date=eventdt,
                product_date=product.product_date,
            )

            if int(item.qty) > 1:
                for i_ in range(1, int(item.qty) + 1):
                    t = Ticket.objects.create(
                        orderItem=item,
                        price=item.price,
                        paidAt=item.order.paidAt,
                        organizerPhoto=item.organizer.cover,
                        organizerLogo=item.organizer.avatar,
                        organizerDate=item.organizer.organizer_date,
                        organizerName=item.organizer.name,
                        firstName=info.firstName,
                        lastName=info.lastName,
                        orderNo=item.order._id,
                        productName=item.product.name,
                        variationName=item.variationName,
                        variationValue=item.variationValue,
                        customization=item.customization,
                        typeOrder=item.order.isOffline,
                        is_daily=item.is_daily,
                        has_date=item.has_date,
                        event_date=item.event_date,
                        product_date=item.product_date,
                    )
            elif int(item.qty) == 1:
                # if item.product.name in ["5 for 4 Group Ticket - 3 Day Pass","5 for 4 Group Ticket - Friday 09/05","5 for 4 Group Ticket - Saturday 10/05","5 for 4 Group Ticket - Sunday 11/05"]:
                #     for item in range(1, 6):
                #         Ticket.objects.create(
                #             orderItem=item,
                #             price=item.price,
                #             paidAt=item.order.paidAt,
                #             organizerPhoto=item.organizer.cover,
                #             organizerLogo=item.organizer.avatar,
                #             organizerDate=item.organizer.organizer_date,
                #             organizerName=item.organizer.name,
                #             firstName=info.firstName,
                #             lastName=info.lastName,
                #             orderNo=item.order._id,
                #             productName=item.product.name,
                #             variationName=item.variationName,
                #             variationValue=item.variationValue,
                #             customization=item.customization,
                #             typeOrder=item.order.isOffline,
                #             is_daily=item.is_daily,
                #             has_date=item.has_date,
                #             event_date=item.event_date,
                #             # product_date=item.product_date,
                #         )
                # else:
                Ticket.objects.create(
                    orderItem=item,
                    price=item.price,
                    paidAt=item.order.paidAt,
                    organizerPhoto=item.organizer.cover,
                    organizerLogo=item.organizer.avatar,
                    organizerDate=item.organizer.organizer_date,
                    organizerName=item.organizer.name,
                    firstName=info.firstName,
                    lastName=info.lastName,
                    orderNo=item.order._id,
                    productName=item.product.name,
                    variationName=item.variationName,
                    variationValue=item.variationValue,
                    customization=item.customization,
                    typeOrder=item.order.isOffline,
                    is_daily=item.is_daily,
                    has_date=item.has_date,
                    event_date=item.event_date,
                    product_date=item.product_date,
                )
            product = Product.objects.get(_id=item.product._id)
            # if not product.organizer.is_daily or (
            #     product.organizer.is_daily and product.has_date
            # ):
            if not product.organizer.is_daily:
                product.countInStock -= int(item.qty)
            product.save()

        # send email

        order.id = order._id
        orderid = order._id
        html_content = render_to_string(
            "order_confirmation_offline.html",
            {"order": order, "items": order.orderitem_set.all, "orderid": orderid},
        )  # render with dynamic value
        text_content = strip_tags(
            html_content
        )  # Strip the html tag. So people can see the pure text at least.
        msg = EmailMultiAlternatives(
            "Aylink Booking confirmation", text_content, emailFrom, [info.email]
        )
        msg.attach_alternative(html_content, "text/html")
        msg.send()

        html_content2 = render_to_string(
            "order_confirmation_offline.html",
            {"order": order, "items": order.orderitem_set.all, "orderid": orderid},
        )  # render with dynamic value
        text_content2 = strip_tags(
            html_content2
        )  # Strip the html tag. So people can see the pure text at least.
        msg2 = EmailMultiAlternatives(
            "Aylink Payment - New Offline Order ",
            text_content2,
            emailFrom,
            ["<EMAIL>",  "<EMAIL>"],
        )
        msg2.attach_alternative(html_content, "text/html")
        msg2.send()

        serializer = OrderSerializer(order, many=False)
        return Response(serializer.data)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def getMyOrders(request):
    status = request.query_params.get("status")
    user = request.user
    # if user != order.user:
    #     return Response({"detail": "Not authorized"}, status=401)
    orders = user.order_set.filter(isPaid=True).order_by("-createdAt")
    if status and status == "False":
        orders = orders.filter(isOffline=False)
    serializer = OrderSerializer(orders, many=True)
    return Response(serializer.data)


## get organizer orders


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getOrders(request):
    status = request.query_params.get("status")
    search = request.query_params.get("q", None)

    organizer = Organizer.objects.get(user=request.user)
    _orderitem = OrderItem.objects.filter(organizer=organizer)
    orders = (
        Order.objects.filter(orderitem__in=_orderitem, isPaid=True)
        .order_by("-createdAt")
        .distinct()
    )

    if status and status != "All":
        orders = orders.filter(isOffline=status)
    if status == "All":
        orders = orders

    if search:
        search_terms = search.split()
        if len(search_terms) == 1:
            if is_valid_uuid(search_terms[0]):
                orders = orders.filter(Q(_id__contains=search_terms[0]))
            else:
                orders = orders.filter(
                    Q(user__first_name__icontains=search_terms[0])
                    | Q(user__last_name__icontains=search_terms[0])
                )

        elif len(search_terms) == 2:
            orders = orders.filter(
                Q(user__first_name__icontains=search_terms[0])
                | Q(user__last_name__icontains=search_terms[1])
            )

    page = request.query_params.get("page")
    paginator = Paginator(orders, 20)

    try:
        orders = paginator.page(page)
    except PageNotAnInteger:
        orders = paginator.page(1)
    except EmptyPage:
        orders = paginator.page(paginator.num_pages)

    if page == None:
        page = 1

    page = int(page)

    serializer = OrderOrganizerSerializer(
        orders, context={"user": request.user}, many=True
    )
    return Response(
        {
            "orders": serializer.data,
            "page": page,
            "pages": paginator.num_pages,
            "count": paginator.count,
        }
    )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getOrganizerOrderById(request, pk):
    try:
        order = Order.objects.get(_id=pk)
        if order:

            serializer = OrderOrganizerSerializer(
                order, many=False, context={"user": request.user}
            )
            return Response(serializer.data)
        else:
            Response(
                {"detail": "Not authorized to view this order"},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except:
        return Response(
            {"detail": "Order does not exist"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
def getOrderById(request, pk):
    try:
        order = Order.objects.get(_id=pk)
        if order:
            # Check if user is authenticated and owns the order, or if it's a guest order
            if request.user.is_authenticated:
                # For authenticated users, check if they own the order
                if order.user == request.user:
                    serializer = OrderSerializer(order, many=False)
                    return Response(serializer.data)
                else:
                    return Response(
                        {"detail": "Not authorized to view this order"},
                        status=status.HTTP_403_FORBIDDEN,
                    )
            else:
                # For unauthenticated users, only allow access to guest orders
                if order.as_guest:
                    serializer = OrderSerializer(order, many=False)
                    return Response(serializer.data)
                else:
                    return Response(
                        {"detail": "Authentication required to view this order"},
                        status=status.HTTP_401_UNAUTHORIZED,
                    )
        else:
            return Response(
                {"detail": "Order not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
    except Order.DoesNotExist:
        return Response(
            {"detail": "Order does not exist"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"detail": f"An error occurred: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(["GET"])
def checkMailOrder(request, pk):
    order = Order.objects.get(_id=pk)
    if order:
        try:
            user = order.user
            emailFrom = "AyLink <<EMAIL>>"
            to = ["<EMAIL>"]
            to_off = ["<EMAIL>"]

            messagemail = ""

            subjectEmail = "Aylink Booking confirmation"
            orderid = order._id

            subject, from_email = subjectEmail, emailFrom

            html_content = render_to_string(
                "order_confirmation.html",
                {"order": order, "items": order.orderitem_set.all, "orderid": orderid, "showmail": True},
            )  # render with dynamic value

            text_content = strip_tags(
                html_content
            )  # Strip the html tag. So people can see the pure text at least.
            msg = EmailMultiAlternatives(subject, text_content, from_email, to)

            msg.attach_alternative(html_content, "text/html")

            sent_count = msg.send()
            if sent_count:
                messagemail += "message 1 send "
            else:
                messagemail += "message 1 not send "

            html_content = render_to_string(
                "order_confirmation_offline.html",
                {"order": order, "items": order.orderitem_set.all, "orderid": orderid},
            )  # render with dynamic value

            text_content = strip_tags(
                html_content
            )  # Strip the html tag. So people can see the pure text at least.
            msg = EmailMultiAlternatives(subject, text_content, from_email, to_off)

            msg.attach_alternative(html_content, "text/html")

            sent_count = msg.send()
            if sent_count:
                messagemail += "message 2 send "
            else:
                messagemail += "message 2 not send "

            import logging

            logging.basicConfig(level=logging.DEBUG)
            return Response(
                {
                    "detail": orderid,
                    "messagemail": messagemail,
                    "subject": msg.subject,
                    "from_email": msg.from_email,
                    "to": msg.to,
                    "body": msg.body,
                    "send": sent_count,
                },
            )
        except Exception as e:
            return Response(
                {"detail": f"An error occurred while sending the email: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
    else:
        return Response(
            {"detail": "Order not exist"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["PUT"])
# @permission_classes([IsAuthenticated])
def updateOrderToPaid(request, pk):
    order = Order.objects.get(_id=pk)
    # print(order.email)
    user = order.user
    emailFrom = "AyLink <<EMAIL>>"

    # Handle both logged-in users and guest users
    if user:
        # Logged-in user
        to = [user.email]
        toEmail = user.email
        first_name = user.first_name
        last_name = user.last_name
    else:
        # Guest user
        to = [order.guest_email]
        toEmail = order.guest_email
        # Split guest_name into first and last name
        name_parts = order.guest_name.split(' ', 1)
        first_name = name_parts[0] if name_parts else ""
        last_name = name_parts[1] if len(name_parts) > 1 else ""

    subjectEmail = "Aylink Booking confirmation"
    orderid = order._id

    order_len = Order.objects.filter(isPaid=True).count()

    subject, from_email = subjectEmail, emailFrom

    if order.isPaid:
        return Response("Order already paid")

    if order.hasDiscount and order.code:
        now = timezone.now()
        data = request.data
        coupon = Coupon.objects.get(
            code__iexact=order.code, valid_from__lte=now, valid_to__gte=now, active=True
        )
        if coupon:
            if coupon.has_max and coupon.max_count <= coupon.used:
                return Response(
                    {"detail": "Code Coupon has expired please try again"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"detail": "Code Coupon has expired please try again"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    if order.orderitem_set.all() and len(order.orderitem_set.all()) > 0:
        # check er
        for i in order.orderitem_set.all():
            product = Product.objects.get(_id=i.product._id)

            if not product.isActive or product.isDeleted:
                return Response(
                    {
                        "detail": "NOT ACTIVE : One or many products is no longer availabe !"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # condition on varia also
            if not order.hasDiscount and not product.hasVariation:
                if (
                    float(product.price) != float(i.price)
                    or int(i.qty) > product.countInStock
                ):
                    return Response(
                        {
                            "detail": "One or many items are updated ! Please double update your cart "
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            if not order.hasDiscount and product.hasVariation:
                variationAtt = get_object_or_404(
                    VariationAttribute, pk=i.variationAttributeId.id
                )
                if (
                    float(variationAtt.price) != float(i.price)
                    or int(i.qty) > product.countInStock
                ):
                    return Response(
                        {
                            "detail": "One or many items are updated ! Please double update your cart "
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # add this 27-2-25
            if product.organizer.is_daily:
                tickets = Ticket.objects.filter(
                    Q(orderItem__product=product) & Q(event_date=i.event_date)
                )

                if tickets.count() + int(i.qty) > product.countInStock:
                    return Response(
                        {
                            "detail": "One or many items are updated ! Please double update your cart "
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

        _total = 0.0
        for i in order.orderitem_set.all():
            _total += float(i.price) * float(i.qty)
        print(order.totalPrice)
        print(_total)

        if float(order.totalPrice) != float(_total):
            return Response("Something went wrong ")

    if "cmi_update_time" in request.data:
        order.cmiUpdatedTime = request.data["cmi_update_time"]
    if "cmi_email_address" in request.data:
        order.cmiEmail = request.data["cmi_email_address"]
    if "cmi_tans_id" in request.data:
        order.cmiTransId = request.data["cmi_tans_id"]
    if "cmi_status" in request.data:
        order.cmiStatus = request.data["cmi_status"]

    # you must change the organizer ID
    # if len(order.orderitem_set.filter(organizer___id = 6)) > 0 or  len(order.orderitem_set.filter(organizer___id = 7)) > 0:
    #     # the order is for travellink
    #     order.isPaid = True
    #     order.paidAt = datetime.now()
    #     order.save()

    #     for i in order.orderitem_set.all():
    #         product = Product.objects.get(_id=i.product._id)
    #         product.countInStock -= int(i.qty)
    #         product.save()

    #     html_content2 = render_to_string('order_confirmation.html', {'order':order,"items":order.orderitem_set.all}) # render with dynamic value
    #     text_content2 = strip_tags(html_content2) # Strip the html tag. So people can see the pure text at least.
    #     msg2 = EmailMultiAlternatives("Aylink Payment - New Order from " + str(user.email), text_content2, from_email, ["<EMAIL>"])
    #     msg2.attach_alternative(html_content2, "text/html")
    #     msg2.send()

    #     return Response({"detail":"Order was paid"})

    # and request.data.cmi_r_hash == request.data.cmi_a_hash

    if (
        "cmi_update_time" in request.data
        and "cmi_email_address" in request.data
        and "cmi_tans_id" in request.data
        and "cmi_status" in request.data
        and "cmi_r_hash" in request.data
        and "cmi_a_hash" in request.data
        and request.data["cmi_r_hash"] == request.data["cmi_a_hash"]
    ):
        order.isPaid = True
        order.paidAt = timezone.now()
        order.save()

        show_mail = False

        for i in order.orderitem_set.all():
            if i.organizer.show_mail:
                show_mail = True
            if i.qty > 1:
                for item in range(1, i.qty + 1):
                    check_isticket_customization = i.isticket_customization
                    nameticket = ""

                    if check_isticket_customization and i.list_ticket_name != "":
                        reconstructed_list = i.list_ticket_name.split("||")

                        if len(reconstructed_list) >= item:
                            nameticket = reconstructed_list[item - 1]
                        else:
                            nameticket = ""
                    t = Ticket.objects.create(
                        orderItem=i,
                        price=i.price,
                        paidAt=i.order.paidAt,
                        organizerPhoto=i.organizer.cover,
                        organizerLogo=i.organizer.avatar,
                        organizerDate=i.organizer.organizer_date,
                        organizerName=i.organizer.name,
                        firstName=first_name,
                        lastName=last_name,
                        orderNo=i.order._id,
                        productName=i.product.name,
                        variationName=i.variationName,
                        variationValue=i.variationValue,
                        customization=i.customization,
                        typeOrder=i.order.isOffline,
                        # add 27-2-25
                        is_daily=i.is_daily,
                        has_date=i.has_date,
                        event_date=i.event_date,
                        product_date=i.product_date,
                        isticket_customization=i.isticket_customization,
                        ticket_name=nameticket,
                    )
            elif i.qty == 1:
                check_isticket_customization = i.isticket_customization
                nameticket = ""

                if check_isticket_customization and i.list_ticket_name != "":
                    reconstructed_list = i.list_ticket_name.split("||")

                    if len(reconstructed_list) >= 1:
                        nameticket = reconstructed_list[0]
                    else:
                        nameticket = ""
                # "5 for 4 Group Ticket - 3 Day Pass","5 for 4 Group Ticket - Friday 09/05","5 for 4 Group Ticket - Saturday 10/05","5 for 4 Group Ticket - Sunday 11/05"
                if i.product.name in ["5 for 4 Group Ticket - 3 Day Pass","5 for 4 Group Ticket - Friday 09/05","5 for 4 Group Ticket - Saturday 10/05","5 for 4 Group Ticket - Sunday 11/05"]:
                    for item in range(1, 6):
                        Ticket.objects.create(
                            orderItem=i,
                            price=i.price,
                            paidAt=i.order.paidAt,
                            organizerPhoto=i.organizer.cover,
                            organizerLogo=i.organizer.avatar,
                            organizerDate=i.organizer.organizer_date,
                            organizerName=i.organizer.name,
                            firstName=first_name,
                            lastName=last_name,
                            orderNo=i.order._id,
                            productName=i.product.name,
                            variationName=i.variationName,
                            variationValue=i.variationValue,
                            customization=i.customization,
                            typeOrder=i.order.isOffline,
                            # add 27-2-25
                            is_daily=i.is_daily,
                            has_date=i.has_date,
                            event_date=i.event_date,
                            product_date=i.product_date,
                            isticket_customization=i.isticket_customization,
                            ticket_name=nameticket,
                        )
                else:
                    Ticket.objects.create(
                        orderItem=i,
                        price=i.price,
                        paidAt=i.order.paidAt,
                        organizerPhoto=i.organizer.cover,
                        organizerLogo=i.organizer.avatar,
                        organizerDate=i.organizer.organizer_date,
                        organizerName=i.organizer.name,
                        firstName=first_name,
                        lastName=last_name,
                        orderNo=i.order._id,
                        productName=i.product.name,
                        variationName=i.variationName,
                        variationValue=i.variationValue,
                        customization=i.customization,
                        typeOrder=i.order.isOffline,
                        # add 27-2-25
                        is_daily=i.is_daily,
                        has_date=i.has_date,
                        event_date=i.event_date,
                        product_date=i.product_date,
                        isticket_customization=i.isticket_customization,
                        ticket_name=nameticket,
                    )
            product = Product.objects.get(_id=i.product._id)
            if not product.organizer.is_daily:
                product.countInStock -= int(i.qty)
            product.save()

        if order.hasDiscount and order.code:
            now = timezone.now()
            data = request.data
            coupon = Coupon.objects.get(
                code__iexact=order.code,
                valid_from__lte=now,
                valid_to__gte=now,
                active=True,
            )
            if coupon:
                if coupon.has_max:
                    coupon.max_count = coupon.max_count - 1
                coupon.used = coupon.used + 1
                coupon.save()

        # send email to the user
        html_content = render_to_string(
            "order_confirmation.html",
            {
                "order": order,
                "items": order.orderitem_set.all,
                "orderid": orderid,
                "showmail": show_mail,
                "is_admin_email": False,  # Show download button for customer
            },
        )  # render with dynamic value
        text_content = strip_tags(
            html_content
        )  # Strip the html tag. So people can see the pure text at least.
        msg = EmailMultiAlternatives(subject, text_content, from_email, to)

        msg.attach_alternative(html_content, "text/html")

        sent_count = msg.send()

        # Only create EmailStatus for logged-in users
        if user:
            EmailStatus.objects.create(
                user=user, sent_count=sent_count, status="ORDER PAID"
            )

        # send email to Aylink (Admin notification)

        html_content2 = render_to_string(
            "order_confirmation.html",
            {
                "order": order,
                "items": order.orderitem_set.all,
                "orderid": orderid,
                "is_admin_email": True,  # Hide download button for admin
            },
        )  # render with dynamic value
        text_content2 = strip_tags(
            html_content2
        )  # Strip the html tag. So people can see the pure text at least.
        # Use appropriate email for the subject line
        customer_email = user.email if user else order.guest_email
        msg2 = EmailMultiAlternatives(
            f"🔔 Nouvelle commande #{orderid} de {customer_email}",
            text_content2,
            from_email,
            [
                "<EMAIL>",
                "<EMAIL>",
            ],
        )
        msg2.attach_alternative(html_content2, "text/html")
        msg2.send()

        # Only save notification for logged-in users
        if user:
            save_notification(
                "Confirmation de réservation Aylink",
                "Nous vous remercions d'avoir choisi AyLink pour acheter vos billets en ligne.",
                user,
                orderid,
                3,
                None,
                False,
            )

        return Response({"detail": "Order was paid"})

    return Response({"detail": "Order was not paid"}, status=400)


@api_view(["PUT"])
@permission_classes([IsAdminUser])
def generateOrderTickets(request, pk):
    order = Order.objects.get(_id=pk)
    # print(order.email)
    user = order.user
    emailFrom = "AyLink <<EMAIL>>"

    # Handle both logged-in users and guest users
    if user:
        # Logged-in user
        to = [user.email]
        first_name = user.first_name
        last_name = user.last_name
    else:
        # Guest user
        to = [order.guest_email]
        # Split guest_name into first and last name
        name_parts = order.guest_name.split(' ', 1)
        first_name = name_parts[0] if name_parts else ""
        last_name = name_parts[1] if len(name_parts) > 1 else ""

    subjectEmail = "Aylink Booking confirmation"
    order.id = order._id
    orderid = order._id

    order_len = Order.objects.filter(isPaid=True).count()

    subject, from_email = subjectEmail, emailFrom

    if order.orderitem_set.all() and len(order.orderitem_set.all()) > 0:
        # check er
        for i in order.orderitem_set.all():

            product = Product.objects.get(_id=i.product._id)
            # condition on varia also
            if (
                float(product.price) != float(i.price)
                or int(i.qty) > product.countInStock
            ):
                print("pass")
                # return Response({'detail': 'One or many items are updated ! Please double update your cart '}, status=status.HTTP_400_BAD_REQUEST)

    if order.isPaid == True:
        for i in order.orderitem_set.all():
            if i.qty > 1:
                for item in range(1, i.qty + 1):
                    t = Ticket.objects.create(
                        orderItem=i,
                        price=i.price,
                        paidAt=i.order.paidAt,
                        organizerPhoto=i.organizer.avatar,
                        organizerDate=i.organizer.organizer_date,
                        organizerName=i.organizer.name,
                        firstName=first_name,
                        lastName=last_name,
                        orderNo=i.order._id,
                        productName=i.product.name,
                        variationName=i.variationName,
                        variationValue=i.variationValue,
                        customization=i.customization,
                        typeOrder=i.order.isOffline,
                    )
            elif i.qty == 1:
                Ticket.objects.create(
                    orderItem=i,
                    price=i.price,
                    paidAt=i.order.paidAt,
                    organizerPhoto=i.organizer.avatar,
                    organizerDate=i.organizer.organizer_date,
                    organizerName=i.organizer.name,
                    firstName=first_name,
                    lastName=last_name,
                    orderNo=i.order._id,
                    productName=i.product.name,
                    variationName=i.variationName,
                    variationValue=i.variationValue,
                    customization=i.customization,
                    typeOrder=i.order.isOffline,
                )
            product = Product.objects.get(_id=i.product._id)
            product.countInStock -= int(i.qty)
            product.save()

        _tickets = Ticket.objects.filter(orderItem__in=order.orderitem_set.all())
        # send email to the user
        print(_tickets)

        # send email to the user
        html_content = render_to_string(
            "order_confirmation.html",
            {
                "order": order,
                "items": order.orderitem_set.all,
                "orderid": orderid,
                "showmail": True,  # Show download button for guest users
                "is_admin_email": False,  # Show download button for customer
            },
        )  # render with dynamic value
        text_content = strip_tags(
            html_content
        )  # Strip the html tag. So people can see the pure text at least.
        msg = EmailMultiAlternatives(subject, text_content, from_email, to)

        msg.attach_alternative(html_content, "text/html")

        sent_count = msg.send()

        # Only create EmailStatus for logged-in users
        if user:
            EmailStatus.objects.create(
                user=user, sent_count=sent_count, status="ORDER PAID"
            )

        # send email to Aylink (Admin notification)

        html_content2 = render_to_string(
            "order_confirmation.html",
            {
                "order": order,
                "items": order.orderitem_set.all,
                "orderid": orderid,
                "is_admin_email": True,  # Hide download button for admin
            },
        )  # render with dynamic value
        text_content2 = strip_tags(
            html_content2
        )  # Strip the html tag. So people can see the pure text at least.
        # Use appropriate email for the subject line
        customer_email = user.email if user else order.guest_email
        msg2 = EmailMultiAlternatives(
            f"Nouvelle commande #{orderid} de {customer_email}",
            text_content2,
            from_email,
            ["<EMAIL>", "<EMAIL>"],
        )
        msg2.attach_alternative(html_content2, "text/html")
        msg2.send()

        return Response({"detail": "Tickets generated !"})

    return Response({"detail": "Error"})


@api_view(["GET"])
@permission_classes([IsOrganizer])
def exportOrdersExcel(request):

    # orders = Order.objects.filter(isPaid=True)
    organizer = Organizer.objects.get(user=request.user)
    _orderitem = OrderItem.objects.filter(organizer=organizer)

    orders = (
        Order.objects.filter(orderitem__in=_orderitem, isPaid=True)
        .order_by("-createdAt")
        .distinct()
    )

    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = 'attachment; filename="export_.csv"'

    serializer = OrderSerializerExcel(
        orders,
        many=True,
        context={"user": request.user},
    )
    header = OrderSerializerExcel.Meta.fields
    writer = csv.DictWriter(
        response,
        fieldnames=[
            "Date",
            "firstName",
            "lastName",
            "phone",
            "email",
            "items",
            "totalPrice",
            "statut",
        ],
    )
    writer.writeheader()

    for row in serializer.data:
        typeOrder = "Online"
        first_name = row["user"]["first_name"]
        last_name = row["user"]["last_name"]
        email = row["user"]["email"]
        phone = row["user"]["phone"]
        # Check if 'isOffline' key exists in the row before accessing it
        if row.get("isOffline", False):
            typeOrder = "Offline"
            first_name = row["infoClient"]["firstName"]
            last_name = row["infoClient"]["lastName"]
            email = row["infoClient"]["email"]
            phone = row["infoClient"]["phone"]
        _row = {
            "Date": row["paidAt"],
            "firstName": first_name,
            "lastName": last_name,
            "phone": phone,
            "email": email,
            "items": row["orderItems"],
            "totalPrice": row["totalPrice"],
            "statut": typeOrder,
        }
        writer.writerow(_row)

    return response

    # pdf = render_to_pdf('pdf/invoice.html', data)

    # return HttpResponse(pdf, content_type='application/pdf')


# get only if he is connected :
@api_view(["GET"])
def exportOrderTicketPdf(request, pk):
    try:
        print(f"=== EXPORT TICKET PDF START: {pk} ===")

        order = Order.objects.get(_id=pk)
        print(f"Order found: {order._id}")

        _tickets = Ticket.objects.filter(orderItem__in=order.orderitem_set.all())
        print(f"Tickets found: {_tickets.count()}")

        showmail = False
        show_mail = request.query_params.get("show")
        if show_mail and show_mail == "show":
            showmail = True
        print(f"Show mail: {showmail}")

        data = {
            "order": order,
            "items": order.orderitem_set.all,
            "tickets": _tickets,
            "showmail": showmail,
        }

        print("Rendering PDF...")
        pdf = render_to_pdf("pdf/tickets.html", data)

        if pdf:
            print("PDF generated successfully")
            return HttpResponse(pdf, content_type="application/pdf")
        else:
            print("PDF generation failed - render_to_pdf returned None")
            return HttpResponse("Error generating PDF", status=500)

    except Order.DoesNotExist:
        print(f"Order not found: {pk}")
        return HttpResponse("Order not found", status=404)
    except Exception as e:
        print(f"Error exporting ticket PDF: {e}")
        import traceback
        traceback.print_exc()
        return HttpResponse(f"Error generating ticket: {str(e)}", status=500)


# @api_view(['GET'])
# def exportTicketPdf(request,pk):

#     factory = qrcode.image.svg.SvgImage
#     qr_image = qrcode.make("test010",image_factory=factory, box_size=20)
#     bufstore = io.BytesIO()
#     qr_image.save(bufstore)
#     print(bufstore.getvalue().decode())

#     data = {
#         "ticketNo":"TESTOO22",
#         "firstname":"DDFF",
#         "lastname":"Boussakssou",
#         "organizer":"Lost Nomads",
#         "svg":bufstore.getvalue().decode()

#     }
#     pdf = render_to_pdf('pdf/ticket.html', data)

#     return HttpResponse(pdf, content_type='application/pdf')


@api_view(["DELETE"])
@permission_classes([IsOrganizer])
def deleteOrder(request, pk):
    order = Order.objects.get(_id=pk)
    serializer = OrderSerializer(order, many=False)

    return Response(serializer.data)


@api_view(["POST"])
# @permission_classes([IsAdminUser])  # Commented out for easy testing
def testAdminEmail(request, pk):
    """
    Test function to send admin notification email
    Usage: POST /api/orders/{order_id}/test-admin-email/
    """
    try:
        # Get the order
        order = Order.objects.get(_id=pk)
        user = order.user

        # Email configuration
        emailFrom = "AyLink <<EMAIL>>"
        orderid = order._id

        # Determine customer email for subject line
        customer_email = user.email if user else order.guest_email

        # Render the admin notification template
        html_content = render_to_string(
            "order_confirmation.html",
            {
                "order": order,
                "items": order.orderitem_set.all,
                "orderid": orderid,
                "is_admin_email": True,  # Hide download button for admin
            },
        )

        # Create text version
        text_content = strip_tags(html_content)

        # Send email to test address
        test_email = "<EMAIL>"
        subject = f" TEST - Nouvelle commande #{orderid} de {customer_email}"

        msg = EmailMultiAlternatives(
            subject,
            text_content,
            emailFrom,
            [test_email],
        )
        msg.attach_alternative(html_content, "text/html")

        sent_count = msg.send()

        if sent_count:
            return Response({
                "detail": "Test admin email sent successfully!",
                "order_id": orderid,
                "customer_email": customer_email,
                "test_email": test_email,
                "subject": subject,
                "order_type": "Guest Order" if order.as_guest else "User Order",
                "total_price": str(order.totalPrice),
                "items_count": order.orderitem_set.count(),
                "sent": True
            }, status=200)
        else:
            return Response({
                "detail": "Failed to send test admin email",
                "order_id": orderid,
                "sent": False
            }, status=400)

    except Order.DoesNotExist:
        return Response({
            "detail": f"Order with ID {pk} not found"
        }, status=404)

    except Exception as e:
        return Response({
            "detail": f"Error sending test admin email: {str(e)}",
            "order_id": pk,
            "sent": False
        }, status=500)
