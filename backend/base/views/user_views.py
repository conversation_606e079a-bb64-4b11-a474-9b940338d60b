from django.shortcuts import render
from django.db import transaction
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from rest_framework.exceptions import AuthenticationFailed, ValidationError
from django.contrib.auth import authenticate

from base.models import User, EmailStatus

from base.serializers import ProductSerializer, UserSerializer

# Create your views here.
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import TokenObtainPairView

from django.contrib.auth.hashers import make_password
from rest_framework import status

from smtplib import SMTPException

# email


from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags

import requests

from base.emails import send_email_reset_password
from django.contrib.auth.tokens import (
    default_token_generator,
    PasswordResetTokenGenerator,
)
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode

from django_otp.oath import TOTP
from django_otp.util import random_hex
from django.core.cache import cache

from django.utils.encoding import force_bytes

from base.views.notification_views import save_notification
from django.core.exceptions import ObjectDoesNotExist

# recaptcha
from base.utils import verify_recaptcha, is_mobile

from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger


# new


@api_view(["PUT"])
@permission_classes([IsAdminUser])
def update_user_detail(request, pk):
    try:
        user = User.objects.get(pk=pk)
        if user:

            user.first_name = request.data["first_name"]
            user.last_name = request.data["last_name"]
            user.phone = request.data["phone"]
            user.email = request.data["email"]
            user.role = request.data["role"]
            if "password" in request.data and request.data["password"] != "":
                user.set_password(request.data["password"])

            user.save()
            return Response({"detail": "This User has been updated successfully."})

        else:
            return Response({"detail": "Sorry, This User does not exist"}, status=401)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This User does not exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsAdminUser])
def get_user_detail(request, pk):
    try:
        user = User.objects.get(pk=pk)
        if user:
            serializer = UserSerializer(user, many=False)
            return Response(serializer.data)
        else:
            return Response({"detail": "Sorry, This User Does Not Exist"}, status=401)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This User Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["POST"])
@permission_classes([IsAdminUser])
def create_new_user(request):
    first_name = request.data["first_name"]
    last_name = request.data["last_name"]
    email = request.data["email"]
    phone = request.data["phone"]
    password = request.data["password"]
    role = request.data["role"]
    # check mail is exist
    try:
        user = User.objects.filter(email=email).all()
        if user:
            return Response({"detail": "Sorry, This email already exists"}, status=402)
        else:
            newuser = User.objects.create_user(
                email=email,
                phone=phone,
                first_name=first_name,
                last_name=last_name,
                role=role,
                is_active=True,
                password=password,
            )
            return Response({"detail": "This user has been added successfully"})
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["DELETE"])
@permission_classes([IsAdminUser])
def deleteNewUser(request, pk):
    try:
        user = User.objects.get(pk=pk)
        if user:
            user.delete()
            return Response("User was deleted", status=200)
        else:
            return Response({"detail": "Sorry, This User Does Not Exist"}, status=401)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This User Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsAdminUser])
def getNewListUsers(request):
    users = User.objects.filter(role__in=[1, 2, 4]).exclude(id=request.user.id)
    # paginate
    page = request.query_params.get("page")
    pages = 1
    count = 1
    if page != "0":
        paginator = Paginator(users, 10)

        try:
            users = paginator.page(page)
        except PageNotAnInteger:
            users = paginator.page(1)
        except EmptyPage:
            users = paginator.page(paginator.num_pages)

        if page == None:
            page = 1
        pages = paginator.num_pages
        count = paginator.count

    serializer = UserSerializer(users, many=True)
    return Response(
        data={
            "users": serializer.data,
            "page": page,
            "pages": pages,
            "count": count,
        }
    )


# last


class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
    # def validate(self, attrs):
    #     data = super().validate(attrs)

    #     serializer = UserSerializer(self.user).data
    #     for k, v in serializer.items():
    #         data[k] = v

    #     return data
    # def validate(self, attrs):
    #     email = attrs.get('email')
    #     try:
    #         # Attempt to validate and authenticate the user
    #         data = super().validate(attrs)
    #     except AuthenticationFailed as e:
    #         # Here, you can customize the error message for authentication failure
    #         raise ValidationError({'detail': "Nom d'utilisateur ou mot de passe incorrect."}) from e

    #     # Assuming authentication is successful, proceed with the original logic
    #     serializer = UserSerializer(self.user).data

    #     for k, v in serializer.items():
    #         data[k] = v

    #     return data

    def validate(self, attrs):
        email = attrs.get("email")
        password = attrs.get("password")

        if email and password:
            email = email.lower()

            # Perform a case-insensitive search for the user by email
            user = User.objects.filter(email__iexact=email).first()

            if user:
                # Authenticate the user

                user = authenticate(email=user.email, password=password)
                if attrs.get("fb_token"):
                    user.token_fb = attrs.get(
                        "fb_token"
                    )  # refresh token for notification
                    user.save()
                if user:
                    data = super().validate(attrs)
                    serializer = UserSerializer(user).data

                    for k, v in serializer.items():
                        data[k] = v

                    return data
                else:
                    raise AuthenticationFailed(
                        {"detail": "Nom d'utilisateur ou mot de passe incorrect."}
                    )
            else:
                raise AuthenticationFailed(
                    {"detail": "Nom d'utilisateur ou mot de passe incorrect."}
                )
        else:
            raise ValidationError(
                {"detail": "Les champs 'email' et 'password' sont requis."}
            )

    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)

        # Add custom claims
        token["role"] = user.role
        token["email"] = user.email
        # ...

        return token


class MyTokenObtainPairView(TokenObtainPairView):
    serializer_class = MyTokenObtainPairSerializer


@api_view(["POST"])
def registerUser(request):
    token = request.data.get("token")  # recaptcha
    # user_agent = request.headers.get('User-Agent')
    score = verify_recaptcha(token)
    # print(score)

    if not is_mobile and score >= 0.5 or is_mobile:
        serializer = UserSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            if (
                User.objects.filter(email=request.data["email"].lower()).exists()
                and not User.objects.filter(email=request.data["email"].lower())
                .first()
                .is_active
            ):
                return Response(
                    data={
                        "detail": "Cet email est desactivé, contactez nous pour resoudre votre problem !"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if User.objects.filter(email=request.data["email"].lower()).exists():
                return Response(
                    data={"detail": "Un utilisateur avec cet email existe déjà !"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            user = serializer.save(
                is_active=True, role=3, email=request.data["email"].lower()
            )

            if user:
                # send user welcome
                html_content = render_to_string(
                    "welcome.html", {"first_name": user.first_name}
                )  # render with dynamic value
                text_content = strip_tags(
                    html_content
                )  # Strip the html tag. So people can see the pure text at least.
                msg = EmailMultiAlternatives(
                    "Welcome to Aylink",
                    text_content,
                    "Aylink <<EMAIL>>",
                    [user.email],
                )
                msg.attach_alternative(html_content, "text/html")
                sent_count = msg.send()
                EmailStatus.objects.create(
                    user=user, sent_count=sent_count, status="USER REGISTRATION"
                )

                if sent_count == 1:
                    print("Email sent successfully")

                else:
                    print("Failed to send email")

                postdata = {"email": user.email, "first_name": user.first_name}

                # send welcome to aylink

                # send email to Aylink
                # html_content = render_to_string('welcome.html',{"first_name":user.first_name}) # render with dynamic value
                # text_content = strip_tags(html_content) # Strip the html tag. So people can see the pure text at least.
                # <AUTHOR> <EMAIL>", ["<EMAIL>"])
                # msg2.attach_alternative(html_content, "text/html")
                # msg2.send()

                # save notification
                save_notification(
                    "Nous sommes ravis de vous accueillir sur AyLink",
                    "Nous vous souhaitons une agréable expérience sur la plateforme AyLink. la plateforme de billetterie en ligne qui vous offre un accès facile et sécurisé à des événements passionnants.",
                    user,
                    None,
                    1,
                    None,
                    False,
                )

                serializer = UserSerializer(user, many=False)
                return Response(serializer.data)
                # return Response(json, status=status.HTTP_201_CREATED)

        return Response(
            data={"detail": "Please check your informations !"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    else:
        return Response(
            data={"detail": "Something went wrong, please try again !"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def updateUserProfile(request):
    user = request.user
    serializer = UserSerializer(user, many=False)

    data = request.data
    user.user_name = data["user_name"]
    user.email = data["email"]

    if data["password"] != "":
        user.password = make_password(data["password"])

    user.save()

    return Response(serializer.data)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def getUserProfile(request):
    user = request.user
    serializer = UserSerializer(user, many=False)
    return Response(serializer.data)


@api_view(["GET"])
@permission_classes([IsAdminUser])
def getUsers(request):
    users = User.objects.all()
    serializer = UserSerializer(users, many=True)
    return Response(serializer.data)


@api_view(["GET"])
@permission_classes([IsAdminUser])
def getUserById(request, pk):
    user = User.objects.get(id=pk)
    serializer = UserSerializer(user, many=False)
    return Response(serializer.data)


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def updateUser(request, pk):
    user = User.objects.get(id=pk)
    if request.user.id == user.id:
        data = request.data
        user.first_name = data["first_name"]
        user.last_name = data["last_name"]
        user.phone = data["phone"]
        # user.username = data['email']
        user.email = data["email"]
        user.save()

        serializer = UserSerializer(user, many=False)

        return Response(serializer.data)
    else:
        return Response(
            {"detail": "You are not autorized to do this action "}, status=401
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def updateUserPassword(request, pk):
    user = User.objects.get(id=pk)
    if request.user.id == user.id:
        data = request.data

        if user.check_password(request.data["old_password"]):
            user.password = make_password(data["password"])
            user.save()
            serializer = UserSerializer(user, many=False)
            return Response(serializer.data)
        else:
            return Response({"detail": "Old Password is not correct ! "}, status=400)
    else:
        return Response(
            {"detail": "You are not autorized to do this action "}, status=401
        )


@api_view(["DELETE"])
@permission_classes([IsAdminUser])
def deleteUser(request, pk):
    userForDeletion = User.objects.get(id=pk)
    userForDeletion.delete()
    return Response("User was deleted")


# @api_view(['DELETE'])
# @permission_classes([IsAuthenticated])
# def deleteMyProfile(request):
#     user = request.user
#     if not user.is_staff and user.is_active and is not user.is_deleted:

#         user.is_deleted = True
#         user.is_active = False

#     user.save()
#     return Response('User was deleted')


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def deleteMyProfile(request):
    user = request.user
    # Corrected logical conditions and syntax
    if not user.is_staff and user.is_active and not user.is_deleted:
        with transaction.atomic():
            user.is_deleted = True
            user.is_active = False
            user.save()
        return Response({"message": "User was deleted"}, status=200)
    else:
        # Handling for when deletion conditions are not met
        return Response(
            {
                "message": "No action taken. User cannot be deleted or is already marked as deleted."
            },
            status=400,
        )


import time
import hashlib


@api_view(["POST"])
def password_reset(request):
    email = request.data.get("email")
    check_mobile = request.data.get("check_mobile")
    if email:
        user = User.objects.filter(email=email).first()

        if user is not None:
            # generate password reset token and send email
            token_generator = PasswordResetTokenGenerator()
            token = token_generator.make_token(user)
            uidb64 = urlsafe_base64_encode(force_bytes(user.pk))
            cache.delete(email)
            otp = TOTP(key=bytes(random_hex(20), "utf-8"), step=300)
            print(otp)
            cache.set(email, otp.token(), 300)
            otp_value = otp.token()

            email_subject = "Réinitialisation du mot de passe"
            is_sent = send_email_reset_password(email, email_subject, otp_value)

            if is_sent:
                otp_cd = None
                if check_mobile:
                    otp_cd = otp_value
                else:
                    my_str = str(otp_value).encode()
                    otp_cd = hashlib.sha256(my_str).hexdigest()

                user.token_fb = otp_value
                user.save()

                return Response(
                    {
                        "detail": "Réinitialisation du mot de passe envoyé.",
                        "status": "success",
                        "email": email,
                        "otp_code": otp_cd,
                    },
                    status=200,
                )

            else:
                return Response({"detail": "le mail n'a pas été envoyé "}, status=400)

            # if is_sent :
            # else :
            #     return Response({'detail': "Veuillez vérifier votre email ! ","status":""},status=400)
        else:
            return Response({"detail": "Utilisateur introuvable"}, status=400)
    return Response({"detail": "Utilisateur introuvable"}, status=400)


@api_view(["POST"])
def password_reset_confirm(request):
    email = request.data.get("email", "")
    otp_value = request.data.get("otp", "")
    new_password = request.data.get("new_password", "")
    otp_valid = request.data.get("otp_valid", "")
    check_mobile = request.data.get("check_mobile")
    hashotp = request.data.get("hash")

    if email and otp_value and new_password:

        if otp_valid and check_mobile:
            if str(otp_valid) == str(otp_value):
                user = User.objects.filter(email=email).first()
                if user is not None:
                    user.set_password(new_password)
                    user.save()
                    # cache.delete(email)  # Ensure OTP is deleted after successful reset
                    return Response(
                        {
                            "status": "success",
                            "detail": "Le mot de passe a été réinitialisé",
                        }
                    )
                else:
                    return Response(
                        {"detail": "L'utilisateur avec cet email n'existe pas"},
                        status=400,
                    )
            else:
                return Response({"detail": "L'OTP n'est pas valide"}, status=400)

        else:
            if hashotp:
                my_str = str(otp_value).encode()
                hashed = hashlib.sha256(my_str).hexdigest()
                if str(hashotp) == str(hashed):
                    user = User.objects.filter(email=email).first()
                    if user is not None:
                        user.set_password(new_password)
                        user.save()
                        # cache.delete(email)  # Ensure OTP is deleted after successful reset
                        return Response(
                            {
                                "status": "success",
                                "detail": "Le mot de passe a été réinitialisé",
                            }
                        )
                    else:
                        return Response(
                            {
                                "detail": "L'utilisateur avec cet email n'existe pas",
                            },
                            status=400,
                        )
                else:
                    return Response(
                        {
                            "detail": "L'OTP n'est pas valide",
                        },
                        status=400,
                    )

            else:
                user = User.objects.filter(email=email).first()
                if user is not None:
                    if str(user.token_fb) == str(otp_value):
                        user.set_password(new_password)
                        user.save()
                        return Response(
                            {
                                "status": "success",
                                "detail": "Le mot de passe a été réinitialisé",
                            }
                        )
                    else:
                        return Response({"detail": "L'OTP a expiré"}, status=400)

                else:
                    return Response(
                        {
                            "detail": "L'utilisateur avec cet email n'existe pas",
                        },
                        status=400,
                    )

    else:
        return Response(
            {
                "detail": "Veuillez indiquer l'adresse email, l'OTP et le nouveau mot de passe."
            },
            status=400,
        )
