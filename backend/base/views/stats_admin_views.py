from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.core.paginator import Pa<PERSON>ator, EmptyPage, PageNotAnInteger
from base.models import OrderItem, Order, Organizer, Product, Ticket
from base.serializers import OrderItemSerializer, OrderSerializer
from datetime import datetime
from rest_framework import status
from django.http import JsonResponse

# for chart
from django.db.models import Count
from django.db.models.functions import ExtractDay
from django.utils import timezone

from django.db.models.functions import <PERSON>runc<PERSON>ont<PERSON>, TruncWeek, TruncDay, TruncHour
import decimal
from dateutil.relativedelta import relativedelta
from datetime import datetime, timedelta
from rest_framework.permissions import IsAuthenticated, IsAdminUser


@api_view(["GET"])
@permission_classes([IsAdminUser])
def get_order_admin_stats(request):

    organizerId = request.query_params.get("organizerId", None)
    start_date = request.query_params.get("startdate", None)
    end_date = request.query_params.get("enddate", None)

    orders = Order.objects.filter(isPaid=True).order_by("-paidAt")
    if organizerId and organizerId != "" and organizerId != "all":
        try:
            # Ensure the organizer exists
            organizer = Organizer.objects.get(pk=organizerId)
        except Organizer.DoesNotExist:
            return Response({"error": "Organizer does not exist"}, status=400)

        orders = orders.filter(orderitem__organizer=organizer).distinct()

    if start_date:
        orders = orders.filter(paidAt__gte=start_date)
    if end_date:
        orders = orders.filter(paidAt__lte=end_date)

    # paginate
    page = request.query_params.get("page", "0")
    pages = 1
    count = 1
    if page != "0":
        paginator = Paginator(orders, 10)

        try:
            orders = paginator.page(page)
        except PageNotAnInteger:
            orders = paginator.page(1)
        except EmptyPage:
            orders = paginator.page(paginator.num_pages)

        if page == None:
            page = 1
        pages = paginator.num_pages
        count = paginator.count

    serializer = OrderSerializer(orders, many=True)
    return Response(
        data={
            "orders": serializer.data,
            "page": page,
            "pages": pages,
            "count": count,
        }
    )


@api_view(["GET"])
@permission_classes([IsAdminUser])
def getAdminStats(request):
    try:
        # all organizers
        # only one organizer

        organizerId = request.query_params.get("organizerId", None)
        start_date = request.query_params.get("startdate", None)
        end_date = request.query_params.get("enddate", None)

        if organizerId == "all":
            _orderitems = OrderItem.objects.filter(order__isPaid=True)
            products = Product.objects.filter(isActive=True).order_by("-createdAt")
        else:
            organizer = Organizer.objects.get(pk=organizerId)
            _orderitems = OrderItem.objects.filter(
                organizer=organizer, order__isPaid=True
            )
            # fees = decimal.Decimal( (100 - organizer.feesPercent) / 100)
            products = Product.objects.filter(
                isActive=True, organizer=organizer
            ).order_by("-createdAt")

        if start_date and start_date is not None and start_date != "":

            _orderitems = _orderitems.filter(order__paidAt__gte=start_date)

        if end_date and end_date is not None and end_date != "":

            _orderitems = _orderitems.filter(order__paidAt__lte=end_date)

        _sold = 0
        _revenu = 0
        _net = 0
        for i in _orderitems:
            _sold = _sold + i.qty
            _revenu = _revenu + (i.qty * i.price)

            _net += float(i.qty * i.price) * (1 - i.organizer.feesPercent / 100)

        total_tickets = 0
        for product in products:
            total_tickets = total_tickets + product.countInStock

        # orders = Order.objects.filter(orderitem__in=_orderitem,isPaid=True).order_by("-createdAt")
        # for order in orders:
        #     _revenu += order.

        # print(_orderitem[1].qty)
        # orders = Order.objects.filter(orderitem__in=_orderitem,isPaid=True).order_by("-createdAt")

        return Response(
            {
                "tickets_sold": _sold,
                "revenu": _revenu,
                "total_places": total_tickets,
                "net_profit": float("{:.2f}".format(_net)),
            }
        )
    except Exception as e:
        print(e)
        return Response(
            {"tickets_sold": 0, "revenu": 0, "total_places": 0, "net_profit": 0}
        )


@permission_classes([IsAdminUser])
@api_view(["GET"])
def getAdminChart(request):
    time_filter = request.query_params.get("time_filter", "today")
    organizer = Organizer.objects.get(user=request.user)
    _tickets = Ticket.objects.filter(orderItem__organizer=organizer)

    if time_filter == "this_month":
        start_date = timezone.now().replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )
        trunc = TruncDay("paidAt")
        date_format = "%d"
    elif time_filter == "today":
        start_date = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        trunc = TruncHour("paidAt")
        date_format = "%H"
    elif time_filter == "all_time":
        start_date = None
        trunc = TruncMonth("paidAt")
        date_format = "%Y-%b"
    else:
        return Response({"error": "Invalid time_filter parameter"}, status=400)

    if start_date is not None:
        sales_data = (
            _tickets.filter(paidAt__gte=start_date)
            .annotate(truncated_date=trunc)
            .values("truncated_date")
            .annotate(sales=Count("id"))
            .order_by("truncated_date")
        )
    else:
        sales_data = (
            _tickets.annotate(truncated_date=trunc)
            .values("truncated_date")
            .annotate(sales=Count("id"))
            .order_by("truncated_date")
        )

    sales_data = [
        {"date": d["truncated_date"].strftime(date_format), "sales": d["sales"]}
        for d in sales_data
    ]

    return Response(sales_data)


@api_view(["GET"])
@permission_classes([IsAdminUser])
def getAdminOverview(request):
    organizer = Organizer.objects.get(user=request.user)
    products = Product.objects.filter(organizer=organizer, isDeleted=False).order_by(
        "-createdAt"
    )

    data = []
    for product in products:
        _p = {}
        _orderItems = OrderItem.objects.filter(
            organizer=organizer, product=product, order__isPaid=True
        )
        _p["product"] = {"name": product.name, "countInStock": product.countInStock}
        amount = 0
        _qty = 0
        _sold_online = 0
        _sold_offline = 0
        for _o in _orderItems:
            amount += _o.price * _o.qty
            _qty += _o.qty
            if _o.order.isOffline:
                _sold_offline += _o.qty
            else:
                _sold_online += _o.qty

        _p["order"] = {
            "qty": _qty,
            "amount": amount,
            "sold_offline": _sold_offline,
            "sold_online": _sold_online,
        }
        data.append(_p)
    return JsonResponse(data, safe=False)


@api_view(["GET"])
@permission_classes([IsAdminUser])
def getAdminInventory(request):
    organizer = Organizer.objects.get(user=request.user)
    _orderitems = OrderItem.objects.filter(organizer=organizer, order__isPaid=True)

    products = Product.objects.filter(
        organizer=organizer, isDeleted=False, isActive=True
    )

    total = 0
    for _p in products:
        total += _p.countInStock
    tickets_sold = Ticket.objects.filter(orderItem__in=_orderitems).count()

    return Response({"sold": tickets_sold, "total": total})
