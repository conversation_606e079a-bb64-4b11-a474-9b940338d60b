from base.models import Notification
from base.serializers import NotificationSerializer

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response

from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.shortcuts import get_object_or_404


# get list notification
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_my_notification(request):
    notifications = Notification.objects.filter(user=request.user).order_by(
        "-created_at"
    )
    serializer = NotificationSerializer(notifications, many=True)
    return Response({"notifications": serializer.data})


def save_notification(title, body, user, order, type_notif, icon, is_admin):
    notif = Notification.objects.create(
        title=title,
        body=body,
        user=user,
        type_notif=type_notif,
        order=order,
        icon=icon,
        is_admin=is_admin,
    )
    return notif
    # try:
    #     db = firestore.client()
    #     typ_notif = "Notification"
    #     if type_notif == 1:
    #         typ_notif = "Message"
    #     elif type_notif == 2:
    #         typ_notif = "Diagnostic"
    #     elif type_notif == 3:
    #         typ_notif = "Tarif"
    #     elif type_notif == 4:
    #         typ_notif = "Command"
    #     data = {
    #         "type_notification": typ_notif,
    #         "to_user": None if user is None else user.email,
    #         "title": title,
    #         "body": body,
    #         "order": None if order is None else order.id,
    #         "is_admin": is_admin,
    #         "is_vue": False,
    #     }
    #     # Add a new document with an automatically generated ID
    #     db.collection("notification").add(data)
    #     # return notif
    #     try:
    #         if user is not None and user.token_fb and user.token_fb != "":
    #             url = "https://fcm.googleapis.com/fcm/send"
    #             headers = {
    #                 "Authorization": "key=AIzaSyDbh5ysz2KPpQ6NbiiYYOzDmGXUpTISdCE",
    #                 "Content-Type": "application/json",
    #             }
    #             data = {
    #                 "to": user.token_fb,
    #                 "notification": {
    #                     "title": title,
    #                     "body": body,
    #                 },
    #                 "data": data,
    #             }
    #             response = requests.post(url, headers=headers, json=data)
    #     except:
    #         print(f"Firestore error:  ")
    # except:
    #     print(f"Firestore error:  ")
