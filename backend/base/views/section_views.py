from django.shortcuts import render, get_object_or_404

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from base.permissions import IsOrganizer
from base.models import Section, Organizer
from base.serializers import SectionSerializer
from django.core.exceptions import ObjectDoesNotExist

from rest_framework import status


# new


@api_view(["POST"])
@permission_classes([IsOrganizer])
def createNewSection(request):
    try:
        organizerId = request.data["organizer"]
        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer:

                serializer = SectionSerializer(data=request.data)
                if serializer.is_valid(raise_exception=True):
                    serializer.save(organizer=organizer)
                # serializer = SectionSerializer(section, many=False)
                return Response({"section": serializer.data})
            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist 2"}, status=401
                )
        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist 2"}, status=401
            )
    except ObjectDoesNotExist:
        return Response(
            {"detail": "Sorry, This Organizer Does Not Exist 3"}, status=401
        )
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getNewSections(request):
    try:
        organizerId = request.query_params.get("organizer", "")
        if organizerId and organizerId != "":
            organizer = Organizer.objects.get(pk=organizerId)
            if organizer:
                # organizer = get_object_or_404(Organizer, user=request.user)
                sections = Section.objects.filter(organizer=organizer)

                serializer = SectionSerializer(sections, many=True)

                return Response({"sections": serializer.data})

            else:
                return Response(
                    {"detail": "Sorry, This Organizer Does Not Exist 1"}, status=401
                )

        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist 2"}, status=401
            )

    except ObjectDoesNotExist:
        return Response(
            {"detail": "Sorry, This Organizer Does Not Exist 3"}, status=401
        )
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# last


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getSections(request):

    organizer = get_object_or_404(Organizer, user=request.user)
    sections = Section.objects.filter(organizer=organizer)

    serializer = SectionSerializer(sections, many=True)

    return Response({"sections": serializer.data})


@api_view(["GET"])
def getOrganizerSections(request, slug):
    organizer = get_object_or_404(Organizer, slug=slug)
    sections = Section.objects.filter(organizer=organizer)
    serializer = SectionSerializer(sections, many=True)

    return Response({"sections": serializer.data})


@api_view(["POST"])
@permission_classes([IsOrganizer])
def createSection(request):

    data = request.data
    organizer = Organizer.objects.get(user=request.user)
    # name = data["name"]
    # image = request.FILES.get("image")

    # section = Section.objects.create(
    #     name=name,
    #     image=image,
    #     organizer=organizer
    # )
    serializer = SectionSerializer(data=request.data)
    if serializer.is_valid(raise_exception=True):
        serializer.save(organizer=organizer)

    # serializer = SectionSerializer(section, many=False)
    return Response({"section": serializer.data})


@api_view(["PUT"])
@permission_classes([IsOrganizer])
def updateSection(request, pk):
    data = request.data
    # only the organizer can change his sections
    section = Section.objects.get(_id=pk)

    if "name" in data and data["name"] != "":
        section.name = data["name"]
    else:
        return Response({"detail": "Name field is required"}, status=400)

    if "image" in data and data["image"] != "":
        section.image = request.FILES.get("image")
    # else:
    #     return Response({"detail": "Image field is required"}, status=400)
    section.save()

    serializer = SectionSerializer(section, many=False)
    return Response({"section": serializer.data})

    # if section.organizer == Organizer.objects.get(user=request.user):

    # else :
    #     return Response({'detail': "You don't have permissions "})


@api_view(["DELETE"])
@permission_classes([IsOrganizer])
def deleteSection(request, pk):

    section = Section.objects.get(_id=pk)
    section.delete()

    serializer = SectionSerializer(section, many=False)

    return Response({"section": serializer.data})
