from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import Is<PERSON><PERSON>ent<PERSON><PERSON>, IsAdminUser
from rest_framework.response import Response

from rest_framework import status
from django.utils import timezone
from django.core.mail import send_mail


@api_view(["POST"])
# @permission_classes([IsAdminUser])
def check_email(request):
    try:
        ss = send_mail(
            "Email test",
            "Here is the message.",
            "AyLink <<EMAIL>>",
            [request.data["email"]],
        )
        print("email was sent succu")
        print(ss)
    except Exception as e:
        print(f"An error occurred while sending the email: {e}")

    return Response({"detail": "Email has been sent "})
