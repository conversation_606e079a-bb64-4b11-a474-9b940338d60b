from django.shortcuts import get_object_or_404

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import Is<PERSON><PERSON><PERSON><PERSON>ted, IsAdminUser
from rest_framework.response import Response
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

from base.permissions import IsOrganizer

from base.models import Organizer, ExpiredOrganizer, User, OrganizerCategory
from base.serializers import (
    OrganizerSerializer,
    ExpiredOrganizerSerializer,
    UserSerializer,
)
from django.db.models import Q
from rest_framework import status
from django.utils import timezone
from django.core.exceptions import ObjectDoesNotExist


# new
@api_view(["DELETE"])
@permission_classes([IsAdminUser])
def delete_organizer(request, pk):
    try:
        organizer = Organizer.objects.get(pk=pk)
        if organizer:
            organizer.delete()

            return Response(
                {"detail": "This Organizer has been deleted successfull"}, status=200
            )
        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )

    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["PUT"])
@permission_classes([IsAdminUser])
def update_organizer_detail(request, pk):
    try:
        organizer = Organizer.objects.get(pk=pk)
        if organizer:
            user = User.objects.get(pk=request.data["user_select"])
            if user is None:
                return Response(
                    {"detail": "Sorry, User not found, please try again"}, status=401
                )
            # search catgeory
            category = OrganizerCategory.objects.get(pk=request.data["category_select"])
            if category is None:
                return Response(
                    {"detail": "Sorry, Category not found, please try again"},
                    status=401,
                )

            organizer_avatar = organizer.avatar
            if (
                "organizer_avatar" in request.data
                and request.data["organizer_avatar"] != ""
            ):
                organizer_avatar = request.FILES.get("organizer_avatar")

            logo_ticket = organizer.logoTicket
            if "logo_ticket" in request.data and request.data["logo_ticket"] != "":
                logo_ticket = request.FILES.get("logo_ticket")

            organizer_image = organizer.image
            if (
                "organizer_image" in request.data
                and request.data["organizer_image"] != ""
            ):
                organizer_image = request.FILES.get("organizer_image")

            organizer_cover = organizer.cover
            if (
                "organizer_cover" in request.data
                and request.data["organizer_cover"] != ""
            ):
                organizer_cover = request.FILES.get("organizer_cover")

            date_from = (
                None
                if request.data["is_daily"] == True
                or request.data["is_daily"] == "True"
                else organizer.organizerDateFrom
            )
            date_to = (
                None
                if request.data["is_daily"] == True
                or request.data["is_daily"] == "True"
                else organizer.organizerDateTo
            )

            max_date = None
            if "max_date" in request.data and request.data["max_date"] != "":
                max_date = request.data["max_date"]

            if "date_from" in request.data and request.data["date_from"] != "":
                date_from = request.data["date_from"]

            if "date_to" in request.data and request.data["date_to"] != "":
                date_to = request.data["date_to"]

            organizer.name = request.data["organizer_name"]
            organizer.category = category
            organizer.user = user
            organizer.avatar = organizer_avatar
            organizer.logoTicket = logo_ticket
            organizer.image = organizer_image
            organizer.cover = organizer_cover
            organizer.about = request.data["organizer_about"]
            organizer.isActive = request.data["organizer_status"]
            organizer.feesPercent = request.data["organizer_percent"]
            organizer.organizerDateFrom = date_from
            organizer.organizerDateTo = date_to
            organizer.max_date = max_date

            # organizer.organizerDateFrom = request.data["date_from"]
            # organizer.organizerDateTo = request.data["date_to"]
            organizer.show_mail = request.data["show_mail"]
            organizer.is_daily = request.data["is_daily"]
            organizer.isticket_customization = request.data["isticket_customization"]
            

            organizer.save()
            return Response({"detail": "This Organizer has been successfully updated"})

        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsAdminUser])
def get_organizer_detail(request, pk):
    try:
        organizer = Organizer.objects.get(pk=pk)
        if organizer:
            serializer = OrganizerSerializer(organizer, many=False)
            return Response(
                data={
                    "organizer": serializer.data,
                }
            )
        else:
            return Response(
                {"detail": "Sorry, This Organizer Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Organizer Does Not Exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["POST"])
@permission_classes([IsAdminUser])
def create_new_organizer(request):
    try:
        # search user
        user = User.objects.get(pk=request.data["user_select"])
        if user is None:
            return Response(
                {"detail": "Sorry, User not found, please try again"}, status=401
            )
        # search catgeory
        category = OrganizerCategory.objects.get(pk=request.data["category_select"])
        if category is None:
            return Response(
                {"detail": "Sorry, Category not found, please try again"}, status=401
            )

        organizer_avatar = None
        if (
            "organizer_avatar" in request.data
            and request.data["organizer_avatar"] != ""
        ):
            organizer_avatar = request.FILES.get("organizer_avatar")

        logo_ticket = None
        if "logo_ticket" in request.data and request.data["logo_ticket"] != "":
            logo_ticket = request.FILES.get("logo_ticket")

        organizer_image = None
        if "organizer_image" in request.data and request.data["organizer_image"] != "":
            organizer_image = request.FILES.get("organizer_image")

        organizer_cover = None
        if "organizer_cover" in request.data and request.data["organizer_cover"] != "":
            organizer_cover = request.FILES.get("organizer_cover")

        max_date = None
        if "max_date" in request.data and request.data["max_date"] != "":
            max_date = request.data["max_date"]

        date_from = None
        date_to = None

        if "date_from" in request.data and request.data["date_from"] != "":
            date_from = request.data["date_from"]

        if "date_to" in request.data and request.data["date_to"] != "":
            date_to = request.data["date_to"]

        organizer = Organizer.objects.create(
            name=request.data["organizer_name"],
            category=category,
            user=user,
            avatar=organizer_avatar,
            logoTicket=logo_ticket,
            image=organizer_image,
            cover=organizer_cover,
            about=request.data["organizer_about"],
            isActive=request.data["organizer_status"],
            feesPercent=request.data["organizer_percent"],
            organizerDateFrom=date_from,
            organizerDateTo=date_to,
            show_mail=request.data["show_mail"],
            is_daily=request.data["is_daily"],
            max_date=max_date,
            isticket_customization=request.data["isticket_customization"],
        )
        return Response({"detail": "This Organizer has been successfully created"})

    except Exception as e:
        print(f"Error creating order: {e}")
        return Response(
            {"detail": "Something went wrong while creating the organizer."}, status=401
        )


@api_view(["GET"])
@permission_classes([IsAdminUser])
def get_list_users_organizers(request):
    users = User.objects.filter(role=2).order_by("-pk")

    page = request.query_params.get("page")
    pages = 1
    count = 1
    if page != "0":
        paginator = Paginator(users, 10)

        try:
            users = paginator.page(page)
        except PageNotAnInteger:
            users = paginator.page(1)
        except EmptyPage:
            users = paginator.page(paginator.num_pages)

        if page == None:
            page = 1
        pages = paginator.num_pages
        count = paginator.count

    serializer = UserSerializer(users, many=True)
    return Response(
        data={
            "users": serializer.data,
            "page": page,
            "pages": pages,
            "count": count,
        }
    )


@api_view(["GET"])
@permission_classes([IsAdminUser])
def get_list_organizers(request):
    organizers = Organizer.objects.all().order_by("-organizerDateFrom")

    filter = request.query_params.get("filter")
    if filter and filter != "":
        if filter == "current":
            organizers = organizers.filter(organizerDateFrom__gte=timezone.now())
        if filter == "last":
            organizers = organizers.filter(organizerDateFrom__lt=timezone.now())
        if filter == "active":
            organizers = organizers.filter(isActive=True)

    # paginate
    page = request.query_params.get("page")
    pages = 1
    count = 1
    if page != "0":
        paginator = Paginator(organizers, 10)

        try:
            organizers = paginator.page(page)
        except PageNotAnInteger:
            organizers = paginator.page(1)
        except EmptyPage:
            organizers = paginator.page(paginator.num_pages)

        if page == None:
            page = 1
        pages = paginator.num_pages
        count = paginator.count

    serializer = OrganizerSerializer(organizers, many=True)
    return Response(
        data={
            "organizers": serializer.data,
            "page": page,
            "pages": pages,
            "count": count,
        }
    )


@api_view(["GET"])
@permission_classes([IsOrganizer])
def getOrganizersUser(request):
    organizers = Organizer.objects.filter(isActive=True, user=request.user).order_by(
        "organizerDateFrom"
    )
    page = 1
    page = int(page)
    serializer = OrganizerSerializer(organizers, many=True)
    return Response({"organizers": serializer.data, "page": page})


# last


@api_view(["GET"])
def getAllOrganizers(request):
    organizers = (
        Organizer.objects.filter(isActive=True)
        # .filter(organizerDateFrom__gte=timezone.now())
        .order_by("organizerDateFrom")
    )
    page = 1
    page = int(page)
    serializer = OrganizerSerializer(organizers, many=True)
    return Response({"organizers": serializer.data, "page": page})


# search and filter on orginizers
@api_view(["GET"])
def findOrganizers(request):
    search = request.GET.get("search")
    category_id = request.GET.get("category_id")
    date = request.GET.get("date")

    organizers = (
        Organizer.objects.filter(isActive=True)
        .filter(Q(organizerDateFrom__gte=timezone.now()) | Q(is_daily=True))
        .order_by("organizerDateFrom")
    )

    if search:
        organizers = organizers.filter(name__icontains=search)

    if category_id:
        organizers = organizers.filter(category_id=category_id)

    if date:
        organizers = organizers.filter(
            organizerDateFrom__lte=date, organizerDateTo__gte=date
        )

    page = 1
    page = int(page)
    serializer = OrganizerSerializer(organizers, many=True)
    return Response({"organizers": serializer.data, "page": page})


# active orginizers
@api_view(["GET"])
def getOrganizers(request):
    organizers = (
        Organizer.objects.filter(isActive=True, is_see=True)
        .filter(Q(organizerDateFrom__gte=timezone.now()) | Q(is_daily=True))
        .order_by("organizerDateFrom")
    )
    
    # home_see = request.query_params.get("home", "")
    # if home_see and home_see != "":
    #     organizers = (
    #         Organizer.objects.filter(isActive=True, is_see=True)
    #         .filter(Q(organizerDateFrom__gte=timezone.now()) | Q(is_daily=True))
    #         .order_by("organizerDateFrom")
    #     )
    # else:
    #     organizers = (
    #         Organizer.objects.filter(isActive=True)
    #         .filter(Q(organizerDateFrom__gte=timezone.now()) | Q(is_daily=True))
    #         .order_by("organizerDateFrom")
    #     )
    page = 1
    page = int(page)
    serializer = OrganizerSerializer(organizers, many=True)
    return Response({"organizers": serializer.data, "page": page})


@api_view(["GET"])
def getOrganizerEvents(request):

    organizer = Organizer.objects.get(pk=request.user.id)

    serializer = OrganizerSerializer(organizer, many=False)

    return Response({"organizer": serializer.data})


# past organizers
@api_view(["GET"])
def getExpiredOrganizers(request):

    exp_organizers = Organizer.objects.filter(
        organizerDateFrom__lt=timezone.now(), is_daily=False
    ).order_by("-organizerDateFrom")

    page = request.query_params.get("page", "0")
    if page != "0":
        paginator = Paginator(exp_organizers, 9)
        try:
            exp_organizers = paginator.page(page)
        except PageNotAnInteger:
            exp_organizers = paginator.page(1)
        except EmptyPage:
            exp_organizers = paginator.page(paginator.num_pages)
        pages = paginator.num_pages
        count = paginator.count
    else:
        pages = 1
        count = exp_organizers.count()

    serializer = OrganizerSerializer(exp_organizers, many=True)

    return Response(
        {
            "organizers": serializer.data,
            "page": page,
            "pages": pages,
            "count": count,
        }
    )


@api_view(["GET"])
def getOrganizer(request, slug):
    organizer = get_object_or_404(Organizer, slug=slug)

    serializer = OrganizerSerializer(organizer, many=False)

    return Response(serializer.data)
