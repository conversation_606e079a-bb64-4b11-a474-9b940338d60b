from rest_framework.decorators import api_view, permission_classes
from django.shortcuts import get_object_or_404
from base.models import Donation,Organizer 
from rest_framework.response import Response
from base.permissions import IsOrganizer
from base.serializers import DonationSerializer
from rest_framework import status
from django.utils import timezone
from decimal import Decimal

@api_view(['POST'])
# @permission_classes([IsOrganizer])
def donation_create(request):
    organizer = get_object_or_404(Organizer,pk=3)
    # request.data["organizer"] = 3
    serializer = DonationSerializer(data=request.data)
    if serializer.is_valid(raise_exception=True):
        serializer.save(organizer=organizer)
        return Response(serializer.data)
    
    return Response({'detail':"donation was not added"},status=400)

@api_view(['GET'])
@permission_classes([IsOrganizer])
def donation_list(request):
    organizer = Organizer.objects.get(user=request.user)
    donations = Donation.objects.filter(isPaid=True).filter(organizer=organizer)
    serializer = DonationSerializer(donations,many=True)
    return Response(serializer.data)

@api_view(['GET'])
# @permission_classes([IsOrganizer])
def donation_detail(request,pk):
    donation = get_object_or_404(Donation,pk=pk)
    serializer = DonationSerializer(donation,many=False)
    return Response(serializer.data)

@api_view(['PUT'])
# @permission_classes([IsOrganizer])
def donation_topaid(request,pk):
    donation = Donation.objects.get(pk=pk)
    donation.isPaid = True
    donation.save()
    
    return Response({'detail':"donation is paid"})