from django.urls import path
from base.views import category_views as views

urlpatterns = [
    path("", views.getCategories, name="categories"),
    path("add/", views.createCategory, name="create-category"),
    # new
    path("create/", views.create_catergory, name="create_category"),
    path("detail/<str:pk>/", views.get_catergory_detail, name="get_catergory_detail"),
    path("update/<str:pk>/", views.update_category, name="update_category"),
    path("delete/<str:pk>/", views.delete_catergory, name="delete_catergory"),
    # last
    path("<str:pk>/update/", views.updateCategory, name="update-category"),
    path("<str:pk>/delete/", views.deleteCategory, name="delete-category"),
]
