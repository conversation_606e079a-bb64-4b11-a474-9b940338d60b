from django.urls import path
from base.views import ticket_views as views

urlpatterns = [
    # new
    path("new-data/", views.getNewTicketsStats, name="new-tickets-stats"),
    path("new-scanned/", views.getNewTicketsScanned, name="new-tickets-scanned"),
    path("new-events", views.getNewOrganizerEvents, name="new-events-organizer"),
    path("dash-scan/<str:pk>/", views.getTicketsStatsScan, name="ticket-stats-scan"),
    path("new-scann/<str:pk>/", views.scannNewTicket, name="scan-new-ticket"),
    path(
        "scann/new-confirm/<str:pk>/",
        views.confirmNewScannTicket,
        name="confirm-new-scan-ticket",
    ),
    path("new-json-data/<str:pk>/", views.getNewTickets, name="new-tickets"),
    path("admin-json-data/", views.getNewAllTickets, name="list-new-tickets"),
    path("delete/<str:pk>/", views.delete_ticket, name="delete_ticket"),
    # last
    path("", views.getTicketsStats, name="tickets-stats"),
    path("scanned", views.getTicketsScanned, name="tickets-scanned"),
    path("json-data", views.getTickets, name="tickets"),
    path("generate-qr-code", views.getQrCode, name="qr-code"),
    path("events", views.getOrganizerEvents, name="events-organizer"),
    path("scann", views.scannTicket, name="scan-ticket"),
    path("scann/confirm", views.confirmScannTicket, name="confirm-scan-ticket"),
    path("pdf/<str:pk>", views.exportTicketPdf, name="pdf-ticket"),
    path("<str:pk>", views.getOrderTickets, name="tickets"),
]
