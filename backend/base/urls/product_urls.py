from django.urls import path
from base.views import product_views as views

urlpatterns = [
    # new
    path("admin/new-list/", views.getNewAdminProducts, name="new-admin-products"),
    path(
        "new-delete/<str:pkorg>/<str:pk>/",
        views.deleteNewProduct,
        name="new-product-delete",
    ),
    path("new-create/", views.createNewProduct, name="new-product-create"),
    path("new-update/<str:pk>/", views.updateNewProduct, name="product-new-update"),
    # last
    path("", views.getProducts, name="products"),
    path(
        "organizer/<str:organizer>/",
        views.getProductsByOrganizer,
        name="products-by-org",
    ),
    path(
        "organizertest/<str:organizer>/",
        views.getProductsByOrganizertest,
        name="products-by-org-test",
    ),
    path("admin/", views.getAdminProducts, name="admin-products"),
    path("create/", views.createProduct, name="product-create"),
    path("upload/", views.uploadImage, name="image-upload"),
    # add variation
    path(
        "<str:pk>/variations/add/",
        views.createProductVariation,
        name="create-variation",
    ),
    path("variations/<str:pk>/", views.updateProductVariation, name="update-variation"),
    path(
        "<str:pk>/variations/<str:varId>/delete/",
        views.deleteProductVariation,
        name="delete-variation",
    ),
    path("<str:pk>/images/", views.createProductImages, name="create-images"),
    path(
        "<str:pk>/images/<str:imageId>/", views.deleteProductImage, name="delete-image"
    ),
    path("<str:pk>/", views.getProduct, name="product"),
    path("slug/<slug:slug>/", views.getProductBySlug, name="product-slug"),
    path("update/<str:pk>/", views.updateProduct, name="product-update"),
    path("delete/<str:pk>/", views.deleteProduct, name="product-delete"),
    path("<str:pk>/disable/", views.disableProduct, name="product-disable"),
    path(
        "check-product-date/<str:pk>/",
        views.checkProductByDate,
        name="check-product-by-date",
    ),
]
