from django.urls import path
from base.views import section_views as views

urlpatterns = [
    path("", views.getSections, name="sections"),
    path("add/", views.createSection, name="create-section"),
    path(
        "organizer/<slug:slug>/",
        views.getOrganizerSections,
        name="organizer-list-section",
    ),
    path("<str:pk>/update/", views.updateSection, name="update-section"),
    path("<str:pk>/delete/", views.deleteSection, name="delete-section"),
    # new
    path("new-list/", views.getNewSections, name="new-sections"),
    path("new-add/", views.createNewSection, name="create-new-section"),
]
