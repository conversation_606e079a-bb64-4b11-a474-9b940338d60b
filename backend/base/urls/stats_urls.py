from django.urls import path
from base.views import stats_views as views


urlpatterns = [
    path("", views.getStats, name="get-stats"),
    path("overview/", views.getOverview, name="get-overview"),
    path("chart/", views.getChart, name="get-chart"),
    path("inventory/", views.getInventory, name="get-inventory"),
    path("days-left/", views.getDaysLeft, name="get-days-left"),
    # new
    path("new-stats/", views.getNewStats, name="get-stats"),
    path("new-chart/", views.getNewChart, name="get-new-chart"),
    path("new-inventory/", views.getNewInventory, name="get-new-inventory"),
    path("new-days-left/", views.getNewDaysLeft, name="get-new-days-left"),
    path("new-overview/", views.getNewOverview, name="get-new-overview"),
]
