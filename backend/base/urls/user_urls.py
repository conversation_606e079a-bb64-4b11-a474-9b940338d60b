from django.urls import path
from base.views import user_views as views

from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)


urlpatterns = [
    # new
    path("new-list/", views.getNewListUsers, name="new-list-users"),
    path("new-delete/<str:pk>/", views.deleteNewUser, name="user-new-delete"),
    path("create-new-user/", views.create_new_user, name="create_new_user"),
    path("detail/<str:pk>/", views.get_user_detail, name="get_user_detail"),
    path("update-user/<str:pk>/", views.update_user_detail, name="update_user_detail"),
    # last
    path("login/", views.MyTokenObtainPairView.as_view(), name="token_obtain_pair"),
    # path('register/', views.registerUser, name='register'),
    path("register/", views.registerUser, name="register-user"),
    # path('logout/blacklist/', views.blacklistTokenUpdateView.as_view(),
    #      name='blacklist')
    # path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("password-reset/", views.password_reset, name="password-reset"),
    path(
        "password-reset-confirm/",
        views.password_reset_confirm,
        name="password-reset-confirm",
    ),
    path("profile/", views.getUserProfile, name="user-profile"),
    path("profile/delete/", views.deleteMyProfile, name="user-delete"),
    # path('profile/update/', views.updateUserProfile, name="user-profile-update"),
    path("", views.getUsers, name="users"),
    path("<str:pk>/", views.getUserById, name="user"),
    path("update/<str:pk>/", views.updateUser, name="user-update"),
    path(
        "update-password/<str:pk>/",
        views.updateUserPassword,
        name="user-update-password",
    ),
    path("delete/<str:pk>/", views.deleteUser, name="user-delete"),
]
