from django.urls import path
from base.views import order_views as views


urlpatterns = [
    # new
    path("export-to-csv/", views.exportOrdersToExcel, name="export-orders-to-excel"),
    path("new-list/", views.getNewOrders, name="new-orders"),
    path(
        "orders-by-product/<str:pk>/",
        views.getOrdersByProduct,
        name="get-orders-by-product",
    ),
    path("new-export/", views.exportNewOrdersExcel, name="export-new-orders-excel"),
    path(
        "new-organizer/<str:pk>/",
        views.getNewOrganizerOrderById,
        name="organizer-new-order",
    ),
    path("new-list-admin/", views.getOrdersList, name="get-orders-list"),
    # last
    path("", views.getOrders, name="orders"),
    path("add/", views.addOrderItems, name="orders-add"),
    path("add/offline/", views.addOrderOfflineItems, name="orders-add-offline"),
    path("myorders/", views.getMyOrders, name="myorders"),
    path("export/", views.exportOrdersExcel, name="export-orders-excel"),
    path(
        "<str:pk>/generate-tickets/",
        views.generateOrderTickets,
        name="generate-tickets",
    ),
    # path('<str:pk>/delete/', views.deleteOrder, name='order-delete'),
    path("<str:pk>/export-ticket/", views.exportOrderTicketPdf, name="export-ticket"),
    path("<str:pk>/", views.getOrderById, name="user-order"),
    path("organizer/<str:pk>/", views.getOrganizerOrderById, name="organizer-order"),
    path("<str:pk>/pay/", views.updateOrderToPaid, name="pay"),
    path("<str:pk>/confirm/", views.updateOrderToPaidConfirm, name="confirm"),
    # path("<str:pk>/confirmord/", views.updateOrderToPaidConfirmOrdr, name="confirmord"),
    path("<str:pk>/checkmail/", views.checkMailOrder, name="checkMailOrder"),
    path("<str:pk>/test-admin-email/", views.testAdminEmail, name="test-admin-email"),
    # new
    path("<str:pk>/delete/", views.delete_order, name="delete_order"),
]
