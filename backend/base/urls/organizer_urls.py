from django.urls import path
from base.views import organizer_views as views


from base.views.organizer_views import (
    get_list_organizers,
    get_list_users_organizers,
    create_new_organizer,
    get_organizer_detail,
    update_organizer_detail,
    delete_organizer,
)


urlpatterns = [
    # path('', views.getCategories, name="categories"),
    path("", views.getOrganizers, name="get-organizer"),
    path("all-organizers", views.getAllOrganizers, name="get-all-organizer"),
    path("find-organizers/", views.findOrganizers, name="find-organizers"),
    path("expired/", views.getExpiredOrganizers, name="get-expired-organizer"),
    path("slug/<slug:slug>/", views.getOrganizer, name="get-organizer"),
    # path('add/', views.createOrganizer, name="create-organizer"),
    # path('<str:pk>/update/', views.updateOrganizer, name="update-organizer"),
    # path('<str:pk>/delete/', views.deleteOrganizer, name="delete-organizer"),
    # new
    path("organizers-user/", views.getOrganizersUser, name="get-organizers-user"),
    path("list-organizers/", get_list_organizers, name="get_list_organizers"),
    path("list-users/", get_list_users_organizers, name="get_list_users_organizers"),
    path("create-organizer/", create_new_organizer, name="create_new_organizer"),
    path("detail/<str:pk>/", get_organizer_detail, name="get_organizer_detail"),
    path("update/<str:pk>/", update_organizer_detail, name="update_organizer_detail"),
    path("delete/<str:pk>/", delete_organizer, name="delete_organizer"),
]
