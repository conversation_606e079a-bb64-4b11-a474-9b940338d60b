from backend import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags


def send_email_account_activation(email, email_subject, otp):
    try:
        email_body = render_to_string("password_reset_email.html", {"otp": otp})
        text_content = strip_tags(email_body)
        msg = EmailMultiAlternatives(
            email_subject, text_content, "<EMAIL>", [email]
        )
        msg.attach_alternative(email_body, "text/html")
        msg.send()
        return True
    except:
        return False


def send_email_reset_password(email, email_subject, otp_value):
    try:
        email_body = render_to_string("password_reset_email.html", {"otp": otp_value})
        text_content = strip_tags(email_body)
        msg = EmailMultiAlternatives(
            email_subject, text_content, "<EMAIL>", [email]
        )
        msg.attach_alternative(email_body, "text/html")
        msg.send()
        return True
    except:
        return False


def send_email_transfer(email, email_subject):
    email_body = render_to_string("transfer_email_sent.html", {"test": "hell"})
    text_content = strip_tags(email_body)
    msg = EmailMultiAlternatives(
        email_subject, text_content, "<EMAIL>", [email]
    )
    msg.attach_alternative(email_body, "text/html")
    msg.send()
