from django.db import models
from django.db.models import Max
import time

# from django.contrib.auth.models import User
import uuid
from django.utils.text import slugify
from django.core.exceptions import ValidationError

from django.utils import timezone
from django.contrib.auth.models import AbstractUser
from django.contrib.auth.models import (
    AbstractBaseUser,
    PermissionsMixin,
    BaseUserManager,
)
from .managers import CustomUserManager

from django.core.validators import MaxValueValidator, MinValueValidator
from random import randint
from django.utils import timezone

from django.utils.safestring import mark_safe


def get_difference(date1, date2):
    delta = date2 - date1
    return delta


from datetime import datetime
from base.utils import get_string

# class CustomAccountManager(BaseUserManager):

#     def create_superuser(self, email, user_name, first_name, password, **other_fields):

#         other_fields.setdefault('is_staff', True)
#         other_fields.setdefault('is_superuser', True)
#         other_fields.setdefault('is_active', True)

#         if other_fields.get('is_staff') is not True:
#             raise ValueError(
#                 'Superuser must be assigned to is_staff=True.')
#         if other_fields.get('is_superuser') is not True:
#             raise ValueError(
#                 'Superuser must be assigned to is_superuser=True.')

#         return self.create_user(email, user_name, first_name, password, **other_fields)

#     def create_user(self, email, user_name, first_name, password, **other_fields):

#         if not email:
#             raise ValueError(_('You must provide an email address'))

#         email = self.normalize_email(email)
#         user = self.model(email=email, user_name=user_name,
#                           first_name=first_name, **other_fields)
#         user.set_password(password)
#         user.save()
#         return user


class User(AbstractBaseUser, PermissionsMixin):
    ADMIN = 1
    ORGANIZER = 2
    VISITOR = 3
    SCANNER = 4

    class Meta:
        verbose_name = "user"
        verbose_name_plural = "users"

    ROLE_CHOICES = (
        (ADMIN, "Admin"),
        (ORGANIZER, "Organizer"),
        (VISITOR, "Visitor"),
        (SCANNER, "Scanner"),
    )
    role = models.PositiveSmallIntegerField(choices=ROLE_CHOICES, blank=True, null=True)
    # You can create Role model separately and add ManyToMany if user has more than one role
    avatar = models.FileField(
        ("Image"), upload_to="images/avatar/", null=True, blank=True
    )

    email = models.EmailField(("email address"), unique=True)
    # user_name = models.CharField(max_length=150, unique=True)
    phone = models.CharField(max_length=20)
    first_name = models.CharField(max_length=150)
    last_name = models.CharField(max_length=150)
    start_date = models.DateTimeField(default=timezone.now)
    about = models.TextField(("about"), max_length=500, blank=True)
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(("Is deleted "), default=False)
    token_fb = models.TextField(("Token Firebase"), blank=True, null=True)
    objects = CustomUserManager()

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["first_name", "last_name"]

    def __str__(self):
        return self.email


# model for emails confirmation

# User / TYPE = INSCI/CONFIR = IS_SENT = TRUE - FALSE - DATE - HEURE


class OrganizerCategory(models.Model):
    name = models.CharField(max_length=50)
    slug = models.SlugField(blank=True)
    is_active = models.BooleanField(("Is Active"), default=False)
    image = models.ImageField(
        ("Image"),
        upload_to="images/OrganizerCategory",
        height_field=None,
        width_field=None,
        null=True,
        max_length=None,
    )

    def __str__(self):
        return self.name


class Organizer(models.Model):
    _id = models.AutoField(primary_key=True)
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    # user = models.OneToOneField(User, on_delete=models.SET_NULL, null=True)
    name = models.CharField(max_length=50)
    slug = models.SlugField(blank=True)
    category = models.ForeignKey(
        "OrganizerCategory",
        verbose_name=("Category Organizer"),
        null=True,
        on_delete=models.SET_NULL,
    )
    avatar = models.ImageField(
        null=True,
        blank=True,
        upload_to="images/Organizer/avatar",
        default="/placeholder.png",
    )
    logoTicket = models.ImageField(
        null=True,
        blank=True,
        upload_to="images/Organizer/avatar",
        default="/placeholder.png",
    )
    image = models.ImageField(
        ("Image"),
        upload_to="images/Organizer",
        height_field=None,
        width_field=None,
        null=True,
        max_length=None,
    )
    cover = models.ImageField(
        null=True,
        blank=True,
        upload_to="images/Organizer/cover",
        default="/placeholder.png",
    )
    about = models.TextField(null=True, blank=True)
    createdAt = models.DateTimeField(auto_now_add=True)
    isActive = models.BooleanField(("Is Active"), default=False)
    feesPercent = models.FloatField(
        ("Percent fees % (10 % by default)"), default=10, null=True, blank=True
    )
    show_mail = models.BooleanField("Show In Mail", default=False)
    # date
    organizerDateFrom = models.DateTimeField(auto_now_add=False, null=True, blank=True)
    organizerDateTo = models.DateTimeField(auto_now_add=False, null=True, blank=True)
    #
    is_daily = models.BooleanField("Is Daily", default=False)
    max_date = models.DateField("Max Date", null=True, blank=True)
    isticket_customization = models.BooleanField("Ticket Customization", default=False)
    is_see = models.BooleanField("See On Home", default=True)
    as_guest = models.BooleanField("Continue As Guest", default=False)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # if self.slug is None:
        slugtest = slugify(self.name)
        checkorganizer = (
            Organizer.objects.filter(slug=slugtest).exclude(_id=self._id).first()
        )
        if checkorganizer:
            self.slug = slugify(f"{self.name}-{int(time.time())}")
        else:
            self.slug = slugify(self.name)
        super(Organizer, self).save(*args, **kwargs)

    @property
    def isEnded(self):
        if self.organizerDateFrom:

            delta = self.organizerDateFrom - timezone.now()

            days = delta.days
            seconds = delta.seconds / 60 / 60
            # print("days",days)
            # print("seconds",seconds)
            if days < 0:
                return True
            return False
        return False

    @property
    def organizer_date(self):
        return (
            None
            if self.is_daily
            else (
                str(self.organizerDateFrom.strftime("%m-%d-%Y"))
                + " à "
                + str(str(self.organizerDateFrom.strftime("%H:%M")))
            )
        )


class Section(models.Model):
    _id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50)
    slug = models.SlugField(blank=True)
    organizer = models.ForeignKey(Organizer, on_delete=models.SET_NULL, null=True)
    image = models.ImageField(upload_to="images/sections")

    class Meta:
        verbose_name = "Section"
        verbose_name_plural = "Sections"

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        self.slug = slugify(self.name)
        super(Section, self).save(*args, **kwargs)


class Product(models.Model):
    _id = models.AutoField(primary_key=True)
    section = models.ForeignKey(
        "section",
        verbose_name=("Section"),
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    # slug = models.SlugField(blank=True,unique=True,)
    slug = models.SlugField(blank=True, unique=True, max_length=200)
    organizer = models.ForeignKey(Organizer, on_delete=models.SET_NULL, null=True)
    name = models.CharField(max_length=250)
    image = models.ImageField(null=True, blank=True, default="/placeholder.png")
    isImmediately = models.BooleanField(("is Immediately"), default=True)
    eventDate = models.DateTimeField(auto_now_add=False, null=True, blank=True)
    hasNoEndDate = models.BooleanField(("Has No End Date"), default=False)
    endDate = models.DateTimeField(auto_now_add=False, null=True, blank=True)
    product_date = models.DateField("Product Date", auto_now=False, auto_now_add=False, null=True, blank=True)

    description = models.TextField()
    price = models.DecimalField(max_digits=7, decimal_places=0)
    isActive = models.BooleanField(("Is Active"), default=False)
    countInStock = models.IntegerField(default=0)
    createdAt = models.DateTimeField(auto_now_add=True)
    hasComment = models.BooleanField(("Has Comment"), default=False)
    hasVariation = models.BooleanField(("Has Vaordriation"), default=False)

    isDeleted = models.BooleanField(("Is Deleted"), default=False)

    hasVariation = models.BooleanField(("Has Variation"), default=False)
    variation = models.ForeignKey(
        "Variation",
        verbose_name=("variation"),
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )

    # coupon = models.ForeignKey("base.Coupon", verbose_name=("Coupon "),blank=True, on_delete=models.CASCADE,null=True)

    isCustomized = models.BooleanField(("Is Customized"), default=False)
    customizationTitle = models.CharField(
        ("Customization title"), max_length=100, null=True, blank=True
    )
    has_date = models.BooleanField(("Has Date"), default=False)
    event_date = models.DateField(
        "Event Date", auto_now_add=False, null=True, blank=True
    )
    qte_tickets = models.IntegerField("Maximum ticket quantity", default=5)
    index_order = models.IntegerField("Index Order", default=0);

    def __str__(self):
        return self.name

    @property
    def has_active_discount(self):
        return self.coupon.is_valid

    def clean(self):
        # Don't allow draft entries to have a pub_date.
        if self.hasVariation and not self.variation:
            raise ValidationError("Variation is required !")
        # Set the pub_date for published items if it hasn't been set already.

    # def save(self, *args, **kwargs):
    #     self.slug = slugify(self.name)
    #     super(Product, self).save(*args, **kwargs)

    def save(self, *args, **kwargs):
        if not self.pk:
            self.slug = slugify(self.name + "-" + str(randint(100000, 999999)))

        super().save(*args, **kwargs)


class Images(models.Model):
    _id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(
        max_length=200,
        null=True,
        blank=True,
    )
    product = models.ForeignKey(Product, on_delete=models.SET_NULL, null=True)
    orderImage = models.IntegerField(default=0, null=True, blank=True)
    image = models.ImageField(null=True, blank=True, default="/placeholder.png")

    def __str__(self):
        return self.name

    def image_tag(self):
        if self.image.url is not None:
            return mark_safe('<img src="{}" height="50"/>'.format(self.image.url))
        else:
            return ""


class Variation(models.Model):
    _id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=50)  # size / color /
    attributes = models.ManyToManyField(
        "VariationAttribute", verbose_name=("attributes")
    )
    isPriceVary = models.BooleanField(("Is Price Vary"), default=False)

    def __str__(self):
        return self.name


class VariationAttribute(models.Model):
    name = models.CharField(max_length=50)  # M / L / N
    price = models.DecimalField(max_digits=7, decimal_places=2, null=True, blank=True)

    def __str__(self):
        return "(" + self.name + "," + str(self.price) + ")"


# def order_number():
#     invid = Order.objects.aggregate(max_inv=Max('orderNo') )['max_inv']
#     if invid is not None:
#         return int(invid) + 14
#     return 1


class Order(models.Model):
    _id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,blank=True)
    paymentMethod = models.CharField(max_length=200, null=True, blank=True)
    taxPrice = models.DecimalField(
        max_digits=7, decimal_places=2, null=True, blank=True
    )
    totalPrice = models.DecimalField(
        max_digits=7, decimal_places=2, null=True, blank=True
    )
    isPaid = models.BooleanField(default=False)
    paidAt = models.DateTimeField(auto_now_add=False, null=True, blank=True)
    createdAt = models.DateTimeField(auto_now_add=True)

    isOffline = models.BooleanField(default=False)  # by default : True
    isFree = models.BooleanField(default=False)
    cmiEmail = models.EmailField(("Email CMI"), max_length=254, null=True, blank=True)
    cmiTransId = models.CharField(
        ("Cmi Trans ID"), max_length=50, null=True, blank=True
    )
    cmiStatus = models.BooleanField(("Cmi Status"), null=True, blank=True)
    cmiUpdatedTime = models.TimeField(
        ("Updated time "), auto_now=False, auto_now_add=False, null=True, blank=True
    )

    # discount
    hasDiscount = models.BooleanField(("Has Discount"), default=False)
    code = models.CharField(max_length=50, null=True, blank=True)
    discount = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(75)], default=0
    )
    couponId = models.IntegerField(("Coupon ID"), null=True, blank=True)

    isDeleted = models.BooleanField(("Is Deleted"), default=False)
    guest_email = models.EmailField("Gust Email", null=True, blank=True)
    guest_name = models.CharField("Guest Name",max_length=255, null=True, blank=True)
    guest_phone = models.CharField("Guest Phone",max_length=255, null=True, blank=True)
    as_guest = models.BooleanField("Is Guest Order", default=False)

    # @property
    # def orderNumber(self):
    #     return "NO"+str(self.orderNo.zfill(3) )

    @property
    def created_at(self):
        return self.createdAt.strftime("%m-%d-%Y, %H:%M")

    @property
    def paid_at(self):
        return self.paidAt.strftime("%m-%d-%Y, %H:%M")

    # def __str__(self):
    #         return str(self._id)


class OrderItem(models.Model):
    _id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(Product, on_delete=models.SET_NULL, null=True)
    order = models.ForeignKey(Order, on_delete=models.SET_NULL, null=True)
    organizer = models.ForeignKey(
        Organizer, verbose_name=("Organizer"), on_delete=models.CASCADE, null=True
    )
    name = models.CharField(max_length=200, null=True, blank=True)
    qty = models.IntegerField(null=True, blank=True, default=0)
    price = models.DecimalField(max_digits=7, decimal_places=2, null=True, blank=True)
    image = models.CharField(max_length=500, null=True, blank=True)
    variationName = models.CharField(max_length=50, null=True, blank=True)
    variationValue = models.CharField(max_length=50, null=True, blank=True)
    variationAttributeId = models.ForeignKey(
        "base.VariationAttribute",
        verbose_name=("Variation Attribute Id"),
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    comment = models.CharField(max_length=30, null=True, blank=True)
    customization = models.TextField(null=True, blank=True)
    is_daily = models.BooleanField(("Is daily"), default=False)
    has_date = models.BooleanField(("Has Date"), default=False)
    event_date = models.DateField(
        "Event Date", auto_now_add=False, null=True, blank=True
    )
    product_date = models.DateField(
        "Product Date", auto_now_add=False, null=True, blank=True
    )
    list_ticket_name = models.TextField("Ticket Names", blank=True, null=True)
    isticket_customization = models.BooleanField("Ticket Customization", default=False)

    def __str__(self):
        return str(self.name)


class Ticket(models.Model):
    orderItem = models.ForeignKey(
        "base.OrderItem", verbose_name=("Order Item"), on_delete=models.CASCADE
    )
    ticketNo = models.CharField(("Ticket No"), max_length=50, unique=True)
    organizerPhoto = models.CharField(
        ("Organizer Photo"), max_length=400, null=True, blank=True
    )
    organizerLogo = models.CharField(
        ("Organizer Logo"), max_length=400, null=True, blank=True
    )
    organizerName = models.CharField(
        ("Organizer Name"), max_length=200, null=True, blank=True
    )
    organizerDate = models.CharField(
        ("Organizer Date"), max_length=100, null=True, blank=True
    )
    productName = models.CharField(
        ("Product Name"), max_length=200, null=True, blank=True
    )
    firstName = models.CharField(("First Name"), max_length=100, null=True, blank=True)
    lastName = models.CharField(("Last Name"), max_length=100, null=True, blank=True)

    variationName = models.CharField(
        ("Variation Name"), max_length=100, null=True, blank=True
    )
    variationValue = models.CharField(
        ("Variation Value"), max_length=100, null=True, blank=True
    )
    comment = models.CharField(("Comment"), max_length=300, null=True, blank=True)
    customization = models.CharField(("Comment"), max_length=300, null=True, blank=True)

    orderNo = models.CharField(("Order No"), max_length=100, null=True, blank=True)
    typeOrder = models.CharField(("Type Order"), max_length=50, null=True, blank=True)
    price = models.FloatField(("Price"), null=True, blank=True)
    # paidAt = models.CharField(("Paid At"), max_length=50,null=True,blank=True)
    paidAt = models.DateTimeField(auto_now_add=False, null=True, blank=True)
    isScanned = models.BooleanField(default=False)
    scannedAt = models.DateTimeField(auto_now_add=False, null=True, blank=True)
    # add 26/2/25
    event_date = models.DateField(("Event Date"), null=True, blank=True)
    product_date = models.DateField(("Product Date"), null=True, blank=True)
    is_daily = models.BooleanField(("Is daily"), default=False)
    has_date = models.BooleanField(("Has Date"), default=False)

    transfered = models.BooleanField(("Ticket Transfered !"), default=False)
    isticket_customization = models.BooleanField("Ticket Customization", default=False)
    ticket_name = models.CharField("Ticket Name", max_length=500, blank=True, null=True)

    def save(self, *args, **kwargs):
        if self.pk is None:
            self.ticketNo = get_string(6, 6)
        super(Ticket, self).save(*args, **kwargs)


# class QRcode

# class ShippingAddress(models.Model):
#     _id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
#     order = models.OneToOneField(Order, on_delete=models.CASCADE, null=True, blank=True)
#     firstName = models.CharField(max_length=200, null=True, blank=True)
#     lastName = models.CharField(max_length=200, null=True, blank=True)
#     phone = models.CharField(max_length=200, null=True, blank=True)
#     email = models.CharField(max_length=200, null=True, blank=True)
#     isUnder18 =  models.BooleanField(default=False)
#     isTermsChecked =  models.BooleanField(default=False)
#     passportCin = models.CharField(max_length=200, null=True, blank=True)


#     def __str__(self):
#         return str(self.firstName)


class InfoClient(models.Model):
    order = models.OneToOneField(Order, on_delete=models.CASCADE, null=True, blank=True)
    firstName = models.CharField(max_length=200, null=True, blank=True)
    lastName = models.CharField(max_length=200, null=True, blank=True)
    phone = models.CharField(max_length=200, null=True, blank=True)
    email = models.CharField(max_length=200, null=True, blank=True)
    # isUnder18 =  models.BooleanField(default=False)
    # isTermsChecked =  models.BooleanField(default=False)
    # passportCin = models.CharField(max_length=200, null=True, blank=True)

    def __str__(self):
        return str(self.firstName)


# coupon code


class Coupon(models.Model):
    organizer = models.ForeignKey(Organizer, on_delete=models.SET_NULL, null=True)
    code = models.CharField(max_length=50, unique=True)
    valid_from = models.DateTimeField()
    valid_to = models.DateTimeField()
    discount = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(75)]
    )
    active = models.BooleanField(default=False)
    product = models.ForeignKey(
        "base.Product",
        verbose_name=("Product with Discount"),
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    used = models.IntegerField(("Used "), default=0)
    has_max = models.BooleanField(
        "Has a max",
        default=False,
    )
    max_count = models.IntegerField("Max Count", default=0)
    # is_all = models.BooleanField(default=False)

    def __str__(self):
        return self.code + " - " + str(self.organizer.pk)

    @property
    def duration(self):
        dt = self.valid_to - timezone.now()
        return dt.days

    @property
    def is_valid(self):
        dt = self.valid_to - timezone.now()
        return self.active and dt.days >= 0


class ExpiredOrganizer(models.Model):
    name = models.CharField(max_length=200, null=True, blank=True)
    date = models.DateField(("Date Event"), auto_now=False, auto_now_add=False)
    image = models.ImageField(
        ("Image "), upload_to=None, height_field=None, width_field=None, max_length=None
    )

    def __str__(self):
        return str(self.name)


class EmailStatus(models.Model):

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    status = models.CharField(("Status "), max_length=30, null=True)
    sent_count = models.IntegerField(("Sent count"), default=0, null=True)

    def __str__(self):
        return self.user.email


#  donation


class Donation(models.Model):
    _id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    organizer = models.ForeignKey(
        Organizer, verbose_name=("Organizer"), on_delete=models.CASCADE, null=True
    )
    emailDon = models.EmailField(("Email Don"), max_length=254, null=True, blank=True)
    phoneDon = models.CharField(max_length=20, null=True)
    fullName = models.CharField(max_length=80, null=True)
    totalDon = models.DecimalField(
        max_digits=7, decimal_places=2, null=True, blank=True
    )
    isPaid = models.BooleanField(("Is Paid"), default=False)

    createdAt = models.DateTimeField(auto_now_add=True, null=True)

    class Meta:
        verbose_name = "donation"
        verbose_name_plural = "donations"


class TransferOrder(models.Model):
    # Status choices
    STATUS_CHOICES = [
        ("IN_PROGRESS", "In Progress"),
        ("COMPLETED", "Completed"),
        ("FAILED", "Failed"),
    ]
    from_user = models.ForeignKey(
        User, related_name="FROM_USER", on_delete=models.SET_NULL, null=True
    )
    to_user = models.ForeignKey(
        User, related_name="TO_USER", on_delete=models.SET_NULL, null=True
    )
    ticketNo = models.CharField(("Ticket No"), max_length=50)
    orderNo = models.CharField(("Order No"), max_length=100, null=True, blank=True)
    ticket = models.ForeignKey(
        "base.Ticket", verbose_name=("Ticket"), on_delete=models.CASCADE
    )
    status = models.CharField(
        max_length=12,
        choices=STATUS_CHOICES,
        default="IN_PROGRESS",
        verbose_name="Ticket Status",
    )
    count = models.IntegerField(("No of Times Transfered "), default=0)

    isActive = models.BooleanField(("Is Active !"), default=False)
    createdAt = models.DateTimeField(auto_now_add=True, null=True)

    class Meta:
        verbose_name = "transferorder"
        verbose_name_plural = "transferorders"

    # def __str__(self):
    #     return self.name

    # def get_absolute_url(self):
    #     return reverse("transferorder_detail", kwargs={"pk": self.pk})


# notification model
class Notification(models.Model):
    STATUS_CHOICES = (
        (1, "Notification"),
        (2, "Transfer"),
        (3, "Order"),
    )
    user = models.ForeignKey(
        User, related_name="NOTIF_USER", on_delete=models.SET_NULL, null=True
    )
    type_notif = models.IntegerField("Type Notif", choices=STATUS_CHOICES, default=1)
    order = models.CharField(("Order No"), max_length=100, null=True, blank=True)
    title = models.CharField(
        "Title Notification", max_length=250, null=True, blank=True
    )
    body = models.TextField("Body notif", null=True, blank=True)
    icon = models.TextField("Icon notif", null=True, blank=True)
    is_vu = models.BooleanField("Is Vu", default=False)
    is_admin = models.BooleanField("Is Admin", default=False)
    created_at = models.DateTimeField(verbose_name="Create Date", auto_now_add=True)
    updated_at = models.DateTimeField(verbose_name="Date Modified", auto_now=True)

    class Meta:
        verbose_name = "Notification"
        verbose_name_plural = "Notifications"
