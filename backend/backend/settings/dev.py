from pathlib import Path
import os

BASE_DIR = Path(__file__).resolve().parent.parent.parent

DEBUG = True

ALLOWED_HOSTS = [
    "0.0.0.0",
    "127.0.0.1",
    "localhost",
    "***************",
    "***************",
    "***************",
]

# CORS_ALLOWED_ORIGINS = [
#     'http://127.0.0.1:3000',
#     'http://localhost:3000'
# ]

CORS_ORIGIN_ALLOW_ALL = True

# CORS_ORIGIN_WHITELIST = [
#      'http://localhost:3000',
#      'http://127.0.0.1:3000'
# ]


DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db3.sqlite3",
    }
}


# DATABASES = {
#      'default': {
#          'ENGINE': 'django.db.backends.postgresql_psycopg2',
#          'NAME': os.environ['DB_NAME'],
#          'USER':os.environ['DB_USER'],
#          'PASSWORD': os.environ['DB_PASSWORD'],
#          'HOST': os.environ['DB_HOST'],
#          'PORT': os.environ['DB_PORT']
#      }
#  }
