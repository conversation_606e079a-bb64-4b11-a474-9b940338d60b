from pathlib import Path
import os

# from dotenv import load_dotenv
# load_dotenv()

BASE_DIR = Path(__file__).resolve().parent.parent.parent

DEBUG = False

ALLOWED_HOSTS = [
    "127.0.0.1",
    "localhost",
    "aylink.ma",
    "www.aylink.ma",
    "admin.aylink.ma",
    "www.admin.aylink.ma",
    "sup-admin.aylink.ma",
    "www.sup-admin.aylink.ma",
]

CORS_ALLOWED_ORIGINS = [
    "https://www.aylink.ma",
    "https://admin.aylink.ma",
    "https://www.admin.aylink.ma",
    "https://aylink.ma",
    "https://payment.aylink.ma",
    "https://www.payment.aylink.ma",
    "http://localhost:3000",
    "https://sup-admin.aylink.ma",
    "https://www.sup-admin.aylink.ma",
]

CORS_ORIGIN_WHITELIST = [
    "https://www.aylink.ma",
    "https://admin.aylink.ma",
    "https://www.admin.aylink.ma",
    "https://aylink.ma",
    "https://payment.aylink.ma",
    "https://www.payment.aylink.ma",
    "http://localhost:3000",
    "https://sup-admin.aylink.ma",
    "https://www.sup-admin.aylink.ma",
]

CSRF_TRUSTED_ORIGINS = [
    "https://www.aylink.ma",
    "https://admin.aylink.ma",
    "https://www.admin.aylink.ma",
    "https://aylink.ma",
    "http://localhost:3000",
    "https://sup-admin.aylink.ma",
    "https://www.sup-admin.aylink.ma",
]

# CSRF_TRUSTED_ORIGINS=['https://www.aylink.ma','https://aylink.ma','https://admin.aylink.ma','https://www.admin.aylink.ma']

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": os.environ["DB_NAME"],
        "USER": os.environ["DB_USER"],
        "PASSWORD": os.environ["DB_PASSWORD"],
        "HOST": os.environ["DB_HOST"],
        "PORT": os.environ["DB_PORT"],
    }
}


AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "JRhdETgxCQ7vY7aUZmSuyl2AFnsQiUBveDWpV8ll"
AWS_STORAGE_BUCKET_NAME = "epiceventsstatic"
AWS_S3_FILE_OVERWRITE = False
DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"
STATICFILES_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"
AWS_QUERYSTRING_AUTH = False
AWS_DEFAULT_ACL = "public-read"
# AWS_S3_ENDPOINT_URL: 'https://sts.ap-south-1.amazonaws.com'
AWS_S3_REGION_NAME = "eu-west-3"
