"""
    backend URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView


urlpatterns = [
    path("dj-admin/", admin.site.urls),
    path("api/products/", include("base.urls.product_urls")),
    path("api/users/", include("base.urls.user_urls")),
    path("api/orders/", include("base.urls.order_urls")),
    path("api/orders-admin/", include("base.urls.order_admin_urls")),
    path("api/sections/", include("base.urls.section_urls")),
    path("api/organizers/", include("base.urls.organizer_urls")),
    path("api/coupons/", include("base.urls.coupon_urls")),
    path("api/tickets/", include("base.urls.ticket_urls")),
    path("api/transfers/", include("base.urls.transfer_urls")),
    path("api/stats/", include("base.urls.stats_urls")),
    path("api/check/", include("base.urls.check_urls")),
    path("api/donation/", include("base.urls.donation_urls")),
    path("api/stats-admin/", include("base.urls.stats_admin_urls")),
    path("api/categories/", include("base.urls.category_urls")),
    path("api/notifications/", include("base.urls.notification_urls")),
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
